<template>
    <abc-dialog
        v-if="showDialog"
        ref="dialogRef"
        v-model="showDialog"
        v-bind="dialogProps"
        @open="onDialogOpen"
        @close="onDialogClose"
    >
        <abc-flex v-abc-loading="loading" vertical :gap="16">
            <template v-if="orderType === 'goodsIn'">
                <abc-tips-card-v2 v-if="isAdd" theme="warning">
                    {{
                        isFixed ? '请在正确入库信息处修改' : `${tooltips}，请谨慎填写`
                    }}
                </abc-tips-card-v2>
                <template v-else>
                    <abc-tips-card-v2 v-if="comment" theme="primary">
                        {{ comment }}
                    </abc-tips-card-v2>
                    <abc-tips-card-v2 v-else theme="primary">
                        <!--张仲景门店发起的修正单会进行审核-->
                        {{ canReview ? '修正单总部审核中' : '已根据正确入库信息生成采购入库修正单' }}
                    </abc-tips-card-v2>
                </template>
            </template>

            <abc-descriptions
                v-if="!isAdd"
                :column="3"
                :label-width="100"
                grid
                disabled
                size="large"
            >
                <template #title>
                    <abc-flex :gap="12" align="baseline" style="font-weight: normal;">
                        <abc-text theme="black" bold>
                            {{ goodsInfo?.displayName ?? '' }}
                        </abc-text>
                        <abc-text theme="gray" size="mini">
                            {{ goodsInfo?.displaySpec ?? '' }}
                            <span style="padding-left: 4px;">{{ goodsInfo?.manufacturer }}</span>
                        </abc-text>
                    </abc-flex>
                </template>

                <template v-if="multiPharmacyCanUse">
                    <abc-descriptions-item label="入库库房">
                        {{ inPharmacyName }}
                    </abc-descriptions-item>
                    <abc-descriptions-item v-if="showOutPharmacy" label="出库库房">
                        {{ outPharmacyName }}
                    </abc-descriptions-item>
                </template>

                <template v-if="showCostPrice">
                    <abc-descriptions-item label="修正前进价">
                        {{ getPackageCostPrice(orderInfo?.diffView?.beforePackageCostPrice, '', goodsInfo) }}
                    </abc-descriptions-item>
                    <abc-descriptions-item label="修正后进价">
                        {{ getPackageCostPrice(orderInfo?.diffView?.afterPackageCostPrice,'',goodsInfo) }}
                    </abc-descriptions-item>
                </template>

                <abc-descriptions-item label="供应商">
                    {{ orderInfo?.diffView?.supplierName || '-' }}
                </abc-descriptions-item>

                <!--修正单不能修改，最后修改时间就是创建时间-->
                <abc-descriptions-item label="修正时间">
                    {{ (orderInfo?.lastModified || orderInfo?.lastModifiedDate || orderInfo?.createdDate) | parseTime }}
                </abc-descriptions-item>
                <abc-descriptions-item label="修正人">
                    {{ orderInfo?.lastModifiedBy?.name || orderInfo?.createdUser?.name || '' }}
                </abc-descriptions-item>
            </abc-descriptions>

            <abc-form ref="formRef" is-excel>
                <!--采购入库修正单-->
                <abc-table
                    v-if="orderType === CorrectOrderTypeEnum.GoodsIn"
                    :render-config="tableConfig"
                    :data-list="tableData"
                    size="large"
                    :show-hover-tr-bg="false"
                >
                    <template v-if="isAdd && goodsInfo" #topHeader>
                        <abc-flex style="width: 100%; padding: 4px 8px;" align="center" :gap="12">
                            <abc-text theme="black" size="large" bold>
                                {{ goodsInfo.displayName }}
                            </abc-text>
                            <abc-text theme="gray">
                                {{ goodsInfo.displaySpec }}
                                <span style="padding-left: 4px;">{{ goodsInfo.manufacturer }}</span>
                            </abc-text>
                            <abc-text v-if="supplierName" theme="gray" style="margin-left: auto;">
                                供应商：{{ supplierName }}
                            </abc-text>
                        </abc-flex>
                    </template>

                    <template v-if="orderInfo" #footer>
                        <abc-flex style="margin-left: 12px;">
                            <abc-text v-if="orderInfo.diffView?.displayGoodsCount" theme="gray">
                                数量修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView.displayGoodsCount }}
                                </abc-text>；
                            </abc-text>
                            <abc-text theme="gray">
                                含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCost | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <abc-text v-if="orderInfo.diffView?.totalCostE" theme="gray">
                                ；不含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCostE | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <!--<abc-text theme="black">-->
                            <!--    正确进价：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView?.packageCostPrice | formatMoney(false) }}-->
                            <!--    </abc-text>-->
                            <!--</abc-text>-->
                        </abc-flex>
                    </template>

                    <template v-else #footer>
                        <abc-flex v-if="diffData.displayCount || diffData.amount" style="margin-left: 12px;">
                            <abc-text v-if="diffData.displayCount" theme="gray">
                                数量修正:<abc-text theme="black" bold>
                                    {{ diffData.displayCount }}
                                </abc-text>；
                            </abc-text>
                            <abc-text theme="gray">
                                金额修正:<abc-text theme="black" bold>
                                    {{ diffData.amount | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <!--<abc-text theme="black">-->
                            <!--    正确进价：<abc-text bold>-->
                            <!--        {{ diffData.costPrice | formatMoney(false) }}-->
                            <!--    </abc-text>-->
                            <!--</abc-text>-->
                        </abc-flex>
                    </template>

                    <template #name="{ trData: row }">
                        <abc-table-cell>
                            <abc-flex v-if="row._type === ItemDataType.original" vertical>
                                <abc-text theme="black" size="normal">
                                    {{ row.name }}
                                </abc-text>
                                <abc-link
                                    theme="primary"
                                    size="small"

                                    @click="handleOpenOriginalOrder(row)"
                                >
                                    {{ row.orderNo ?? '' }}
                                </abc-link>
                            </abc-flex>
                            <abc-text v-else :theme="row._type === ItemDataType.diff ? 'warning-light' : 'black'">
                                {{ row.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #count="{ trData: row }">
                        <template v-if="isEditCount(row._type)">
                            <stock-unit-or-input
                                :key="customTrKey(row)"
                                :value="row"
                                :type="'count'"
                                :immediate-change="!isStrictCountWithTraceCodeCollect"
                                :use-unit.sync="row.useUnit"
                                :use-count.sync="row.useCount"
                                :validate-fn="validateNull"
                                :support-zero="true"
                                :input-custom-style="{
                                    textAlign: 'right',
                                }"
                                @change="handleChangeUnitCount(row)"
                            >
                            </stock-unit-or-input>
                        </template>
                        <div v-else-if="row._type === ItemDataType.diff" class="cover-base-border cover-right-border"></div>
                        <abc-table-cell v-else>
                            <abc-tooltip :content="tooltips" placement="top" :disabled="disabledTooltips || row._type === ItemDataType.original">
                                <abc-text :theme="'black'">
                                    {{ row.useCount || '' }}
                                </abc-text>
                            </abc-tooltip>
                        </abc-table-cell>
                    </template>

                    <template #unit="{ trData: row }">
                        <template v-if="isEditPrice(row._type)">
                            <stock-unit-or-input
                                :key="customTrKey(row)"
                                :value="row"
                                :type="'unit'"
                                :immediate-change="false"
                                :use-unit.sync="row.useUnit"
                                :use-count.sync="row.useCount"
                                :validate-fn="validateNull"
                                option-center
                                :select-style="{ textAlign: 'center' }"
                                @change="handleChangeUnitCount(row)"
                            >
                            </stock-unit-or-input>
                        </template>
                        <abc-table-cell v-else>
                            <abc-tooltip :content="tooltips" placement="top" :disabled="disabledTooltips || row._type === ItemDataType.original">
                                <abc-text :theme="row._type === ItemDataType.diff ? 'warning-light' : 'black'">
                                    {{ row._type === ItemDataType.diff ? row.displayGoodsCount : row.useUnit || '' }}
                                </abc-text>
                            </abc-tooltip>
                        </abc-table-cell>
                    </template>

                    <!--进价-->
                    <template
                        #packageCostPrice="{
                            trData: row
                        }"
                    >
                        <!--中西药 进价均支持 5 位小数输入-->
                        <abc-form-item
                            v-if="isEditPrice(row._type)"
                            :validate-event="(_, callback)=>validateCostPrice(callback, row)"
                        >
                            <abc-tooltip :disabled="!validatePrice(row)" :content="validatePrice(row)">
                                <abc-input
                                    v-model="row.useUnitCostPrice"
                                    v-abc-focus-selected
                                    type="money"
                                    :class="{ 'tool-tip-price': validatePrice(row) }"
                                    :input-custom-style="{
                                        padding: '0 6px', textAlign: 'right'
                                    }"
                                    :config="getCostConfig(row)"
                                    @enter="enterEvent"
                                    @input="getCostPriceTotal(row)"
                                    @change="packageCostPriceChange(row)"
                                ></abc-input>
                            </abc-tooltip>
                        </abc-form-item>
                        <abc-table-cell v-else class="ellipsis">
                            <abc-tooltip :content="tooltips" placement="top" :disabled="disabledTooltips || row._type === ItemDataType.original">
                                <abc-text
                                    v-abc-title.ellipsis="getGoodsPriceStr(row.goods, row.useUnitCostPrice)"
                                    :theme="row._type === ItemDataType.diff ? 'warning-light' : 'black'"
                                >
                                </abc-text>
                            </abc-tooltip>
                        </abc-table-cell>
                    </template>
                    <!--金额-->
                    <template
                        #totalPrice="{
                            trData: row,
                        }"
                    >
                        <abc-form-item
                            v-if="isEditPrice(row._type)"
                            ref="TotalCostPrice"
                            :validate-event="(val, callback)=>validateTotalPrice(val, row, callback)"
                        >
                            <abc-input
                                v-model="row.useTotalCostPrice"
                                v-abc-focus-selected
                                type="money"
                                :input-custom-style="{
                                    padding: '0 6px', 'text-align': 'right',
                                }"
                                :config="{
                                    formatLength: 2, max: 10000000, supportZero: true
                                }"
                                @enter="enterEvent"
                                @input="getCostPriceSingle(row)"
                                @change="totalCostPriceChange(row)"
                            ></abc-input>
                        </abc-form-item>
                        <abc-table-cell
                            v-else
                            class="ellipsis"
                        >
                            <abc-tooltip :content="tooltips" placement="top" :disabled="disabledTooltips || row._type === ItemDataType.original">
                                <abc-text
                                    :theme="row._type === ItemDataType.diff ? 'warning-light' : 'black'"
                                    :title="(row.useTotalCostPrice) | formatMoney(false)"
                                >
                                    {{ (row.useTotalCostPrice) | formatMoney(false) }}
                                </abc-text>
                            </abc-tooltip>
                        </abc-table-cell>
                    </template>

                    <!--批号-->
                    <template
                        #batchNo="{
                            trData: row
                        }"
                    >
                        <abc-form-item
                            v-if="row._type === ItemDataType.current && isAdd"
                            :validate-event="validateBatchNo"
                        >
                            <abc-input
                                v-model="row.batchNo"
                                v-abc-focus-selected
                                :max-length="20"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <div v-else-if="row._type === ItemDataType.diff" class="cover-base-border cover-right-border"></div>
                        <abc-table-cell
                            v-else
                            class="ellipsis"
                        >
                            <abc-text
                                v-abc-title.ellipsis="row.batchNo || ''"
                                :theme="row._type === ItemDataType.diff ? 'warning-light' : 'black'"
                            >
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <!--生产日期-->
                    <template
                        #productionDate="{
                            trData: row
                        }"
                    >
                        <abc-form-item
                            v-if="row._type === ItemDataType.current && isAdd"
                            :validate-event="validateDate"
                        >
                            <abc-tooltip :disabled="!getProductDateTooltip(row)" content="生产日期不可选择未来时间">
                                <abc-date-picker
                                    v-model="row.productionDate"
                                    type="datequick"
                                    :show-icon="false"
                                    :width="99"
                                    :class="{ 'tool-tip-price': getProductDateTooltip(row) }"
                                    :prevent-direction-navigation="false"
                                    editable
                                    placeholder=""
                                    @enter="enterEvent"
                                >
                                </abc-date-picker>
                            </abc-tooltip>
                        </abc-form-item>
                        <div v-else-if="row._type === ItemDataType.diff" class="cover-base-border cover-right-border"></div>
                        <abc-table-cell
                            v-else
                            class="ellipsis"
                            :title="row.productionDate"
                        >
                            {{ row.productionDate }}
                        </abc-table-cell>
                    </template>

                    <!--效期-->
                    <template
                        #expiryDate="{
                            trData: row
                        }"
                    >
                        <abc-form-item
                            v-if="row._type === ItemDataType.current && isAdd"
                            :validate-event="validateDate"
                        >
                            <abc-tooltip :disabled="!getExpiryDateTooltip(row)" :content="getExpiryDateTooltip(row)">
                                <abc-date-picker
                                    v-model="row.expiryDate"
                                    type="datequick"
                                    :show-icon="false"
                                    :width="99"
                                    :class="{ 'tool-tip-price': getExpiryDateTooltip(row) }"
                                    :prevent-direction-navigation="false"
                                    editable
                                    placeholder=""
                                    @enter="enterEvent"
                                >
                                </abc-date-picker>
                            </abc-tooltip>
                        </abc-form-item>
                        <div v-else-if="row._type === ItemDataType.diff" class="cover-base-border cover-right-border"></div>
                        <abc-table-cell
                            v-else
                            class="ellipsis"
                            :title="row.expiryDate"
                        >
                            {{ row.expiryDate }}
                        </abc-table-cell>
                    </template>

                    <!--追溯码-->
                    <template
                        #traceableCode="{
                            trData: row
                        }"
                    >
                        <traceable-code-cell
                            v-if="row._type === ItemDataType.original"
                            key="view-cell"
                            v-model="row.traceableCodeList"
                            :goods="row.goods"
                            :goods-count="{
                                label: '入库数量',
                                unit: row.useUnit,
                                unitCount: row.useCount || 0
                            }"
                            readonly
                        ></traceable-code-cell>

                        <template v-if="row._type === ItemDataType.current">
                            <template v-if="isAdd">
                                <traceable-code-cell
                                    v-if="needCollect || isFixed"
                                    key="edit-cell"
                                    :ref="`traceableCodeCellRef${row.keyId}`"
                                    v-model="row.traceableCodeList"
                                    :goods="row.goods"
                                    :goods-count="{
                                        label: '入库数量',
                                        unit: row.useUnit,
                                        unitCount: row.useCount || 0,
                                        maxCount: row._maxTraceCodeCount
                                    }"
                                    :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                                ></traceable-code-cell>

                                <abc-table-cell v-else>
                                    <abc-text theme="gray-light">
                                        无需采集
                                    </abc-text>
                                </abc-table-cell>
                            </template>

                            <template v-else>
                                <traceable-code-cell
                                    key="view-cell"
                                    :ref="`traceableCodeCellRef${row.keyId}`"
                                    v-model="row.traceableCodeList"
                                    :goods="row.goods"
                                    :goods-count="{
                                        label: '入库数量',
                                        unit: row.useUnit,
                                        unitCount: row.useCount || 0,
                                        maxCount: row._maxTraceCodeCount
                                    }"
                                    readonly
                                ></traceable-code-cell>
                            </template>
                        </template>
                    </template>
                </abc-table>

                <!--退货出库修正单-->
                <abc-table
                    v-else-if="orderType === CorrectOrderTypeEnum.GoodsReturn"
                    :render-config="tableConfig"
                    :data-list="tableData"
                    size="large"
                    :show-hover-tr-bg="false"
                >
                    <template v-if="orderInfo" #footer>
                        <abc-flex style="margin-left: 12px;">
                            <!--<abc-text v-if="orderInfo.diffView?.displayGoodsCount" theme="black">-->
                            <!--    数量修正：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView.displayGoodsCount }}-->
                            <!--    </abc-text>；-->
                            <!--</abc-text>-->
                            <abc-text theme="gray">
                                含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCost | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <abc-text v-if="orderInfo.diffView?.totalCostE" theme="gray">
                                ；不含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCostE | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <!--<abc-text theme="black">-->
                            <!--    正确进价：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView?.packageCostPrice | formatMoney(false) }}-->
                            <!--    </abc-text>-->
                            <!--</abc-text>-->
                        </abc-flex>
                    </template>

                    <template #name="{ trData: row }">
                        <abc-table-cell>
                            <abc-flex v-if="row._type === ItemDataType.original" vertical>
                                <abc-text theme="black" size="normal">
                                    {{ row.name }}
                                </abc-text>
                                <abc-link
                                    theme="primary"
                                    size="small"

                                    @click="handleOpenOriginalOrder(row)"
                                >
                                    {{ row.orderNo ?? '' }}
                                </abc-link>
                            </abc-flex>
                            <abc-text v-else>
                                {{ row.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #type="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row._type === ItemDataType.original ? '退货出库' : '退货出库-修正' }}</span>
                        </abc-table-cell>
                    </template>

                    <template #packageCostPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ `${moneyDigit(row.useUnitCostPrice, 5)}/${row.useUnit}` }}</span>
                        </abc-table-cell>
                    </template>

                    <template #count="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.displayGoodsCount }}</span>
                        </abc-table-cell>
                    </template>

                    <template #totalPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.useTotalCostPrice | formatMoney(false) }}</span>
                        </abc-table-cell>
                    </template>
                </abc-table>

                <!--盘点修正单-->
                <abc-table
                    v-else-if="orderType === CorrectOrderTypeEnum.GoodsCheck"
                    :render-config="tableConfig"
                    :data-list="tableData"
                    size="large"
                    :show-hover-tr-bg="false"
                >
                    <template v-if="orderInfo" #footer>
                        <abc-flex style="margin-left: 12px;">
                            <!--<abc-text v-if="orderInfo.diffView?.displayGoodsCount" theme="black">-->
                            <!--    数量修正：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView.displayGoodsCount }}-->
                            <!--    </abc-text>；-->
                            <!--</abc-text>-->
                            <abc-text theme="gray">
                                含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCost | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <abc-text v-if="orderInfo.diffView?.totalCostE" theme="gray">
                                ；不含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCostE | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <!--<abc-text theme="black">-->
                            <!--    正确进价：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView?.packageCostPrice | formatMoney(false) }}-->
                            <!--    </abc-text>-->
                            <!--</abc-text>-->
                        </abc-flex>
                    </template>

                    <template #name="{ trData: row }">
                        <abc-table-cell>
                            <abc-flex v-if="row._type === ItemDataType.original" vertical>
                                <abc-text theme="black" size="normal">
                                    {{ row.name }}
                                </abc-text>
                                <abc-link
                                    theme="primary"
                                    size="small"

                                    @click="handleOpenOriginalOrder(row)"
                                >
                                    {{ row.orderNo ?? '' }}
                                </abc-link>
                            </abc-flex>
                            <abc-text v-else>
                                {{ row.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #type="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.typeName || '' }}</span>
                        </abc-table-cell>
                    </template>

                    <template #packageCostPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ getPackageCostPrice(row.packageCostPrice, '', row.goods) }}</span>
                        </abc-table-cell>
                    </template>

                    <template #count="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.displayGoodsCount }}</span>
                        </abc-table-cell>
                    </template>

                    <template #totalPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.totalCostPriceChange | formatMoney(false) }}</span>
                        </abc-table-cell>
                    </template>
                </abc-table>

                <!--领用修正单-->
                <abc-table
                    v-else-if="orderType === CorrectOrderTypeEnum.GoodsApply"
                    :render-config="tableConfig"
                    :data-list="tableData"
                    size="large"
                    :show-hover-tr-bg="false"
                >
                    <template v-if="orderInfo" #footer>
                        <abc-flex style="margin-left: 12px;">
                            <!--<abc-text v-if="orderInfo.diffView?.displayGoodsCount" theme="black">-->
                            <!--    数量修正：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView.displayGoodsCount }}-->
                            <!--    </abc-text>；-->
                            <!--</abc-text>-->
                            <abc-text theme="gray">
                                含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCost | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <abc-text v-if="orderInfo.diffView?.totalCostE" theme="gray">
                                ；不含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCostE | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <!--<abc-text theme="black">-->
                            <!--    正确进价：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView?.packageCostPrice | formatMoney(false) }}-->
                            <!--    </abc-text>-->
                            <!--</abc-text>-->
                        </abc-flex>
                    </template>

                    <template #name="{ trData: row }">
                        <abc-table-cell>
                            <abc-flex v-if="row._type === ItemDataType.original" vertical>
                                <abc-text theme="black" size="normal">
                                    {{ row.name }}
                                </abc-text>
                                <abc-link
                                    theme="primary"
                                    size="small"

                                    @click="handleOpenOriginalOrder(row)"
                                >
                                    {{ row.orderNo ?? '' }}
                                </abc-link>
                            </abc-flex>
                            <abc-text v-else>
                                {{ row.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #type="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ getApplyType(row) }}</span>
                        </abc-table-cell>
                    </template>

                    <template #packageCostPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ getPackageCostPrice(row.packageCostPrice, '', row.goods) }}</span>
                        </abc-table-cell>
                    </template>

                    <template #count="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.displayGoodsCount }}</span>
                        </abc-table-cell>
                    </template>

                    <template #totalPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.totalCost | formatMoney(false) }}</span>
                        </abc-table-cell>
                    </template>
                </abc-table>

                <!--调拨修正单-->
                <abc-table
                    v-else-if="orderType === CorrectOrderTypeEnum.GoodsTrans"
                    :render-config="tableConfig"
                    :data-list="tableData"
                    size="large"
                    :show-hover-tr-bg="false"
                >
                    <template v-if="orderInfo" #footer>
                        <abc-flex style="margin-left: 12px;">
                            <!--<abc-text v-if="orderInfo.diffView?.displayGoodsCount" theme="black">-->
                            <!--    数量修正：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView.displayGoodsCount }}-->
                            <!--    </abc-text>；-->
                            <!--</abc-text>-->
                            <abc-text theme="gray">
                                含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCost | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <abc-text v-if="orderInfo.diffView?.totalCostE" theme="gray">
                                ；不含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCostE | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <!--<abc-text theme="black">-->
                            <!--    正确进价：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView?.packageCostPrice | formatMoney(false) }}-->
                            <!--    </abc-text>-->
                            <!--</abc-text>-->
                        </abc-flex>
                    </template>

                    <template #name="{ trData: row }">
                        <abc-table-cell>
                            <abc-flex v-if="row._type === ItemDataType.original" vertical>
                                <abc-text theme="black" size="normal">
                                    {{ row.name }}
                                </abc-text>
                                <abc-link
                                    theme="primary"
                                    size="small"

                                    @click="handleOpenOriginalOrder(row)"
                                >
                                    {{ row.orderNo ?? '' }}
                                </abc-link>
                            </abc-flex>
                            <abc-text v-else>
                                {{ row.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #packageCostPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ getPackageCostPrice(row.packageCostPrice, '', row.goods) }}</span>
                        </abc-table-cell>
                    </template>

                    <template #count="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.displayGoodsCount }}</span>
                        </abc-table-cell>
                    </template>

                    <template #totalPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.totalCostPrice | formatMoney(false) }}</span>
                        </abc-table-cell>
                    </template>
                </abc-table>

                <!--出库修正单-报损、其他、科室消耗、生产出库-->
                <abc-table
                    v-else-if="orderType === CorrectOrderTypeEnum.GoodsOut"
                    :render-config="tableConfig"
                    :data-list="tableData"
                    size="large"
                    :show-hover-tr-bg="false"
                >
                    <template v-if="orderInfo" #footer>
                        <abc-flex style="margin-left: 12px;">
                            <!--<abc-text v-if="orderInfo.diffView?.displayGoodsCount" theme="black">-->
                            <!--    数量修正：<abc-text bold>-->
                            <!--        {{ orderInfo.diffView.displayGoodsCount }}-->
                            <!--    </abc-text>；-->
                            <!--</abc-text>-->
                            <abc-text theme="gray">
                                含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCost | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <abc-text v-if="orderInfo.diffView?.totalCostE" theme="gray">
                                ；不含税金额修正:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.totalCostE | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                            <abc-text theme="gray">
                                ；正确进价:<abc-text theme="black" bold>
                                    {{ orderInfo.diffView?.packageCostPrice | formatMoney(false) }}
                                </abc-text>
                            </abc-text>
                        </abc-flex>
                    </template>

                    <template #name="{ trData: row }">
                        <abc-table-cell>
                            <abc-flex v-if="row._type === ItemDataType.original" vertical>
                                <abc-text theme="black" size="normal">
                                    {{ row.name }}
                                </abc-text>
                                <abc-link
                                    theme="primary"
                                    size="small"

                                    @click="handleOpenOriginalOrder(row)"
                                >
                                    {{ row.orderNo ?? '' }}
                                </abc-link>
                            </abc-flex>
                            <abc-text v-else>
                                {{ row.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #packageCostPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ getPackageCostPrice(row.packageCostPrice, '', row.goods) }}</span>
                        </abc-table-cell>
                    </template>

                    <template #count="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.displayGoodsCount }}</span>
                        </abc-table-cell>
                    </template>

                    <template #totalPrice="{ trData: row }">
                        <abc-table-cell>
                            <span>{{ row.totalCost | formatMoney(false) }}</span>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-form>
        </abc-flex>

        <template slot="footer">
            <abc-flex justify="flex-end">
                <abc-button
                    v-if="isAdd"
                    :loading="submitBtnLoading"
                    :disabled="submitBtnDisabled"
                    @click="handleConfirm"
                >
                    确定
                </abc-button>

                <abc-button
                    type="blank"
                    @click="showDialog = false"
                >
                    {{ cancelText }}
                </abc-button>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
// eslint-disable-next-line max-classes-per-file
    import { mapGetters } from 'vuex';
    import Big from 'big.js';
    import StockInAPI from 'api/goods/stock-in';
    import TraceCode, { TraceCodeScenesEnum } from '@/service/trace-code/service';
    import {
        createGUID,
        isNull,
        formatCacheTime,
        moneyDigit,
        parseTime,
    } from '@/utils';
    import clone from '@/utils/clone';

    import {
        validatePrice,
        calCostPriceSingle,
        calCostPriceTotal,
        checkExpiryDate,
        showExpiryDateTip,
        showProductionDateTip,
        validateExpirationTime,
        showOverdueTip,
    } from 'views/inventory/goods-in/common';
    import {
        complexCount,
        // clinicName,
        getGoodsPriceStr,
        // goodsSpec,
        // goodsTotalCostPrice,
        isChineseMedicine,
        // isChinesePatentMedicine,
        // isWesternMedicine,
    } from 'src/filters/goods';

    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import StockUnitOrInput from 'views/inventory/common/stock-unit-or-input';
    import EnterEvent from 'views/common/enter-event';
    import {
        CorrectOrderTypeEnum, GOODS_IN_PRICE_TYPE, GOODS_IN_STATUS, GOODS_OTHER_ORDER_TYPE,
    } from 'views/inventory/constant';
    import fecha from 'utils/fecha';
    import AbcScheduleDialog from 'views/inventory/goods-in/components/schedule-dialog';
    import { formatGoodsStock } from 'views/inventory/goods-out/common';
    import useReviseOrder, { GoodsOrderAPI } from 'views/inventory/hooks/useReviseOrder';
    import { FunctionalDialog } from '@/views/common/functional-dialog.js';
    import {
        ReTransInDialog, ReTransOutDialog, ApplyReturnDialog,
    } from 'views/inventory/goods-apply/trans-dialog.js';
    import useDeleteGoodsHandler from 'views/inventory/hooks/useDeleteGoodsHandler';

    const GoodsInDetailVue = () => import('views/inventory/goods-in/detail.vue');
    const GoodsReturnDetailVue = () => import('views/inventory/goods-in/return-detail.vue');
    const GoodsOutDetailVue = () => import('views/inventory/goods-out/detail.vue');
    const GoodsTransDetailVue = () => import('views/inventory/goods-trans/detail.vue');
    const GoodsCheckDetailVue = () => import('views/inventory/goods-check/detail.vue');

    class GoodsInDetailDialog extends FunctionalDialog {
        constructor(props, id = 'abc-goods-in-detail-dialog') {
            super(props, GoodsInDetailVue, id, 'showDialog');
        }
    }

    class GoodsReturnDetailDialog extends FunctionalDialog {
        constructor(props, id = 'abc-goods-return-detail-dialog') {
            super(props, GoodsReturnDetailVue, id, 'showDialog');
        }
    }

    class GoodsOutDetailDialog extends FunctionalDialog {
        constructor(props, id = 'abc-goods-out-detail-dialog') {
            super(props, GoodsOutDetailVue, id, 'showDialog');
        }
    }

    class GoodsTransDetailDialog extends FunctionalDialog {
        constructor(props, id = 'abc-goods-trans-detail-dialog') {
            super(props, GoodsTransDetailVue, id, 'showDialog');
        }
    }

    class GoodsCheckDetailDialog extends FunctionalDialog {
        constructor(props, id = 'abc-goods-check-detail-dialog') {
            super(props, GoodsCheckDetailVue, id, 'showDialog');
        }
    }

    const ItemDataType = Object.freeze({
        original: 1,// 原始数据
        current: 2,// 当前数据
        diff: 3,// 差异数据
    });
    const compareKey = Object.freeze([
        'useTotalCostPrice',
        'useUnitCostPrice',
        'useCount',
        'useUnit',
        'expiryDate',
        'productionDate',
        'batchNo',
    // 暂不对比追溯码
    // 'traceableCodeList'
    ]);
    const compareKeyNames = Object.freeze({
        useTotalCostPrice: '金额',
        useUnitCostPrice: '进价',
        useCount: '数量',
        useUnit: '数量',
        expiryDate: '有效日期',
        productionDate: '生产日期',
        batchNo: '生产批号',
    });

    export default {
        name: 'CorrectionOrderDialog',
        components: {
            StockUnitOrInput,
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
        },
        mixins: [EnterEvent],
        props: {
            visible: Boolean,
            /**
             * @desc 修正单类型
             * <AUTHOR>
             * @date 2024/10/22 上午11:49
             * @type {String}
             * @default 'goodsIn'
             */
            orderType: {
                type: String,
                default: CorrectOrderTypeEnum.GoodsIn,
                validator: (value) => [
                    CorrectOrderTypeEnum.GoodsIn,
                    CorrectOrderTypeEnum.GoodsOut,
                    CorrectOrderTypeEnum.GoodsApply,
                    CorrectOrderTypeEnum.GoodsTrans,
                    CorrectOrderTypeEnum.GoodsCheck,
                    CorrectOrderTypeEnum.GoodsReturn,
                    CorrectOrderTypeEnum.GoodsApply,
                ].indexOf(value) !== -1,
            },
            inOrderId: String,
            orderId: {
                type: String,
                default: '',
            },
            cancelText: {
                type: String,
                default: '关闭',
            },
            supplierName: {
                type: String,
            },
            orderItem: {
                type: Object,
                default: () => ({}),
            },
            dialogConfig: {
                type: Object,
                default: () => ({}),
            },
            successCallback: Function,
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager(`修正单详情-${Math.random().toString(36).slice(2)}`);

            const { getBusinessNamePrefix } = useReviseOrder();

            const {
                handleGoodsDelete,
                handleGoodsDisable,
            } = useDeleteGoodsHandler();

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                getBusinessNamePrefix,

                handleGoodsDelete,
                handleGoodsDisable,
            };
        },
        data() {
            return {
                ItemDataType,
                CorrectOrderTypeEnum,
                loading: false,
                showDialog: this.visible,
                goodsInfo: null,
                orderInfo: null,
                tableData: [],
                submitBtnLoading: false,
                // 进销存记录修正条数与预估操作时间
                reachData: {
                    tips: '',
                    totalCount: 0,
                    totalSeconds: 0,
                },
            };
        },
        computed: {
            GOODS_IN_STATUS() {
                return GOODS_IN_STATUS;
            },
            ...mapGetters([
                'currentPharmacy',
                'traceCodeConfig',
                'multiPharmacyCanUse',
                'isStrictCountWithTraceCodeCollect',
                'isShowReturnGoodsApplicationButton',
                'isNeedCheckStockInfoNotEmpty',
                'isNeedCheckStockInCostPriceNotZero',
            ]),
            // 待审核-目前只有张仲景门店会走审核
            canReview() {
                return this.orderInfo?.status === GOODS_IN_STATUS.REVIEW;
            },
            tooltips() {
                if (this.orderItem.editable?.readOnly) {
                    return this.orderItem.editable?.reason;
                }
                return this.isShowReturnGoodsApplicationButton ? '入库数量仅可修改一次' : '入库进价，单位，数量仅可修改一次';
            },
            disabledTooltips() {
                if (this.orderItem.editable?.readOnly) {
                    return false;
                }
                return !this.isFixed;
            },
            title() {
                if (this.inOrderId) return '修改采购信息';
                if (this.orderInfo) {
                    const {
                        type, orderNo,
                    } = this.orderInfo;
                    if (this.orderType === CorrectOrderTypeEnum.GoodsIn) return `采购入库修正单${orderNo}`;
                    if (this.orderType === CorrectOrderTypeEnum.GoodsTrans) return `调拨修正单${orderNo}`;
                    if (this.orderType === CorrectOrderTypeEnum.GoodsCheck) return `盘点修正单${orderNo}`;
                    if (this.orderType === CorrectOrderTypeEnum.GoodsReturn) return `退货出库修正单${orderNo}`;
                    if (this.orderType === CorrectOrderTypeEnum.GoodsApply) return `领用修正单${orderNo}`;
                    if (this.orderType === CorrectOrderTypeEnum.GoodsOut) {
                        if (type === GOODS_OTHER_ORDER_TYPE.FIX_LOSS_OUT_ORDER) {
                            return `报损修正单${orderNo}`;
                        }
                        if (type === GOODS_OTHER_ORDER_TYPE.FIX_DEPART_CONSUME_ORDER) {
                            return `科室消耗修正单${orderNo}`;
                        }
                        if (type === GOODS_OTHER_ORDER_TYPE.FIX_OTHER_OUT_ORDER) {
                            return `其他出库修正单${orderNo}`;
                        }
                        if (type === GOODS_OTHER_ORDER_TYPE.FIX_PRODUCTION_OUT_ORDER) {
                            return `生产出库修正单${orderNo}`;
                        }
                    }
                }
                return '修正单';
            },
            comment() {
                if (this.orderInfo) {
                    return this.orderInfo.comment?.[0]?.content ?? '';
                }
                return '';
            },
            showOutPharmacy() {
                return this.orderType !== CorrectOrderTypeEnum.GoodsIn;
            },
            showCostPrice() {
                return !(this.orderType === CorrectOrderTypeEnum.GoodsIn || this.orderType === CorrectOrderTypeEnum.GoodsReturn);
            },
            // 入库修正单中，如果没修改数量，追溯码无需采集
            needCollect() {
                // if (this.orderType === CorrectOrderTypeEnum.GoodsIn) {
                //     const [original, current] = this.tableData;
                //     if (isNull(original.useCount) && isNull(current.useCount)) {
                //         return false;
                //     }
                //     return Number(original.useCount || 0) !== Number(current.useCount || 0);
                // }
                // todo 设计不合理先放开
                return true;
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            isAdd() {
                return !this.orderId;
            },
            isFixed() {
                return !!this.orderItem?.fixedStockOrderList?.length;
            },
            inPharmacyName() {
                const {
                    inPharmacy, pharmacy,
                } = this.orderInfo || {};
                return inPharmacy?.name ?? pharmacy?.name ?? '';
            },
            outPharmacyName() {
                const {
                    outPharmacy, pharmacy,
                } = this.orderInfo || {};
                return outPharmacy?.name ?? pharmacy?.name ?? '';
            },
            dialogProps() {
                return {
                    title: this.title,
                    size: 'huge',
                    'content-styles': 'min-height: 543px;',
                    'append-to-body': true,
                    // showHeaderBorderBottom: false,
                    disabledKeyboard: this.disabledKeyboard,
                    ...this.dialogConfig,
                };
            },
            tableConfig() {
                let list = [];

                if (this.orderType === CorrectOrderTypeEnum.GoodsIn) {
                    list = [
                        {
                            label: '',
                            key: 'name',
                        },
                        {
                            label: '入库数量',
                            key: 'count',
                            style: {
                                width: '86px',
                                maxWidth: '86px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '单位',
                            key: 'unit',
                            style: {
                                width: '56px',
                                maxWidth: '56px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '进价',
                            key: 'packageCostPrice',
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '金额',
                            key: 'totalPrice',
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            style: {
                                width: '108px',
                                minWidth: '108px',
                                maxWidth: '108px',
                            },
                        },
                        {
                            label: '生产日期',
                            key: 'productionDate',
                            style: {
                                width: '96px',
                                minWidth: '96px',
                                maxWidth: '96px',
                            },
                        },
                        {
                            label: '有效日期',
                            key: 'expiryDate',
                            style: {
                                width: '96px',
                                minWidth: '96px',
                                maxWidth: '96px',
                            },
                        },
                        {
                            label: '追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                            },
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    });
                } else if (this.orderType === CorrectOrderTypeEnum.GoodsReturn) {
                    list = [
                        {
                            label: '',
                            key: 'name',
                        },
                        {
                            label: '类型',
                            key: 'type',
                        },
                        {
                            label: '进价',
                            key: 'packageCostPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '出库数量',
                            key: 'count',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '出库金额',
                            key: 'totalPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                    ];
                } else if (this.orderType === CorrectOrderTypeEnum.GoodsCheck) {
                    list = [
                        {
                            label: '',
                            key: 'name',
                        },
                        {
                            label: '类型',
                            key: 'type',
                        },
                        {
                            label: '进价',
                            key: 'packageCostPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '盈亏数量',
                            key: 'count',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '盈亏金额（进价）',
                            key: 'totalPrice',
                            style: {
                                width: '140px',
                                minWidth: '140px',
                                textAlign: 'right',
                            },
                        },
                    ];
                } else if (this.orderType === CorrectOrderTypeEnum.GoodsApply) {
                    let prefix = '入库';

                    const {
                        type,
                    } = this.orderInfo || {};
                    if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_IN_ORDER) {
                        prefix = '入库';
                    }
                    if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_BACK_IN_ORDER) {
                        prefix = '入库';
                    }
                    if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_OUT_ORDER) {
                        prefix = '出库';
                    }
                    if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_BACK_OUT_ORDER) {
                        prefix = '出库';
                    }

                    list = [
                        {
                            label: '',
                            key: 'name',
                        },
                        {
                            label: '类型',
                            key: 'type',
                        },
                        {
                            label: '进价',
                            key: 'packageCostPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: `${prefix}数量`,
                            key: 'count',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: `${prefix}金额`,
                            key: 'totalPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                    ];
                } else if (this.orderType === CorrectOrderTypeEnum.GoodsTrans) {
                    list = [
                        {
                            label: '',
                            key: 'name',
                        },
                        {
                            label: '进价',
                            key: 'packageCostPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '调拨数量',
                            key: 'count',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '调拨金额',
                            key: 'totalPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                    ];
                } else if (this.orderType === CorrectOrderTypeEnum.GoodsOut) {
                    list = [
                        {
                            label: '',
                            key: 'name',
                        },
                        {
                            label: '进价',
                            key: 'packageCostPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '出库数量',
                            key: 'count',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '出库金额',
                            key: 'totalPrice',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                    ];
                }

                return {
                    hasInnerBorder: true,
                    list,
                };
            },
            confirmTitle() {
                const titleList = [];
                if (!this.tableData.length) return [];
                const [original, current] = this.tableData;

                compareKey.forEach((key) => {
                    const newItem = current[key];
                    const oldItem = original[key];
                    if (isNull(newItem) && isNull(oldItem)) {
                    // 相等不处理
                    } else {
                        if (newItem !== oldItem) {
                            titleList.push(compareKeyNames[key]);
                        }
                    }
                });

                return titleList;
            },
            diffData() {
                if (this.isAdd && this.orderType === CorrectOrderTypeEnum.GoodsIn) {
                    const [original, current] = this.tableData;

                    const {
                        useCount, useUnit, useTotalCostPrice, goods,
                    } = original;
                    const {
                        useCount: count, useUnit: unit, useUnitCostPrice: costPrice, useTotalCostPrice: totalCostPrice,
                    } = current;
                    const {
                        packageUnit, pieceNum = 1,
                    } = goods;

                    // 都转换为小单位数量
                    const count1 = useUnit === packageUnit ? useCount * pieceNum : useCount;
                    const count2 = unit === packageUnit ? count * pieceNum : count;
                    const changeCount = Big((+count2 || 0)).minus((+count1 || 0)).toNumber();

                    const amount = Big(Number(totalCostPrice || 0)).minus(Number(useTotalCostPrice || 0)).toNumber();

                    const packageCostPrice = unit === packageUnit ? costPrice : Big(+costPrice || 0).times(pieceNum);

                    return {
                        displayCount: changeCount ? this.getDisplayCount(changeCount, goods) : '',
                        costPrice: packageCostPrice,
                        amount,
                    };
                }
                return {};
            },
            submitBtnDisabled() {
                if (!this.isAdd) return false;
                // eslint-disable-next-line no-unused-vars
                const [_, current] = this.tableData;

                return this.confirmTitle.length === 0 && current?.traceableCodeList?.length === 0;
            },
        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
                if (!val) {
                    this.onClose && this.onClose();
                }
            },
        },
        created() {
            if (this.isAdd && this.orderType === CorrectOrderTypeEnum.GoodsIn) {
                const {
                    id,
                    goods,
                    useCount,
                    useUnit,
                    useUnitCostPrice,
                    useTotalCostPrice,
                    batchNo,
                    productionDate,
                    expiryDate,
                    traceableCodeList,
                    lastModifiedDate,
                    // 已产生的修正单，后续修改要基于最后一次修正的数据进行修改
                    fixedStockOrderList = [],
                } = this.orderItem;
                this.goodsInfo = goods;
                let lastFixedOrder;// 最后一次修正单
                if (fixedStockOrderList) {
                    lastFixedOrder = fixedStockOrderList[fixedStockOrderList.length - 1];
                }

                if (lastFixedOrder) {
                    const item = lastFixedOrder.list[0];
                    this.tableData = [
                        {
                            keyId: createGUID(),
                            _type: ItemDataType.original,
                            name: '原始入库信息',
                            itemId: id,
                            goods,
                            batchNo,
                            useCount,
                            useUnit,
                            useUnitCostPrice,
                            useTotalCostPrice,
                            productionDate,
                            expiryDate,
                            lastModifiedDate,
                            traceableCodeList,// batchNo、productionDate、expiryDate、traceableCodeList使用原单上面的数据是最新的
                        },
                        // {
                        //     keyId: createGUID(),
                        //     _type: ItemDataType.original,
                        //     name: '最近修正入库信息',
                        //     orderNo: lastFixedOrder.orderNo,
                        //     orderId: lastFixedOrder.id,
                        //     itemId: item.id,
                        //     goods: item.goods,
                        //     batchNo,
                        //     batchId: item.batchId,
                        //     useCount: item.useCount,
                        //     useUnit: item.useUnit,
                        //     useUnitCostPrice: item.useUnitCostPrice,
                        //     useTotalCostPrice: item.useTotalCostPrice,
                        //     productionDate,
                        //     expiryDate,
                        //     lastModifiedDate: item.lastModifiedDate,
                        //     traceableCodeList,// batchNo、productionDate、expiryDate、traceableCodeList使用原单上面的数据是最新的
                        // },
                        {
                            keyId: createGUID(),
                            _type: ItemDataType.current,
                            name: '正确入库信息',
                            orderNo: lastFixedOrder.orderNo,
                            orderId: lastFixedOrder.id,
                            itemId: item.id,
                            goods: item.goods,
                            id,
                            batchNo,
                            batchId: item.batchId,
                            useCount: item.useCount,
                            useUnit: item.useUnit,
                            useUnitCostPrice: item.useUnitCostPrice,
                            useTotalCostPrice: item.useTotalCostPrice,
                            productionDate,
                            expiryDate,
                            lastModifiedDate,
                            traceableCodeList: clone(traceableCodeList),
                        },
                    ];

                } else {
                    this.tableData = [
                        {
                            keyId: createGUID(),
                            _type: ItemDataType.original,
                            name: '原始入库信息',
                            id,
                            useCount,
                            useUnit,
                            useUnitCostPrice,
                            useTotalCostPrice,
                            goods,
                            batchNo,
                            productionDate,
                            expiryDate,
                            traceableCodeList,
                            lastModifiedDate,
                        },
                        {
                            keyId: createGUID(),
                            _type: ItemDataType.current,
                            name: '正确入库信息',
                            id,
                            useCount,
                            useUnit,
                            useUnitCostPrice,
                            useTotalCostPrice,
                            goods,
                            batchNo,
                            productionDate,
                            expiryDate,
                            lastModifiedDate,
                            traceableCodeList: clone(traceableCodeList),
                        },
                    ];
                }

                // 初始化时计算追溯码应采数量
                this.$nextTick(() => {
                    this.initTraceCodeCountForTableData();
                });
            } else {
                this.fetchOrderDetail();
            }
        },
        methods: {
            moneyDigit,
            formatCacheTime,
            calCostPriceTotal,
            calCostPriceSingle,
            validatePrice,
            getGoodsPriceStr,
            handleOpenOriginalOrder(row) {
                console.log('handleOpenOriginalOrder', this.orderType);
                if (this.orderType === CorrectOrderTypeEnum.GoodsIn) {
                    new GoodsInDetailDialog({
                        orderId: row.orderId,
                        pharmacyType: this.currentPharmacy?.type,
                    }).generateDialogAsync({
                        parent: this,
                    });
                }
                if (this.orderType === CorrectOrderTypeEnum.GoodsReturn) {
                    new GoodsReturnDetailDialog({
                        orderId: row.orderId,
                    }).generateDialogAsync({
                        parent: this,
                    });
                }
                if (this.orderType === CorrectOrderTypeEnum.GoodsOut) {
                    new GoodsOutDetailDialog({
                        orderId: row.orderId,
                        outType: this.orderInfo?.type,
                    }).generateDialogAsync({
                        parent: this,
                    });
                }
                if (this.orderType === CorrectOrderTypeEnum.GoodsTrans) {
                    new GoodsTransDetailDialog({
                        orderId: row.orderId,
                    }).generateDialogAsync({
                        parent: this,
                    });
                }
                if (this.orderType === CorrectOrderTypeEnum.GoodsCheck) {
                    new GoodsCheckDetailDialog({
                        orderId: row.orderId,
                    }).generateDialogAsync({
                        parent: this,
                    });
                }
                if (this.orderType === CorrectOrderTypeEnum.GoodsApply) {
                    const {
                        type,
                    } = this.orderInfo;
                    if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_IN_ORDER) {
                        new ReTransInDialog({
                            orderId: row.orderId,
                            pharmacyNo: this.currentPharmacy?.no,
                        }).generateDialogAsync({
                            parent: this,
                        });
                    }

                    if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_OUT_ORDER) {
                        new ReTransOutDialog({
                            orderId: row.orderId,
                            pharmacyNo: this.currentPharmacy?.no,
                        }).generateDialogAsync({
                            parent: this,
                        });
                    }

                    if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_BACK_OUT_ORDER || type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_BACK_IN_ORDER) {
                        new ApplyReturnDialog({
                            orderId: row.orderId,
                            pharmacyNo: this.currentPharmacy?.no,
                        }).generateDialogAsync({
                            parent: this,
                        });
                    }
                }

            },
            /**
             * @desc 格式化变化的数量
             * <AUTHOR>
             * @date 2024/12/14 上午10:00
             * @param changeCount 小单位数量
             */
            getDisplayCount(changeCount, goods) {
                let sign = '';
                if (changeCount > 0) {
                    sign = '+';
                } else if (changeCount < 0) {
                    sign = '-';
                }
                changeCount = Math.abs(changeCount);

                let pieceNum, pieceCount, packageCount;
                // 中药的数量都是保存在pieceCount中
                if (isChineseMedicine(goods)) {
                    // 大单位数据清 0 防止重复拼接（如果packageCount有值，pieceCount无值的中药）
                    packageCount = 0;
                    pieceCount = changeCount;
                } else {
                    pieceNum = +goods.pieceNum || 1;
                    pieceCount = Big(changeCount).mod(pieceNum);
                    packageCount = Math.floor(changeCount / pieceNum);
                }
                return sign + complexCount({
                    pieceCount,
                    packageCount,
                    goods,
                });
            },
            getPackageCostPrice(price, useUnit, goods) {
                if (isNull(price)) return '';
                const unit = isChineseMedicine(goods) ? goods.pieceUnit : goods.packageUnit;
                return `${moneyDigit(price, 5)}/${useUnit || unit}`;
            },
            getApplyType(row) {
                const {
                    type,
                } = this.orderInfo;
                if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_IN_ORDER) {
                    return row._type === ItemDataType.original ? '领用-入库' : '领用-修正';
                }
                if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_BACK_IN_ORDER) {
                    return row._type === ItemDataType.original ? '退回-入库' : '退回-修正';
                }
                if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_OUT_ORDER) {
                    return row._type === ItemDataType.original ? '领用-出库' : '领用-修正';
                }
                if (type === GOODS_OTHER_ORDER_TYPE.FIX_RECEPTION_BACK_OUT_ORDER) {
                    return row._type === ItemDataType.original ? '退回-出库' : '退回-修正';
                }
                return '';
            },
            onDialogOpen() {
                this.pushDialogName();
                this.$emit('open');
            },
            onDialogClose() {
                this.popDialogName();
                this.$emit('close');
            },
            handleConfirm() {
                /**
                 * 1.修改采购信息
                 * 2.改变数量/单位导致追溯码重新计算应采
                 */
                if (this.confirmTitle.includes('数量') && this.inOrderId && this.isEnableTraceableCode) {
                    let validateTraceCodeMaxCountNum = 0;
                    this.tableData.forEach((item) => {
                        const refKey = `traceableCodeCellRef${item.keyId}`;
                        const traceableCodeCellRefs = this.$refs[refKey];
                        if (traceableCodeCellRefs) {
                            const isValidate = traceableCodeCellRefs.validateTraceCodeMaxCount?.();
                            if (!isValidate) validateTraceCodeMaxCountNum++;
                        }
                    });
                    if (validateTraceCodeMaxCountNum) {
                        this.$confirm({
                            type: 'warn',
                            title: '追溯码采集提示',
                            content: `${validateTraceCodeMaxCountNum} 个药品追溯码采集条数不符合医保要求`,
                            confirmText: '去修改',
                            showCancel: false,
                        });
                        return;
                    }
                }

                const noConfirm = this.confirmTitle.includes('数量') || this.confirmTitle.includes('进价') || this.confirmTitle.includes('金额');
                this.$refs.formRef.validate(async (val) => {
                    if (val) {
                        if (noConfirm) {
                            this.$confirm({
                                title: '提示',
                                content: '确定后根据正确入库信息生成采购入库修正单',
                                onConfirm: () => {
                                    this.saveCurrentItemEdit();
                                },
                            });
                            return;
                        }

                        this.$confirm({
                            title: '提示',
                            content: '确定后根据正确入库信息更新入库单',
                            onConfirm: () => {
                                this.saveCurrentItemEdit();
                            },
                        });

                    }
                });

            },
            showUpdateTips(onConfirm) {
                const {
                    totalCount, tips, totalSeconds = 0,
                } = this.reachData;
                let text = tips;

                if (!text) {
                    if (totalSeconds > 2 * 60) {
                        text = `保存后将修正${totalCount}条进销存记录，预计${totalSeconds},
                            <br>期间无法发药、出入库、盘点，建议闭店后操作。
                            <br>确认继续吗？`;
                    } else if (totalSeconds > 10 && totalSeconds < 2 * 60) {
                        text = `保存后将修正${totalCount}条进销存记录，预计${totalSeconds},
                            <br>期间无法发药、出入库、盘点，确认继续吗？`;
                    }
                }

                this.$confirm({
                    title: '提示',
                    content: tips,
                    onConfirm,
                });
            },
            async saveCurrentItemEdit() {
                const {
                    id,
                    goods,
                    batchNo, // 批号
                    expiryDate, // 效期
                    productionDate, // 生产日期
                    useUnitCostPrice, // 成本价
                    useTotalCostPrice,
                    useUnit, // 使用的单位
                    useCount, // 使用的单位数量,
                    lastModifiedDate, // 详单最后更新时间
                    traceableCodeList,//追溯码列表
                    opType, // 单价优先还是总价优先
                } = this.tableData[1];
                this.submitBtnLoading = true;
                try {
                    const {
                        totalSeconds = 0,
                    } = this.reachData;

                    const postData = {
                        goodsId: goods.goodsId || goods.id,
                        batchNo,
                        expiryDate: fecha.format(expiryDate, 'YYYY-MM-DD'),
                        productionDate: fecha.format(productionDate, 'YYYY-MM-DD'),
                        useUnitCostPrice,
                        useTotalCostPrice,
                        useUnit,
                        useCount,
                        lastModifiedDate,
                        opType,
                        traceableCodeList,
                    };

                    // if (this.isAdd && (this.needCollect || this.isFixed)) {
                    //     const list = TraceCode.transCodeList(traceableCodeList);
                    //     postData.traceableCodeList = list?.length ? list : null;
                    // }

                    // 保存了修改的信息之后，重新再获取一次最新的数据
                    const res = await StockInAPI.updateDetail(this.inOrderId, id, postData);
                    // 大于10s的任务才显示进度
                    if ((totalSeconds >= 10) && res.taskId) {
                        // 测试进度弹窗
                        this._AbcScheduleDialogInstance = new AbcScheduleDialog({
                            goods,
                            taskId: res.taskId,
                            successFn: () => {
                                this.successFn(res);
                            },
                        });
                        this._AbcScheduleDialogInstance?.generateDialog({
                            parent: this,
                        });
                    } else {
                        await this.successFn(res);
                    }
                } catch (e) {
                    if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail);
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail);
                    } else if (e.code === 962) {
                        // Object.assign(this.order.list[index], Clone(this.cloneOrder.list[index]));
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前库存信息已被修改，请刷新',
                        });
                    } else if (e.code === 471 || e.code === 400) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                    }

                } finally {
                    this.submitBtnLoading = false;
                }
            },
            customTrKey(tr) {
                return tr.keyId || tr.id;
            },
            /**
             * @desc 获取修正单详情
             * <AUTHOR>
             * @date 2024/11/21 下午2:53
             */
            async fetchOrderDetail(orderId = this.orderId) {
                try {
                    this.loading = true;
                    const res = await GoodsOrderAPI[this.orderType](orderId);
                    this.goodsInfo = res?.list?.[0]?.goods;
                    this.orderInfo = res;
                    this.createTableData(res);
                } catch (e) {
                    console.error(e);
                    if (!e.alerted) {
                        this.$Toast({
                            message: e.message,
                            type: 'error',
                        });
                    }
                } finally {
                    this.loading = false;
                }
            },
            // 创建tableData
            createTableData(res) {
                const {
                    originalOrder, list = [], supplier,
                } = res || {};

                // const fixedItemMap = originalOrder?.list?.reduce((map, item) => {
                //     // 目前只能通过批次id关联关系
                //     map[item.batchId] = item;
                //     return map;
                // }, {});

                // 遍历修正单的list-目前的设计只会有一条修正数据
                const tableData = list?.map((item) => {
                    if (this.orderType === CorrectOrderTypeEnum.GoodsIn) {
                        const {
                            id,
                            goods,
                            // batchId, // 批次号
                            originalItemId,
                            batchNo, // 批号
                            expiryDate, // 效期
                            productionDate, // 生产日期
                            useUnitCostPrice, // 成本价
                            useTotalCostPrice,
                            useUnit, // 使用的单位
                            useCount, // 使用的单位数量,
                            lastModifiedDate, // 详单最后更新时间
                            traceableCodeList,//追溯码列表
                            diffView, // 修正信息
                        } = item;

                        let originalItem = originalOrder.list[0];
                        if (originalItemId) {
                            originalItem = originalOrder.list?.find((item) => item.id === originalItemId);
                        }

                        if (this.orderInfo) {
                            this.orderInfo.diffView = {
                                ...diffView,
                                supplierName: supplier,
                            };
                        }

                        return [
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.original,
                                name: '原始入库信息',
                                orderNo: originalOrder.orderNo,
                                orderId: originalOrder.id,
                                id: originalItem.id,
                                useCount: originalItem.useCount,
                                useUnit: originalItem.useUnit,
                                useUnitCostPrice: originalItem.useUnitCostPrice,
                                useTotalCostPrice: originalItem.useTotalCostPrice,
                                goods: originalItem.goods,
                                batchNo: originalItem.batchNo,
                                productionDate: originalItem.productionDate,
                                expiryDate: originalItem.expiryDate,
                                traceableCodeList: originalItem.traceableCodeList,
                                lastModifiedDate: originalItem.lastModifiedDate,
                            },
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.current,
                                name: '正确入库信息',
                                id,
                                useCount,
                                useUnit,
                                useUnitCostPrice,
                                useTotalCostPrice,
                                goods,
                                batchNo,
                                productionDate,
                                expiryDate,
                                traceableCodeList,
                                lastModifiedDate,
                            },
                            // {
                            //     keyId: createGUID(),
                            //     _type: ItemDataType.diff,
                            //     name: '修正信息',
                            //     goods,
                            //     displayGoodsCount: diffView.displayGoodsCount,
                            //     useUnitCostPrice: diffView.packageCostPrice,
                            //     useTotalCostPrice: diffView.totalCost,
                            // },
                        ];
                    }
                    if (this.orderType === CorrectOrderTypeEnum.GoodsReturn) {
                        const {
                            id,
                            goods,
                            // batchId, // 批次号
                            packageCount,
                            pieceCount,
                            originalItemId,
                            // useUnitCostPrice,
                            useTotalCostPrice,
                            diffView, // 修正信息
                        } = item;

                        let originalItem = originalOrder.list[0];
                        if (originalItemId) {
                            originalItem = originalOrder.list?.find((item) => item.id === originalItemId);
                        }

                        if (this.orderInfo) {
                            this.orderInfo.diffView = {
                                ...diffView,
                                supplierName: supplier,
                            };
                        }

                        const displayGoodsCount = formatGoodsStock(originalItem.packageCount, originalItem.pieceCount, originalItem.goods);
                        const displayGoodsCountAfter = formatGoodsStock(packageCount, pieceCount, goods);
                        const useUnit = originalItem.useUnit || item.useUnit || goods.packageUnit || '';
                        return [
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.original,
                                name: '原始退货出库单',
                                id: originalItem.id,
                                orderId: originalOrder.id,
                                orderNo: originalOrder.orderNo,
                                useUnit,
                                useUnitCostPrice: originalItem.useUnitCostPrice,
                                useTotalCostPrice: originalItem.useTotalCostPrice,
                                goods: originalItem.goods,
                                displayGoodsCount ,
                            },
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.diff,
                                name: '正确退货出库信息',
                                id,
                                goods,
                                useUnit,
                                useUnitCostPrice: diffView.packageCostPrice,
                                useTotalCostPrice,
                                displayGoodsCount: displayGoodsCountAfter,
                            },
                        ];
                    }
                    if (this.orderType === CorrectOrderTypeEnum.GoodsCheck) {
                        const {
                            id,
                            goods,
                            // batchId, // 批次号
                            originalItemId, // 原单对应的item id
                            totalCostPriceChange,
                            diffView, // 修正信息
                            packageCountChange,
                            pieceCountChange,
                        } = item;
                        // 这个关系是后端给的，产生了修正单就一定能找到
                        let originalItem = originalOrder.list[0];
                        if (originalItemId) {
                            originalItem = originalOrder.list?.find((item) => item.id === originalItemId);
                        }

                        const totalPriceChange = originalOrder.totalCostPriceChange;
                        const displayGoodsCount = formatGoodsStock(originalItem.packageCountChange, originalItem.pieceCountChange, originalItem.goods);
                        const displayGoodsCountAfter = formatGoodsStock(packageCountChange, pieceCountChange, goods);

                        if (this.orderInfo) {
                            const beforePackageCostPrice = diffView.beforePackageCostPrice || originalItem.packageCostPrice;
                            this.orderInfo.diffView = {
                                ...diffView,
                                // 修正前的进价
                                beforePackageCostPrice,
                                afterPackageCostPrice: diffView.packageCostPrice,
                            };
                        }

                        return [
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.original,
                                name: '原始盘点信息',
                                orderId: originalOrder.id,
                                orderNo: originalOrder.orderNo,
                                typeName: totalPriceChange >= 0 ? '盘盈' : '盘亏',
                                id: originalItem.id,
                                packageCostPrice: originalItem.packageCostPrice,
                                totalCostPriceChange: originalItem.totalCostPriceChange,
                                goods: originalItem.goods,
                                displayGoodsCount,
                            },
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.diff,
                                name: '正确盘点信息',
                                typeName: totalPriceChange >= 0 ? '盘盈修正' : '盘亏修正',
                                id,
                                goods,
                                packageCostPrice: diffView.packageCostPrice,
                                totalCostPriceChange,
                                displayGoodsCount: displayGoodsCountAfter,
                            },
                        ];
                    }
                    if (this.orderType === CorrectOrderTypeEnum.GoodsTrans) {
                        const {
                            batchs,// 调拨的修正明细在这里面
                            originalItemId,
                            totalCostPrice, // 修正单原单item的金额
                        } = item;

                        // 目前从入库修正一次只能改一条数据，所以产生的修正批次也只会有一个。
                        const {
                            diffView,
                            packageCount,
                            pieceCount,
                            batTotalCostPrice,
                        } = batchs[0];
                        let originalItem = originalOrder.list[0];
                        if (originalItemId) {
                            originalItem = originalOrder.list?.find((item) => item.id === originalItemId);
                        }
                        const {
                            packageCostPrice, goods,
                        } = originalItem;

                        const displayGoodsCount = formatGoodsStock(originalItem.packageCount, originalItem.pieceCount, goods);
                        const displayGoodsCountAfter = formatGoodsStock(packageCount, pieceCount, goods);

                        if (this.orderInfo) {
                            const beforePackageCostPrice = diffView.beforePackageCostPrice || packageCostPrice;
                            this.orderInfo.diffView = {
                                ...diffView,
                                // 修正前的进价
                                beforePackageCostPrice,
                                afterPackageCostPrice: diffView.packageCostPrice,
                            };
                        }

                        return [
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.original,
                                name: '原始调拨信息',
                                orderNo: originalOrder.orderNo,// 原调拨单号
                                orderId: originalOrder.id,
                                id: originalItem.id,
                                goods,
                                displayGoodsCount,
                                packageCostPrice,
                                totalCostPrice: batTotalCostPrice,
                            },
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.diff,
                                name: '正确调拨信息',
                                goods: item.goods,
                                packageCostPrice: diffView.packageCostPrice,
                                totalCostPrice,
                                displayGoodsCount: displayGoodsCountAfter,
                            },
                        ];
                    }
                    if (this.orderType === CorrectOrderTypeEnum.GoodsOut) {
                        const {
                            id,
                            goods,
                            // batchId, // 批次号
                            originalItemId,
                            totalCost,
                            diffView, // 修正信息
                            packageCount,
                            pieceCount,
                        } = item;
                        let originalItem = originalOrder.list[0];
                        if (originalItemId) {
                            originalItem = originalOrder.list?.find((item) => item.id === originalItemId);
                        }
                        const displayGoodsCount = formatGoodsStock(originalItem.packageCount, originalItem.pieceCount, originalItem.goods);
                        const displayGoodsCountAfter = formatGoodsStock(packageCount, pieceCount, goods);


                        if (this.orderInfo) {
                            const beforePackageCostPrice = diffView.beforePackageCostPrice || originalItem.packageCostPrice;
                            this.orderInfo.diffView = {
                                ...diffView,
                                // 修正前的进价
                                beforePackageCostPrice,
                                afterPackageCostPrice: diffView.packageCostPrice,
                            };
                        }

                        const name = this.getBusinessNamePrefix(this.orderType, res.type);

                        return [
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.original,
                                name: `原始${name}信息`,
                                orderNo: originalOrder.orderNo,
                                orderId: originalOrder.id,
                                id: originalItem.id,
                                packageCostPrice: originalItem.packageCostPrice,
                                totalCost: originalItem.totalCost,
                                goods: originalItem.goods,
                                displayGoodsCount,
                            },
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.diff,
                                name: `正确${name}信息`,
                                id,
                                goods,
                                packageCostPrice: diffView.packageCostPrice,
                                totalCost,
                                displayGoodsCount: displayGoodsCountAfter,
                            },
                        ];
                    }
                    if (this.orderType === CorrectOrderTypeEnum.GoodsApply) {
                        const {
                            batchs,// 领用的修正明细在这里面
                            goods,
                            originalItemId,
                            totalCostPrice,
                        } = item;

                        // 目前从入库修正一次只能改一条数据，所以产生的修正批次也只会有一个。
                        const {
                            diffView,
                            packageCount,
                            pieceCount,
                        } = batchs[0];

                        let originalItem = originalOrder.list[0];
                        if (originalItemId) {
                            originalItem = originalOrder.list?.find((item) => item.id === originalItemId);
                        }
                        const displayGoodsCount = formatGoodsStock(originalItem.packageCount, originalItem.pieceCount, originalItem.goods);
                        const displayGoodsCountAfter = formatGoodsStock(packageCount, pieceCount, goods);


                        if (this.orderInfo) {
                            const beforePackageCostPrice = diffView.beforePackageCostPrice || originalItem.packageCostPrice;
                            this.orderInfo.diffView = {
                                ...diffView,
                                // 修正前的进价
                                beforePackageCostPrice,
                                afterPackageCostPrice: diffView.packageCostPrice,
                            };
                        }


                        return [
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.original,
                                name: '原始领用信息',
                                orderNo: originalOrder.orderNo,
                                orderId: originalOrder.id,
                                id: originalItem.id,
                                packageCostPrice: originalItem.packageCostPrice,
                                totalCost: originalItem.totalCostPrice,
                                goods: originalItem.goods,
                                displayGoodsCount,
                            },
                            {
                                keyId: createGUID(),
                                _type: ItemDataType.diff,
                                name: '正确领用信息',
                                goods,
                                packageCostPrice: diffView.packageCostPrice,
                                totalCost: totalCostPrice,
                                displayGoodsCount: displayGoodsCountAfter,
                            },
                        ];
                    }
                    return [];
                }) ?? [];

                // 考虑的兼容多个修正数据的场景
                this.tableData = tableData.flat(1);

                // 初始化时计算追溯码应采数量
                this.$nextTick(() => {
                    this.initTraceCodeCountForTableData();
                });
            },
            // 创建成功后
            async successFn(data) {
                if (typeof this.successCallback === 'function') {
                    this.successCallback(data);
                }
                this.$emit('success', data);
                this.$Toast.success('操作成功');
                this.showDialog = false;
            },
            validateNull(callback, value, type) {
                value = value[type === 'count' ? 'useCount' : 'useUnit'];
                // 支持修改为0
                if (isNull(value)) {
                    callback({
                        validate: false, message: '不能为空',
                    });
                    return;
                }
                callback({ validate: true });
            },
            async handleChangeUnitCount(item) {
                // 开启强校验时才实时计算
                if (this.isEnableTraceableCode && this.isStrictCountWithTraceCodeCollect) {
                    await this.initCollectCodeCountList([item]);
                }

                // 对无码商品初始化追溯码
                if (this.isEnableTraceableCode && TraceCode.isNoTraceCodeGoods(item.goods) && TraceCode.isSupportTraceCode(item.goods?.typeId)) {
                    item.traceableCodeList = TraceCode.mergeNoTraceCodeList({
                        ...item,
                        isTrans: item._isTransformable,
                        maxCount: item._maxTraceCodeCount,
                        unitCount: item.useCount,
                        unit: item.useUnit,
                    });
                }

                this.getCostPriceTotal(item);
            },
            /**
             * @desc 初始化时计算表格数据中所有项目的追溯码应采数量
             */
            async initTraceCodeCountForTableData() {
                if (!this.isEnableTraceableCode || !this.isStrictCountWithTraceCodeCollect) {
                    return;
                }

                // 只对当前行数据（可编辑的行）计算追溯码应采数量
                const currentItems = this.tableData.filter((item) => item._type === ItemDataType.current);

                if (currentItems.length > 0) {
                    await this.initCollectCodeCountList(currentItems);
                }
            },
            async initCollectCodeCountList(list = []) {
                const resList = await TraceCode.getMaxTraceCountList({
                    scene: TraceCodeScenesEnum.INVENTORY,
                    dataList: list,
                    getGoodsInfo: (item) => item.goods,
                    getUnitInfo: (item) => ({
                        unitCount: item.useCount,
                        unit: item.useUnit,
                    }),
                    createKeyId: (item) => item.id,
                });

                resList.forEach((e) => {
                    const item = list.find((i) => i.id === e.keyId);
                    if (item) {
                        this.$set(item, '_maxTraceCodeCount', e.traceableCodeNum);
                    }
                });
            },
            getCostPriceSingle(item) {
                if (item.useCount !== '' && item.useTotalCostPrice !== '') {
                    item.useUnitCostPrice = this.calCostPriceSingle(item);
                }
            },
            getCostPriceTotal(item) {
                if (item.useCount !== '' && item.useUnitCostPrice !== '') {
                    item.useTotalCostPrice = this.calCostPriceTotal(item);
                }
            },
            getCostConfig(item) {
                if (isChineseMedicine(item.goods) && item.useUnit === 'Kg') {
                    return {
                        formatLength: 2, max: 10000000, supportZero: true,
                    };
                }
                return {
                    formatLength: 5, max: 10000000, supportZero: true,
                };
            },
            packageCostPriceChange(item) {
                item.opType = GOODS_IN_PRICE_TYPE.UNIT_PRICE;
            },
            totalCostPriceChange(item) {
                item.opType = GOODS_IN_PRICE_TYPE.TOTAL_PRICE;
            },
            validateCostPrice(callback, item) {
                // 支持 0
                if (isNull(item.useUnitCostPrice)) {
                    callback({
                        validate: false, message: '不能为空',
                    });
                    return;
                }

                // 河北不支持0进价
                if (this.isNeedCheckStockInCostPriceNotZero && +item.useUnitCostPrice === 0) {
                    callback({
                        validate: false, message: '不能为0',
                    });
                    return;
                }
                callback({ validate: true });
            },
            validateTotalPrice(value, row, callback) {
                if (Number(row.useUnitCostPrice) === 0 && Number(value) === 0) {
                    callback({
                        validate: true,
                    });
                    return;
                }

                const isError = (Number(row.useCount) === 0 && Number(value) > 0) ||
                    (Number(value) === 0 && Number(row.useCount) > 0);
                if (isError) {
                    callback({
                        validate: false,
                        message: `入库数量为${Number(row.useCount)}，请重新检查入库总金额`,
                    });
                    return;
                }

                callback({
                    validate: true,
                });
            },
            validateDate(string, next) {
                if (!string || checkExpiryDate(string)) {
                    next({ validate: true });
                } else {
                    next({
                        validate: false, message: '格式错误',
                    });
                }
            },
            validateBatchNo(value, callback) {
                if (this.isNeedCheckStockInfoNotEmpty) {
                    if (isNull(value)) {
                        callback({
                            validate: false, message: '医保要求生产批号必填',
                        });
                    } else {
                        callback({ validate: true });
                    }
                } else {
                    callback({ validate: true });
                }
            },

            /**
             * @desc 1. 效期不可小于或等于生产日期 2. 该药品将在一年半之内过期
             * <AUTHOR>
             * @date 2022-04-11 17:38:22
             */
            getExpiryDateTooltip(item) {
                if (!item.expiryDate || !checkExpiryDate(item.expiryDate)) return '';

                let diffTime;
                const expiredWarnMonths = item.goods?.expiredWarnMonths;

                // 诊所、医院总部没有预警设置
                if (expiredWarnMonths && !this.isChainAdmin) {
                    diffTime = expiredWarnMonths * 30 * 24 * 3600 * 1000;
                }

                const today = parseTime((new Date()), 'y-m-d', true);
                if (showOverdueTip(item.expiryDate, today)) {
                    return '该药品已过期';
                }
                if (showExpiryDateTip(item.productionDate, item.expiryDate)) {
                    return '效期不可小于或等于生产日期';
                }
                if (validateExpirationTime(today, item.expiryDate, diffTime)) {
                    if (diffTime) {
                        return `该药品将在${expiredWarnMonths}个月之内过期`;
                    }
                    return '该药品将在一年半之内过期';
                }
                return '';
            },
            getProductDateTooltip(item) {
                if (!item.productionDate || !checkExpiryDate(item.productionDate)) return '';
                return showProductionDateTip(item.productionDate);
            },
            // 是否允许编辑数量
            isEditCount(type) {
                // 当前行数据是新建修正订单，且是第一次修正才允许修改
                return type === ItemDataType.current && this.isAdd && !this.isFixed && !this.orderItem.editable?.readOnly;
            },
            // 是否允许编辑价格
            isEditPrice(type) {
                // 张仲景门店不允许修改金额
                if (this.isShowReturnGoodsApplicationButton) {
                    return false;
                }
                // 当前行数据是新建修正订单，且是第一次修正才允许修改
                return this.isEditCount(type);
            },
        },

    };
</script>

<style lang="scss" scoped>
.cover-base-border {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    pointer-events: none;
    background: transparent;
}

.cover-top-border {
    top: -1px;
    border-top: 1px solid #ffffff;
}

.cover-left-border {
    left: -1px;
    border-left: 1px solid #ffffff;
}

.cover-right-border {
    right: -1px;
    border-right: 1px solid #ffffff;
}

.cover-top-left-border {
    top: -1px;
    left: -1px;
    border-top: 1px solid #ffffff;
    border-left: 1px solid #ffffff;
}
</style>
