import ABCClientManager from './abc-client-manager.js';
import { getPrintJson } from '../print-init/print-asstes.js';
import { loadScript } from '@abc/utils-dom';
import { clone } from '@abc/utils';

export default class PrintManager {
    static instance = null;

    static getInstance() {
        if (!PrintManager.instance) {
            PrintManager.instance = new PrintManager();
        }
        return PrintManager.instance;
    }

    constructor() {
        this.init();
    }

    feature = {
        isLoadABCPrintPackage: false,
        isLoadDevelopPrintPackage: false,
    };

    abcClientManager = null;

    setFeature(key, value) {
        this.feature[key] = value;
    }

    getFeature(key) {
        return this.feature[key];
    }

    loadABCPrintPackage() {
        if (this.getFeature('isLoadABCPrintPackage')) {
            return;
        }
        const isLoadDevelopPrintPackage = this.getFeature('isLoadDevelopPrintPackage');
        // const isLoadDevelopPrintPackage = true;
        if (isLoadDevelopPrintPackage) {
            console.log('%c 请开发打印的同学运行yarn dev:print', 'background: red; padding: 4px;font-size: 18px;color: white; font-weight: bold;');
        }
        const jsSrcList = isLoadDevelopPrintPackage ? [
            {
                'js': '//static-dev-cdn.abczs.cn/abc-print/loader.js',
            },
        ] : getPrintJson();
        const timestamp = new Date().getTime();
        jsSrcList?.forEach((jsSrc) => {
            loadScript(`${jsSrc.js}?t=${timestamp}`, '');
        });
        this.setFeature('isLoadABCPrintPackage', true);
    }

    init() {
        // 避免内部调用时printManager未初始化
        this._timer = setTimeout(() => {
            this.abcClientManager = ABCClientManager.getInstance();
            this.loadABCPrintPackage();
            delete this._timer;
        });
    }

    getPrinterList() {
        return this.abcClientManager.getPrinterList();
    }

    checkDeviceIndexByDeviceName(deviceIndex, deviceName) {
        return this.getPrinterList().some((printer) => {
            return printer.deviceIndex === deviceIndex && printer.deviceName === deviceName;
        });
    }

    getOffsetByDeviceName(deviceName) {
        return this.abcClientManager.getOffsetByDeviceName(deviceName);
    }

    mergedPagesByDeviceName(deviceName, customPages) {
        customPages = clone(customPages);
        const noAbcPrintCustomPageList = this.abcClientManager.getPageListByDeviceName(deviceName)
            .filter((electronPage) => !customPages.some((customPage) => electronPage.paper.name === customPage.paper.name));
        customPages.sort((a) => (a.isRecommend ? -1 : 1));
        return customPages.concat(noAbcPrintCustomPageList);
    }

    isElectronPageSize(deviceName, paperName) {
        return this.abcClientManager.isElectronPageSize(deviceName, paperName);
    }

    supportTinyPrinter(i) {
        return this.abcClientManager.supportTinyPrinter(i);
    }

    async checkPrintInitStatus() {
        return true;
    }

    // 调试
    debug(printConfig, template, mode, extra) {
        this.abcClientManager.debug(printConfig, template, extra);
    }

    // 获取打印机物理偏移
    getPrinterOffset(deviceName) {
        return this.abcClientManager.getPrinterOffset(deviceName);
    }
}
