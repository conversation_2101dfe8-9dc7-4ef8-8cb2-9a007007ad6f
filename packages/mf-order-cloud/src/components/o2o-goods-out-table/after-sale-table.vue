<template>
    <div class="after-sale-table">
        <abc-table
            :render-config="renderConfig"
            :data-list="dataList"
            cell-size="large"
            type="excel"
            top-header-theme="white"
            :show-hover-tr-bg="false"
            :fixed-tr-height="false"
        >
            <template #topHeader>
                <abc-descriptions
                    :column="4"
                    :label-width="88"
                    label-align="left"
                    :bordered="false"
                    size="large"
                    style="width: 100%;"
                    content-padding="12px 0"
                >
                    <abc-descriptions-item content-padding="0" label="">
                        <abc-text bold theme="warning" style="padding-left: 10px;">
                            退货提示：本次有 {{ dataList.length }} 种商品退货
                        </abc-text>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="退款金额" :label-style="{ 'padding-left': '10px' }">
                        <abc-money :value="afterSaleDetail.refundAmount"></abc-money>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="退款理由" :label-style="{ 'padding-left': '10px' }">
                        {{ afterSaleDetail.reason }}
                    </abc-descriptions-item>
                    <abc-descriptions-item label="退款时间" :label-style="{ 'padding-left': '10px' }">
                        {{ afterSaleDetail.extCreated | parseTime }}
                        <abc-link style="margin-left: 64px;" size="small" @click="viewAfterSaleDetail">
                            查看详情
                        </abc-link>
                    </abc-descriptions-item>
                </abc-descriptions>
            </template>
            <template #orderGoodsInfo="{ trData }">
                <abc-table-cell class="product-info-cell" style="height: 100%;">
                    <abc-flex vertical gap="2" style="flex: 1; width: 0;">
                        <abc-flex align="center">
                            <abc-text class="ellipsis">
                                {{ trData.extGoodsName }}
                            </abc-text>
                            <abc-flex style="flex-shrink: 0; margin-left: auto;">
                                * {{ trData.extGoodsCount || '-' }}
                            </abc-flex>
                        </abc-flex>
                        <abc-flex align="center">
                            <abc-text theme="gray" size="mini">
                                SKUID：{{ trData.ecGoodsSkuId }}
                            </abc-text>
                        </abc-flex>
                    </abc-flex>
                </abc-table-cell>
            </template>

            <template #ABCGoods="{ trData }">
                <table-cell-goods :item="trData"></table-cell-goods>
            </template>

            <template #dealWay="{ trData }">
                <abc-table-cell style="height: 100%;">
                    {{ trData.stockDealResultType === AfterSaleItemStockDealResultType.RETURN_IN_STOCK ? '退入库存' : '不退库存' }}
                </abc-table-cell>
            </template>

            <template #batches="{ trData }">
                <abc-table-cell style="height: 100%;">
                    <abc-popover
                        v-if="trData.batchInfos.length"
                        theme="yellow"
                        trigger="hover"
                        style="width: 100%;"
                    >
                        <abc-flex slot="reference" justify="space-between">
                            <abc-text class="ellipsis" :title="trData.batchInfos[0].batchNo">
                                {{ trData.batchInfos[0].batchNo }}
                                <template v-if="trData.batchInfos.length > 1">
                                    ×{{ trData.batchInfos[0].packageCount || trData.batchInfos[0].pieceCount }}
                                </template>
                            </abc-text>
                            <abc-text theme="gray-light">
                                <template v-if="trData.batchInfos.length > 1">
                                    等{{ trData.batchInfos.length }}项
                                </template>
                                <template v-else>
                                    ×{{ trData.batchInfos[0].packageCount || trData.batchInfos[0].pieceCount }}
                                </template>
                            </abc-text>
                        </abc-flex>

                        <abc-flex vertical gap="4">
                            <abc-flex
                                v-for="batchInfo in trData.batchInfos"
                                :key="batchInfo.batchNo"
                                justify="space-between"
                                gap="8"
                            >
                                <abc-text class="ellipsis" :title="batchInfo.batchNo">
                                    {{ batchInfo.batchNo }}
                                </abc-text>
                                <abc-text>
                                    ×{{ batchInfo.packageCount || batchInfo.pieceCount }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>
                    </abc-popover>
                </abc-table-cell>
            </template>

            <template #traceCode="{ trData }">
                <abc-table-cell style="height: 100%;">
                    <abc-popover
                        v-if="trData.traceableCodeList.length"
                        theme="yellow"
                        trigger="hover"
                        style="width: 100%;"
                    >
                        <abc-flex slot="reference" justify="space-between">
                            <abc-text class="ellipsis" :title="trData.traceableCodeList[0].traceCode">
                                {{ trData.traceableCodeList[0].traceCode }}
                                <template v-if="trData.traceableCodeList.length > 1">
                                    ×{{ trData.traceableCodeList[0].count }}
                                </template>
                            </abc-text>
                            <abc-text theme="gray-light">
                                <template v-if="trData.traceableCodeList.length > 1">
                                    等{{ trData.traceableCodeList.length }}项
                                </template>
                                <template v-else>
                                    ×{{ trData.traceableCodeList[0].count }}
                                </template>
                            </abc-text>
                        </abc-flex>

                        <abc-flex vertical gap="4">
                            <abc-flex
                                v-for="traceableCode in trData.traceableCodeList"
                                :key="traceableCode.traceCode"
                                justify="space-between"
                                gap="8"
                            >
                                <abc-text class="ellipsis" :title="traceableCode.traceCode">
                                    {{ traceableCode.traceCode }}
                                </abc-text>
                                <abc-text>
                                    ×{{ traceableCode.count }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>
                    </abc-popover>
                </abc-table-cell>
            </template>
        </abc-table>
    </div>
</template>

<script>
    import TableCellGoods from '@/components/o2o-goods-out-table/table-cell-goods.vue';
    import DialogOrderAfterSale from '@/components/dialog-order-after-sale/index.js';
    import {
        AfterSaleItemStockDealResultType,
    } from '@/utils/constants.js';
    export default {
        name: 'AfterSaleTable',
        components: {
            TableCellGoods,
        },
        props: {
            afterSaleDetail: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                AfterSaleItemStockDealResultType,
            };
        },
        computed: {
            renderConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'orderGoodsInfo',
                            label: '退货商品',
                            style: {
                                flex: '1',
                            },
                        },
                        {
                            key: 'ABCGoods',
                            label: '绑定的ABC系统商品',
                            style: {
                                flex: '1',
                            },
                        },
                        {
                            key: 'dealWay',
                            label: '退货库存处理',
                            style: {
                                flex: 'none',
                                width: '120px',
                            },
                        },
                        {
                            key: 'remark',
                            label: '处理备注',
                            style: {
                                flex: 'none',
                                width: '134px',
                            },
                        },
                        {
                            key: 'batches',
                            label: '批号',
                            style: {
                                flex: 'none',
                                width: '150px',
                            },
                        },
                        {
                            key: 'traceCode',
                            label: '追溯码',
                            style: {
                                width: '260px',
                                maxWidth: '260px',
                            },
                        },
                    ],
                };
            },

            dataList() {
                const res = [];
                if (!this.afterSaleDetail) return res;
                this.afterSaleDetail.items.forEach((it) => {
                    const stock = it.enableOutStockRecord || {};
                    const selectedStock = it.selectedStockRecord || {};

                    const productInfo = stock.hisGoodsInfo || selectedStock.hisGoodsInfo;
                    const selectedStockDetail = selectedStock.stockDetail || {};

                    const dispensingItem = {
                        id: it.id,
                        clinicId: it.clinicId,
                        extGoodsImg: it.extGoodsImg,
                        extGoodsName: it.extGoodsName,
                        extGoodsCount: it.extGoodsCount,
                        extGoodsSkuId: it.extGoodsSkuId,
                        ecGoodsId: it.ecGoodsId,
                        ecGoodsSkuId: it.ecGoodsSkuId,
                        ecGoodsSku: it.ecGoodsSku,
                        unitCount: it.hisReturnStockUnitCount,
                        unit: it.hisGoodsUnit,

                        productId: stock.hisGoodsId,
                        productInfo,
                        useDismounting: stock.useDismounting,
                        packageCount: stock.packageCount,
                        pieceCount: stock.pieceCount,
                        stockId: stock.id,
                        stockDealResultType: it.stockDealResultType,
                        remark: it.remark || '',
                        selectedStockRecordId: selectedStock?.id,
                        traceableCodeList: selectedStockDetail?.traceableCodeList || [],
                        batchInfos: selectedStockDetail?.undispenseBatchInfos || [],
                    };
                    res.push(dispensingItem);
                });
                return res;
            },
        },
        methods: {
            viewAfterSaleDetail() {
                const _dialog = new DialogOrderAfterSale({
                    afterSaleId: this.afterSaleDetail.id,
                    orderNo: this.afterSaleDetail.orderNo,
                    stockDealStatus: this.afterSaleDetail.stockDealStatus,
                    onRefresh: this.$emit('refresh'),
                });
                _dialog.generateDialogAsync({
                    parent: this,
                });
            },
        },
    };
</script>

<style lang="scss">
.after-sale-table {
    width: 100%;

    & + .after-sale-table {
        margin-top: 12px;
    }

    .abc-table-normal-wrapper .abc-table-top-header {
        padding: 0;

        & + .abc-table-header .table-header-col .abc-table-td {
            background: #ffffff;
            border-radius: 0;
        }
    }
}
</style>
