<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        class="refund-batches-selector-dialog"
        :auto-focus="false"
        size="huge"
        title="退药入库确认"
        @close="$emit('close')"
    >
        <abc-layout class="dialog-content clearfix">
            <abc-table
                :need-selected="false"
                :render-config="renderConfig"
                :data-list="batchInfos"
                style="height: 440px;"
            >
                <template #topHeader>
                    <abc-flex justify="space-between" style="width: 100%;">
                        <goods-filed :goods="bindGoodsInfo"></goods-filed>
                        <abc-flex
                            justify="right"
                            align="center"
                            style="min-width: 146px;"
                        >
                            <div style="padding-right: 23px;">
                                可退药入库 {{ bindGoodsInfo.unitCount }} {{ bindGoodsInfo.unit }}
                            </div>
                        </abc-flex>
                    </abc-flex>
                </template>
            </abc-table>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="btnLoading" @click="handleClickConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import GoodsFiled from './goods-filed.vue';
    import ECStockRecordAPI from '../../api/stock-record';

    export default {
        name: 'RefundBatchesSelector',
        components: {
            GoodsFiled,
        },
        props: {
            value: Boolean,
            recordId: {
                type: String,
                required: true,
            },
            onConfirm: {
                type: Function,
                required: true,
            },
        },
        data() {
            return {
                visible: false,
                closed: false,
                contentLoading: false,
                btnLoading: false,
                viewData: {},
            };
        },
        computed: {
            renderConfig() {
                const list = [
                    {
                        'key': 'batchNo',
                        'label': '生产批号',
                        'style': {
                            'flex': '1',
                            'min-width': '120px',
                        },
                    },
                    {
                        'key': 'productionDate',
                        'label': '生产日期',
                        'style': {
                            'flex': '1',
                        },
                    },
                    {
                        'key': 'expiryDate',
                        'label': '效期',
                        'style': {
                            'flex': '1',
                        },
                    },
                    {
                        'key': 'packageCostPrice',
                        'label': '进价',
                        'colType': 'money4',
                        'style': {
                            'flex': '1',
                            'textAlign': 'right',
                        },
                    },
                    {
                        'key': 'inDate',
                        'label': '入库日期',
                        'colType': 'date',
                        'style': {
                            'flex': '1',
                        },
                    },
                    {
                        'key': 'unitCount',
                        'label': '数量',
                        'style': {
                            'width': '120px',
                            'maxWidth': '120px',
                            'textAlign': 'center',
                        },
                    },{
                        'key': 'unit',
                        'label': '单位',
                        'style': {
                            'width': '80px',
                            'maxWidth': '80px',
                            'textAlign': 'center',
                        },
                    },
                ];
                return {
                    hasInnerBorder: false,
                    list,
                };
            },
            bindGoodsInfo() {
                const {
                    bindGoodsInfo,
                } = this.viewData;
                return bindGoodsInfo || {};
            },
            batchInfos() {
                const {
                    batchInfos,
                } = this.bindGoodsInfo;
                return batchInfos || [];
            },
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            this.initDataHandler();
        },
        methods: {
            async initDataHandler() {
                try {
                    this.contentLoading = true;
                    const res = await ECStockRecordAPI.getRecordDetail(this.recordId);
                    this.viewData = res;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
            async handleClickConfirm() {
                try {
                    this.btnLoading = true;
                    this.onConfirm && this.onConfirm();
                    this.closed = true;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }

            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
    .refund-batches-selector-dialog {
        .price {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-width: 100px;
            margin-right: 10px;
        }

        .unit-count-wrap {
            display: flex;
            justify-content: center;
            width: 120px;
            min-width: 120px;
        }

        .unit {
            width: 80px;
            min-width: 80px;
            max-width: 80px;
            padding-right: 4px;
            text-align: center;
        }
    }
</style>
