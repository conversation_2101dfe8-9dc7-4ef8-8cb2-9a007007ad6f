<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        size="medium"
        class="waybill-form-dialog-wrapper"
        :title="title"
        @close="$emit('close')"
    >
        <abc-form
            ref="abcForm"
            v-abc-loading="contentLoading"
            label-width="112"
            item-block
            label-position="left"
        >
            <abc-form-item label="快递公司" required>
                <abc-select
                    v-model="ecMallIdWpCode"
                    :width="260"
                    clearable
                    show-empty
                    :disabled="disabled"
                    empty-text="网店未开通电子面单服务<br/>请在拼多多商家后台开通"
                    @change="handleChangeWpCode"
                >
                    <abc-option
                        v-for="item in waybillInfoList"
                        :key="`${item.ecMallId }|${ item.wpCode}`"
                        :label="`${item.wpName } - ${ item.ecMallName}`"
                        :value="`${item.ecMallId }|${ item.wpCode}`"
                    ></abc-option>
                </abc-select>
            </abc-form-item>
            <abc-form-item v-if="filterBranchAccountList.length" label="网点名称" required>
                <abc-select
                    v-model="postData.branchCode"
                    :disabled="!postData.wpCode || disabled"
                    :width="260"
                    clearable
                    :empty-text="`网店未开通电子面单服务，请在${ECTypeText[ecType]}商家后台开通`"
                    show-empty
                >
                    <abc-option
                        v-for="item in filterBranchAccountList"
                        :key="item.branchCode"
                        :label="item.branchName"
                        :value="item.branchCode"
                    ></abc-option>
                </abc-select>
            </abc-form-item>
            <abc-form-item label="发货地址-取件" required :error="handleShowErrorInfo(selectedShipAddress, '发货地址-取件')">
                <abc-dropdown
                    :disabled="!postData.wpCode || disabled"
                    :max-width="260"
                    :min-width="260"
                    @change="handleShipAddressChange"
                >
                    <abc-edit-div
                        slot="reference"
                        v-model="selectedShipAddress"
                        readonly
                        :disabled="!postData.wpCode || disabled"
                        style="width: 260px; cursor: pointer;"
                    ></abc-edit-div>
                    <abc-dropdown-item
                        v-for="(item,index) in shippAddressList"
                        :key="index"
                        :label="getAddressStr(item)"
                        :value="item"
                    ></abc-dropdown-item>
                </abc-dropdown>
            </abc-form-item>
            <abc-divider size="normal" margin="small" style="margin-bottom: 24px;"></abc-divider>
            <abc-form-item label="纸张尺寸" required>
                <abc-select
                    v-model="postData.standardTemplateId"
                    :disabled="!postData.wpCode || disabled"
                    :width="260"
                    clearable
                >
                    <abc-option
                        v-for="item in standardTemplates"
                        :key="item.standardWaybillId"
                        :label="item.standardTemplateName"
                        :value="item.standardWaybillId"
                    ></abc-option>
                </abc-select>
            </abc-form-item>
            <abc-form-item label="发货地址-展示" required :error="handleShowErrorInfo(selectedContactAddress, '发货地址-展示')">
                <abc-dropdown
                    :max-width="260"
                    :min-width="260"
                    :disabled="disabled"
                    custom-class="waybill-contact-address-dropdown"
                    @change="handleContactAddressChange"
                >
                    <abc-edit-div
                        slot="reference"
                        v-model="selectedContactAddress"
                        readonly
                        :disabled="disabled"
                        style="width: 260px; cursor: pointer;"
                    ></abc-edit-div>
                    <abc-dropdown-item
                        v-for="(item,index) in shipperContactList"
                        :key="index"
                        :value="item"
                    >
                        <abc-flex class="patient" align="center" justify="space-between">
                            <abc-space>
                                <h5>{{ item.name }}</h5>
                                <span style="margin: 0 8px; color: var(--abc-color-T2);">{{ item.mobile }}</span>
                            </abc-space>

                            <abc-button type="text" @click.stop="handleAddShipperContact(item)">
                                修改
                            </abc-button>
                        </abc-flex>
                        <div style="font-size: 12px; color: var(--abc-color-T2); word-break: break-all;">
                            {{ getAddressStr(item.address) }}
                        </div>
                    </abc-dropdown-item>
                    <abc-dropdown-item style="padding: 0;">
                        <div style="padding: 6px 12px; color: var(--abc-color-theme1); text-align: center;" @click="handleAddShipperContact({})">
                            <span>添加发货打印展示地址</span>
                        </div>
                    </abc-dropdown-item>
                </abc-dropdown>
            </abc-form-item>
            <abc-form-item label="打印自定义区域">
                <abc-checkbox v-model="postData.customAreaSwitch" type="number" :disabled="disabled">
                    商品明细
                </abc-checkbox>
            </abc-form-item>
            <abc-divider size="normal" margin="small" style="margin-bottom: 24px;"></abc-divider>
            <abc-form-item label="共享面单">
                <abc-checkbox v-model="postData.shared" type="number" :disabled="disabled">
                    共享给其他网店
                </abc-checkbox>
            </abc-form-item>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-button :disabled="disabled" @click="handleClickConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import ECWayBillAPI from '../../api/way-bill';
    import ECOrderAPI from '../../api/order';
    import DialogContactForm from '../dialog-contact-form';
    import { ECTypeText } from '../../utils/constants';
    import { clone } from '@abc/utils';

    export default {
        name: 'DialogReceiverForm',
        components: {},
        props: {
            value: Boolean,
            disabled: Boolean,
            ecType: {
                type: [String, Number],
                required: true,
            },
            defaultData: {
                type: Object,
            },
            onRefresh: Function,
        },
        data() {
            return {
                ECTypeText,
                visible: false,
                closed: false,
                contentLoading: false,
                shipperContactList: [],
                standardTemplates: [],
                waybillInfoList: [],
                postData: {
                    ecType: this.ecType,
                    ecMallId: '',
                    wpCode: '',
                    branchCode: '',
                    standardTemplateId: '',
                    standardTemplateName: '',
                    standardTemplateUrl: '',
                    standardWaybillType: 0,
                    customAreaSwitch: 0,
                    shared: 0,
                    shipper: {
                        address: {
                            city: '',
                            cityCode: '',
                            detail: '',
                            district: '',
                            districtCode: '',
                            province: '',
                            provinceCode: '',
                        },
                        mobile: '',
                        name: '',
                    },
                    wpShippAddress: {
                        city: '',
                        cityCode: '',
                        detail: '',
                        district: '',
                        districtCode: '',
                        province: '',
                        provinceCode: '',
                    },
                },
            };
        },
        computed: {
            title() {
                return this.postData.id ? '编辑面单模版' : '新建面单模版';
            },
            ecMallIdWpCode: {
                get() {
                    return `${this.postData.ecMallId}|${this.postData.wpCode}`;
                },
                set(val) {
                    const _arr = val.split('|');
                    this.postData.ecMallId = _arr[0];
                    this.postData.wpCode = _arr[1];
                },
            },
            branchAccountList() {
                const res = this.waybillInfoList.find((it) => {
                    return it.wpCode === this.postData.wpCode && it.ecMallId === this.postData.ecMallId;
                });
                return res?.branchAccountList || [];
            },
            filterBranchAccountList() {
                return this.branchAccountList.filter((item) => {
                    return item.branchCode && item.branchName;
                });
            },
            shippAddressList() {
                if (this.filterBranchAccountList.length) {
                    const res = this.filterBranchAccountList.find((item) => {
                        return item.branchCode === this.postData.branchCode;
                    });
                    return res.shippAddressList || [];
                }
                let _arr = [];
                this.branchAccountList.forEach((item) => {
                    _arr = _arr.concat(item.shippAddressList || []);
                });
                return _arr;
            },
            selectedShipAddress() {
                return this.getAddressStr(this.postData.wpShippAddress);
            },
            selectedContactAddress() {
                const {
                    name,
                    mobile,
                    address,
                } = this.postData.shipper || {};
                if (!name) return '';

                return `${name}&nbsp;&nbsp;${mobile}<br/>${this.getAddressStr(address)}`;
            },
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
            filterBranchAccountList(val) {
                if (val && val.length === 1) {
                    this.postData.branchCode = val[0].branchCode;
                }
            },
            shippAddressList(val) {
                if (val && val.length === 1) {
                    this.postData.wpShippAddress = val[0];
                }
            },
        },
        created() {
            this.initHandler();
        },
        methods: {
            async initHandler() {
                try {
                    this.contentLoading = true;
                    await this.fetchEcWaybillInfo();
                    await this.fetchShipperContactList();
                    if (this.defaultData) {
                        this.postData = clone(this.defaultData);
                        this.fetchEcWaybillStandardTemplates();
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
            async fetchEcWaybillInfo() {
                const res = await ECWayBillAPI.fetchEcWaybillInfo({
                    ecType: this.ecType,
                });
                this.waybillInfoList = res.waybillInfoList || [];
                if (this.waybillInfoList && this.waybillInfoList.length === 1) {
                    this.postData.wpCode = this.waybillInfoList[0].wpCode;
                    this.postData.ecMallId = this.waybillInfoList[0].ecMallId;
                    this.fetchEcWaybillStandardTemplates();
                }
            },
            handleShowErrorInfo(val, str) {
                if (!this._showError) {
                    return {
                        error: false,
                    };
                }
                if (!val) {
                    return {
                        error: true,
                        message: `${str}不能为空`,
                    };
                }
                return {
                    error: false,
                };
            },
            isSelectedContact(item) {
                return this.selectedContactAddress === this.getAddressStr(item);
            },
            handleChangeWpCode() {
                this.fetchEcWaybillStandardTemplates();
            },
            handleShipAddressChange(item) {
                Object.assign(this.postData.wpShippAddress, item);
            },
            handleContactAddressChange(item) {
                Object.assign(this.postData.shipper, item);
            },
            async fetchEcWaybillStandardTemplates() {
                const res = await ECWayBillAPI.fetchEcWaybillStandardTemplates({
                    ecType: this.ecType,
                    wpCode: this.postData.wpCode,
                });
                this.standardTemplates = res.rows.filter((it) => it.standardWaybillId);
            },
            async fetchShipperContactList(currentEdit) {
                const res = await ECOrderAPI.fetchShipperContactList();
                this.shipperContactList = res.rows;
                if (currentEdit && currentEdit.id === this.postData.shipper.id) {
                    const res = this.shipperContactList.find((it) => it.id === currentEdit.id);
                    res && this.handleContactAddressChange(res);
                }
            },
            handleAddShipperContact(item) {
                new DialogContactForm({
                    defaultData: item,
                    onRefresh: this.fetchShipperContactList,
                }).generateDialogAsync();
            },
            getAddressStr(item) {
                const {
                    city,
                    detail,
                    district,
                    province,
                } = item;
                const _arr = [];
                if (province) {
                    _arr.push(`${province}`);
                }
                if (city) {
                    _arr.push(`${city}`);
                }
                if (district) {
                    _arr.push(district);
                }
                if (detail) {
                    _arr.push(detail);
                }
                return _arr.join('');
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            handleClickConfirm() {
                this._showError = true;
                this.$refs.abcForm.validate((val) => {
                    if (val) {
                        this.submitHandler();
                    }
                });
            },
            async submitHandler() {
                try {
                    this.buttonLoading = true;
                    const res = this.standardTemplates.find((it) => {
                        return it.standardWaybillId === this.postData.standardTemplateId;
                    });
                    if (res) {
                        this.postData.standardTemplateName = res.standardTemplateName;
                        this.postData.standardTemplateUrl = res.standardTemplateUrl;
                        this.postData.standardWaybillType = res.standardWaybillType;
                    }
                    if (this.postData.id) {
                        await ECWayBillAPI.updateEcWaybillTemplate(this.postData);
                    } else {
                        await ECWayBillAPI.createEcWaybillTemplate(this.postData);
                    }
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                    this.onRefresh && this.onRefresh();
                    this.closed = true;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.buttonLoading = false;
                }

            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.waybill-contact-address-dropdown {
    z-index: 1992;
}

.waybill-form-dialog-wrapper {
    .preview-content {
        padding: 30px;
        overflow: hidden;
        overflow-y: scroll;
        background-color: $P5;
        border-left: 1px solid $P6;

        @include scrollBar;

        .preview-icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 48px;
            height: 48px;
        }

        .way-bill-preview {
            position: relative;
            width: 100%;
            padding: 0;
            text-align: center;

            img {
                width: 100%;
            }

            .mask {
                position: absolute;
                top: 0;
                left: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                font-size: 40px;
                color: #ffffff;
                background-color: rgba(0, 0, 0, 0.5);
            }

            .custom-area {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100px;
                padding: 0 12px;
                font-size: 12px;
                text-align: left;
                background-color: #ffffff;
            }
        }
    }
}
</style>
