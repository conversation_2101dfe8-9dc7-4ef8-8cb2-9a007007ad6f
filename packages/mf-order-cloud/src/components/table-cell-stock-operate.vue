<template>
    <abc-table-cell
        vertical
        align="center"
        justify="center"
    >
        <!--发药出库类型才有操作按钮-->
        <template v-if="type === OutType.DISTRIBUTE">
            <!--正常发货-可以退货入库-->
            <template v-if="status === OutRecordStatus.DELIVERED">
                <abc-tooltip placement="top-end" :disabled="!isChainAdmin" content="请在接单门店操作退药入库">
                    <div>
                        <abc-button
                            variant="text"
                            :disabled="isChainAdmin"
                            @click="handleReturn"
                        >
                            退药入库
                        </abc-button>
                    </div>
                </abc-tooltip>
            </template>
            <!--异常-未绑定可以绑定，绑定了可以发药出库-->
            <template v-if="status === OutRecordStatus.UNBIND">
                <abc-popover
                    v-if="!bindGoodsInfo?.goodsId"
                    placement="top-end"
                    trigger="hover"
                    theme="yellow"
                    :disabled="isAdmin"
                >
                    <div slot="reference">
                        <abc-button
                            variant="text"
                            :disabled="!isAdmin"
                            @click="handleBindGoods"
                        >
                            绑定ABC商品
                        </abc-button>
                    </div>

                    <div>请在连锁总部绑定ABC商品</div>
                </abc-popover>
                <abc-tooltip
                    v-else
                    placement="top-end"
                    :disabled="!isChainAdmin"
                    content="请在接单门店操作发药出库"
                >
                    <div>
                        <abc-button
                            variant="text"
                            :disabled="isChainAdmin"
                            @click="handleDistribute"
                        >
                            发药出库
                        </abc-button>
                    </div>
                </abc-tooltip>
            </template>


            <template v-if="status === OutRecordStatus.SHORTAGE">
                <abc-tooltip
                    placement="top-end"
                    :disabled="!isChainAdmin"
                    content="请在接单门店操作发药出库"
                >
                    <div>
                        <abc-button
                            variant="text"
                            :disabled="isChainAdmin"
                            @click="handleDistribute"
                        >
                            发药出库
                        </abc-button>
                    </div>
                </abc-tooltip>
            </template>

            <template v-if="status === OutRecordStatus.RETURNED">
                <abc-tooltip placement="top-end" content="已操作退药入库">
                    <div>
                        <abc-button
                            icon="s-b-info-circle-line"
                            variant="text"
                            disabled
                        >
                            已退药入库
                        </abc-button>
                    </div>
                </abc-tooltip>
            </template>
        </template>
    </abc-table-cell>
</template>

<script>
    import { mapGetters } from 'vuex';
    import DialogBindGoods from './dialog-bind-goods';
    import {
        OutRecordStatus, OutType,
    } from '../utils/constants';
    import DialogBatchesSelector from 'MfBase/dialog-batches-selector';
    import DialogRefundConfirm from '@/components/dialog-stock-refund-confirm/index.js';
    import ECStockRecordAPI from '../api/stock-record';

    export default {
        name: 'TableCellStockOperate',
        props: {
            bindGoodsInfo: {
                type: Object,
                require: true,
            },
            // 先传进去跑通
            item: {
                type: Object,
                require: true,
            },
            type: {
                type: Number,
                require: true,
            },
            status: {
                type: Number,
                require: true,
            },
            recordId: {
                type: String,
            },
        },
        computed: {
            OutType() {
                return OutType;
            },
            ...mapGetters(['isAdmin', 'isChainAdmin', 'currentClinic']),
            OutRecordStatus() {
                return OutRecordStatus;
            },
            // canEdit() {
            //     return this.item.clinicId === this.currentClinic.clinicId;
            // },
        },
        methods: {
            handleBindGoods() {
                if (!this.isAdmin) return;
                const {
                    clinicId,
                    ecMallId,
                    extGoodsInfo,
                    ecOrder,
                } = this.item;
                new DialogBindGoods({
                    clinicId,
                    ecMallId,
                    mallName: ecOrder?.mallName,
                    goodsSkuId: extGoodsInfo.skuId,
                    goodsId: extGoodsInfo.goodsId,
                    goodsName: extGoodsInfo.goodsName || '-',
                    desc: extGoodsInfo.goodsSpec || '-',
                    hisGoodsList: [],
                }).generateDialogAsync({ parent: this });
            },
            handleDistribute() {
                const {
                    goodsId,
                    unitCount,
                    unit,
                } = this.bindGoodsInfo;
                new DialogBatchesSelector({
                    title: '发药出库确认',
                    customTopHeadRightHtml: `待发药出库 ${unitCount} ${unit}`,
                    chargeItem: {
                        productId: goodsId,
                        unitCount,
                        unit,
                    },
                    fixedCount: unitCount,
                    disabledUnitCount: true,
                    onConfirm: async(data) => {
                        const {
                            unitCount, unit, chargeFormItemBatchInfos,
                        } = data;
                        await ECStockRecordAPI.dispense(this.recordId, {
                            unit,
                            unitCount,
                            productId: goodsId,
                            batchInfos: chargeFormItemBatchInfos,
                        });
                        this.$Toast({
                            message: '操作成功',
                            type: 'success',
                        });
                        this.$emit('refresh-list');
                    },
                }).generateDialog();
            },
            handleReturn() {
                new DialogRefundConfirm({
                    recordId: this.recordId,
                    bindGoodsInfo: this.bindGoodsInfo,
                    onConfirm: async (data) => {
                        await ECStockRecordAPI.unDispense(this.recordId, data);
                        this.$Toast({
                            message: '操作成功',
                            type: 'success',
                        });
                        this.$emit('refresh-list');
                    },
                }).generateDialogAsync();
            },
        },
    };
</script>



