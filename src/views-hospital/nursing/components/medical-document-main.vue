<template>
    <div class="medical-document-main">
        <div class="collapse-container">
            <abc-space class="collapse-operation">
                <template>
                    <abc-tooltip v-if="isAllExpand" placement="top-start" content="全部折叠">
                        <abc-button icon="fold" type="ghost" @click="handleExpand(false)"></abc-button>
                    </abc-tooltip>
                    <abc-tooltip v-else placement="top-start" content="全部展开">
                        <abc-button icon="expand" type="ghost" @click="handleExpand(true)"></abc-button>
                    </abc-tooltip>
                </template>
                <template>
                    <abc-tooltip v-if="isOrderByLog" placement="top-start" content="切换为按日期查看">
                        <abc-button icon="dateview" type="ghost" @click="handleChangeOrderBy('date')"></abc-button>
                    </abc-tooltip>
                    <abc-tooltip v-else placement="top-start" content="切换为按分类查看">
                        <abc-button icon="categoryview" type="ghost" @click="handleChangeOrderBy('type')"></abc-button>
                    </abc-tooltip>
                </template>

                <abc-select v-if="isSupportMedicalVisit" v-model="medicalDocumentRecordType" :width="(enableAddNewDoc && !readonly) ? 160 : 248">
                    <abc-option
                        v-for="item in medicalDocumentRecordTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    ></abc-option>
                </abc-select>

                <template v-if="enableAddNewDoc && !readonly">
                    <medical-document-type-popover
                        v-if="!currentDisableAddNewDoc"
                        ref="medicalDocumentTypePopover"
                        :medical-document-list="medicalTypeList"
                        :medical-record-list="medicalRecordList"
                        :business-type="businessType"
                        @select="handleSelectMedical"
                        @batch-select="handleBatchSelectMedical"
                        @popover-show="fetchMedicalDocumentList"
                    >
                        <abc-button type="success" icon="a-plus13px">
                            病历
                        </abc-button>
                    </medical-document-type-popover>

                    <abc-tooltip v-else placement="top-start" :content="currentDisableAddNewDocTips">
                        <abc-button disabled type="success" icon="a-plus13px">
                            病历
                        </abc-button>
                    </abc-tooltip>
                </template>
            </abc-space>
            <medical-document-collapse
                ref="medicalDocumentCollapse"
                :business-id="currentBusinessId"
                :business-type="currentBusinessType"
                :department-id="departmentId"
                :default-collapse="defaultCollapse"
                :has-daily="enableDailyCollapse"
                :enable-medical-settlement-form="currentEnableMedicalSettlementForm"
                :medical-settlement-form-upload-error="currentMedicalSettlementFormUploadError"
                :medical-record-list.sync="medicalRecordList"
                @change-collapse-item="handleChangeCollapseItemDebounce"
                @update-all-expand="handleChangeExpandAll"
                @batch-print="handleBatchPrint"
            ></medical-document-collapse>
        </div>

        <template v-if="collapse">
            <div
                v-if="currentMedicalDocumentView === MedicalDocumentViewMode.MedicalDocument"
                class="medical-document-preview-container"
            >
                <div ref="previewHeader" class="medical-document-preview_header">
                    <div class="medical-document-preview_header-title">
                        {{ collapse?.name }}
                    </div>

                    <div style="margin-left: auto;">
                        <abc-space>
                            <template v-if="!enableSendback">
                                <abc-tooltip
                                    placement="top"
                                    :disabled="!disableModifyDoc || !disableModifyDocTips"
                                    :content="disableModifyDocTips"
                                >
                                    <div v-if="!readonly && !isHisAdmissionHistory">
                                        <abc-button
                                            :disabled="disableModifyDoc"
                                            :loading="editLoading"
                                            type="blank"
                                            @click="handleClickEditButton"
                                        >
                                            填写
                                        </abc-button>
                                    </div>
                                </abc-tooltip>
                                <abc-dropdown v-if="showAddMedicalBtn && !readonly && !isHisAdmissionHistory" @change="handleAddShareMedicalPage">
                                    <abc-button slot="reference" type="blank">
                                        新增
                                    </abc-button>
                                    <abc-dropdown-item v-for="item in shareMedicalList" :key="item.id" :value="item.id">
                                        {{ item.name }}
                                    </abc-dropdown-item>
                                </abc-dropdown>
                                <abc-button type="blank" @click="printMedicalDocument">
                                    打印
                                </abc-button>
                                <abc-tooltip
                                    placement="top"
                                    :disabled="!disableDeleteDoc || !disableDeleteDocTips"
                                    :content="disableDeleteDocTips"
                                >
                                    <div v-if="!readonly && !isHisAdmissionHistory">
                                        <abc-button
                                            :loading="deleteLoading"
                                            type="danger"
                                            :disabled="disableDeleteDoc"
                                            @click="handleClickDeleteButton"
                                        >
                                            删除
                                        </abc-button>
                                    </div>
                                </abc-tooltip>
                            </template>
                            <template v-else>
                                <abc-button
                                    v-if="![MedicalDocumentArchiveStatus.SEND_BACK].includes(collapse?.archiveStatus)"
                                    type="danger"
                                    @click="handleClickSendBack"
                                >
                                    退回
                                </abc-button>
                                <abc-button type="blank" @click="printMedicalDocument">
                                    打印
                                </abc-button>
                            </template>
                        </abc-space>
                    </div>
                    <div
                        v-if="enableShowBackReason && collapse?.backReason && collapse.archiveStatus === MedicalDocumentArchiveStatus.SEND_BACK"
                        class="medical-document-back-reason"
                    >
                        <abc-space>
                            <abc-icon icon="jinggao"></abc-icon>
                            <span class="medical-document-back-reason_content">病历退回：{{ collapse?.backReason }}</span>
                        </abc-space>
                    </div>
                </div>
                <div
                    ref="medicalDocumentPreviewContent"
                    v-abc-loading="emrTemplateContentLoading"
                    class="medical-document-preview_content"
                    :style="{
                        paddingRight: enableQualityControlMain && showQualityControl ? '320px' : '0', maxWidth: contentMaxWidth ? `${contentMaxWidth}px` : 'none',
                    }"
                >
                    <emr-editor
                        v-if="currentDisplayTemplateContent && currentDisplayTemplateSetting"
                        ref="emrEditor"
                        :key="collapse?.id"
                        :value="currentDisplayTemplateContent"
                        :editable="false"
                        :page-size="currentDisplayTemplateSetting.pageSize"
                        :page-size-reduce="currentDisplayTemplateSetting.pageSizeReduce"
                        :page-orientation="currentDisplayTemplateSetting.pageOrientation"
                        :mode="EditorModeEnum.Use"
                        :enable-embed-doc-view="true"
                        :enable-focus-change="showQualityControl"
                        @getSignCode="handleGetSignCode"
                        @initCareTableDataHandler="handleInitCareTableDataHandler"
                        @mountedEditor="handleMountedEditor"
                        @onTransaction="handleEditorTransaction"
                        @changeFieldFocus="handleChangeFieldFocus"
                    ></emr-editor>
                </div>

                <emr-editor-quality-control-panel
                    v-if="enableQualityControlMain && showQualityControl"
                    :week-validate-editor-fields="weekValidateEditorFields"
                    :strong-validate-editor-fields="strongValidateEditorFields"
                    :ref-fields-diff="refFieldsDiff"
                    :focus-field-index="focusFieldIndex"
                    :enable-update-field-diff-content="enableUpdateFieldDiffContent"
                    @update-diff="handleUpdateDiff"
                    @update-single-diff="handleUpdateSingleDiff"
                    @scroll-to-field-by-index="handleScrollToFieldByIndex"
                    @close="handleCloseQualityControl"
                ></emr-editor-quality-control-panel>

                <div v-if="!showQualityControl && enableQualityControlMain" class="medical-document-main_float-btn" @click="handleClickShowQualityControl">
                    <span class="medical-document-main_float-count">{{ qualityControlCount }}</span>
                    <span class="medical-document-main_float-name">病历质控</span>
                </div>
            </div>

            <div v-if="currentMedicalDocumentView === MedicalDocumentViewMode.Daily" class="daily-preview-container">
                <nursing-daily
                    :patient-order-id="patientOrderId"
                    :patient-id="patientId"
                    :daily-type-id="collapse.id"
                    :disable-daily-module-modify="disableDailyModuleModify || isHisAdmissionHistory"
                    :enable-setup-temperature="enableSetupTemperature"
                    :enable-setup-blood-sugar="enableSetupBloodSugar"
                    :in-patient-detail-info="inPatientDetailInfo"
                ></nursing-daily>
            </div>

            <div
                v-if="currentMedicalDocumentView === MedicalDocumentViewMode.SettlementForm"
                class="medical-document-preview-container"
            >
                <div class="medical-document-preview_header">
                    <div class="medical-document-preview_header-title">
                        {{ collapse?.name }}
                    </div>
                    <div style="margin-left: auto;">
                        <abc-space>
                            <div v-if="currentEnableMedicalSettlementForm && $abcSocialSecurity.isShowDrgOrDipGrpResult">
                                <abc-button
                                    v-abc-check-electron
                                    @click="handleClickGetGroupResult"
                                >
                                    分组信息
                                </abc-button>
                            </div>
                            <div v-if="currentEnableMedicalSettlementForm">
                                <abc-button
                                    v-abc-check-electron
                                    @click="handleClickUploadMedicalSettlementForm"
                                >
                                    上报
                                </abc-button>
                            </div>
                            <abc-tooltip
                                placement="top"
                                content="清单问题请退回首页进行修改"
                            >
                                <div>
                                    <abc-button disabled>
                                        退回
                                    </abc-button>
                                </div>
                            </abc-tooltip>

                            <abc-button type="blank" @click="printSettlementForm">
                                打印
                            </abc-button>
                        </abc-space>
                    </div>

                    <div
                        v-if="settlementForm.uploadResult"
                        class="medical-document-back-reason"
                    >
                        <abc-space>
                            <abc-icon icon="jinggao"></abc-icon>
                            <span class="medical-document-back-reason_content">
                                上报失败：{{ settlementForm.uploadResult }}
                            </span>
                        </abc-space>
                    </div>
                </div>
                <div class="medical-document-preview_content">
                    <div class="medical-document-preview_settlement-form">
                        <iframe
                            v-if="settlementForm.html"
                            ref="iframe"
                            :srcdoc="settlementForm.html"
                            frameborder="0"
                            scrolling="no"
                            onload="this.style.height=this.contentWindow.document.body.scrollHeight+'px'"
                        ></iframe>
                    </div>
                </div>
            </div>

            <div>
                <emr-editor-use-dialog
                    v-if="emrEditorUseDialogVisible"
                    v-model="emrEditorUseDialogVisible"
                    :content="currentOperationTemplateContent"
                    :page-size="currentOperationTemplateSetting.pageSize"
                    :page-size-reduce="currentOperationTemplateSetting.pageSizeReduce"
                    :page-orientation="currentOperationTemplateSetting.pageOrientation"
                    :template-setting="currentOperationTemplateSetting"
                    :history="medicalHistoryList"
                    :patient-order-id="patientOrderId"
                    :template-id="collapse.id"
                    :medical-doc-id="collapse.id"
                    :medical-id="collapse.medicalId"
                    :default-name="emrTemplateOperationEditorData.name"
                    :department-id="departmentId"
                    :business-id="businessId"
                    :shared-page-id="sharedPageId"
                    :business-type="businessType"
                    :patient-info="patientInfo"
                    :ref-key-service="refKeyService"
                    :emr-template-record-list="emrOperationTemplateRecordList"
                    :is-shared-page="!!sharedPageId"
                    :is-add-new-doc="isAddNewDoc"
                    :default-care-table-data="careTableData.data"
                    :patient-id="patientId"
                    :mode="useDialogMode"
                    :enable-quality-control-using-dialog="enableQualityControlUsingDialog"
                    :medical-document-type="currentType"
                    :get-print-options="getPrintOptions"
                    :patient-inpatient-history="patientInpatientHistory"
                    @submit="handleSubmit"
                    @auto-save="handleAutoSave"
                    @save-catalogue="handleSaveCatalogue"
                    @close="handleCloseUseDialog"
                    @fetch-record-list="handleFetchRecordList"
                    @refresh-record-list="initMedicalList"
                ></emr-editor-use-dialog>
            </div>

            <div>
                <medical-document-sendback-dialog
                    v-if="sendbackDialogVisible"
                    v-model="sendbackDialogVisible"
                    @confirm="handleSubmitSendback"
                ></medical-document-sendback-dialog>
            </div>


            <!--用于打印-->
            <div v-if="printCollapse" style="display: none;">
                <nursing-daily
                    ref="printComponent"
                    :key="printCollapse.id"
                    :patient-order-id="patientOrderId"
                    :patient-id="patientId"
                    :daily-type-id="printCollapse.id"
                    :disable-daily-module-modify="disableDailyModuleModify"
                    :enable-setup-temperature="enableSetupTemperature"
                    :enable-setup-blood-sugar="enableSetupBloodSugar"
                    :in-patient-detail-info="inPatientDetailInfo"
                ></nursing-daily>
            </div>
        </template>
    </div>
</template>

<script type="text/ecmascript-6">
    import MedicalDocumentCollapse from '@/views-hospital/nursing/components/medical-document-collapse.vue';
    import MedicalDocumentTypePopover from '@/views-hospital/nursing/components/medical-document-type-popover.vue';
    import EmrAPI from 'api/hospital/emr';
    import {
        getCreateMedicalDocumentDetail,
        handleGetSignCode,
        handlePrintEmr,
        handlePrintSettlementForm,
        mergeCommonTemplate,
        mergeRemoteRefFieldsData,
    } from '@/views-hospital/medical-prescription/components/emr-editor/common/tools';
    import {
        clearFields,
    } from '@abc-emr-editor/tools';
    import {
        AttributesEnum, EditorModeEnum, ExtensionTypeEnum,
    } from '@abc-emr-editor/constants';
    import {
        LocalMedicalDocumentCollapseId,
        MedicalDocumentBusinessType,
        MedicalDocumentRecordType,
        MedicalDocumentRecordTypeOptions,
        MedicalDocumentViewMode, PrintDocumentTaskType,
    } from '@/views-hospital/nursing/common/constants';
    import NursingDaily from '@/views-hospital/nursing/components/nursing-daily.vue';
    import {
        CATALOGUE_FILE_OWNER_TYPE, TemplateCategoryEnum, TemplateSceneTypeEnum,
    } from 'utils/constants';
    import { CategoryTemplateAPI } from 'api/catalogue-template';
    import {
        mapGetters,
    } from 'vuex';
    import {
        AbcEmrEditorService,
    } from '@/views-hospital/medical-prescription/components/emr-editor/AbcEmrEditorService.js';
    import Clone from 'utils/clone';
    import { debounce } from 'utils/lodash';
    import { AbcEmrEditor } from '@/views-hospital/medical-prescription/components/emr-editor/AbcEmrEditor';
    import {
        EnableBreakAtFirstPageType,
        EnableBreakPageHeaderAndFooterType,
        MedicalDocumentTagEnum,
    } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants';
    import MedicalRecordManagementAPI from 'api/hospital/medical-record-management';
    import {
        MedicalDocumentArchiveStatus,
        SettlePayModeEnum,
    } from '@/views-hospital/medical-record-management/utils/constants';
    import MedicalDocumentSendbackDialog from '@/views-hospital/nursing/components/medical-document-sendback-dialog';
    import { isDev } from '@/assets/configure/build-env';
    import EmrEditorQualityControlPanel
        from '@/views-hospital/medical-prescription/components/emr-editor/emr-editor-quality-control-panel';
    import MedicalRefFieldMixin from '@/views-hospital/nursing/mixins/medical-ref-field';
    import { isEqual } from '@abc/utils';
    import {
        getBusinessId, getBusinessType,
    } from '@/views-hospital/nursing/utils';
    import {
        MedicalDocumentRecordTypeEnum,
    } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { formatDate } from '@abc/utils-date';

    export default {
        name: 'MedicalDocumentMain',
        components: {
            MedicalDocumentCollapse,
            EmrEditor: AbcEmrEditorService.loadEmrEditor,
            EmrEditorUseDialog: () => import('@/views-hospital/medical-prescription/components/emr-editor/emr-editor-using-dialog.vue'),
            NursingDaily,
            MedicalDocumentTypePopover,
            MedicalDocumentSendbackDialog,
            EmrEditorQualityControlPanel,
        },
        mixins: [MedicalRefFieldMixin],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            readonly: {
                type: Boolean,
                default: false,
            },
            patientId: {
                type: String,
                default: '',
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            departmentId: {
                type: String,
                default: '',
            },
            businessId: {
                type: String,
                default: '',
            },
            businessType: {
                type: Number,
                default: MedicalDocumentBusinessType.HOSPITAL,
            },
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
            refKeyService: {
                type: Object,
                default: null,
            },
            defaultCollapse: {
                type: Object,
                default: null,
            },
            currentQuickItem: {
                type: Object,
                default: () => {
                },
            },
            // 使用弹窗质控
            enableQualityControlUsingDialog: {
                type: Boolean,
                default: false,
            },
            // 主页面质控
            enableQualityControlMain: {
                type: Boolean,
                default: false,
            },
            // 医保结算清单
            enableMedicalSettlementForm: {
                type: Boolean,
                default: false,
            },
            // 退回
            enableSendback: {
                type: Boolean,
                default: false,
            },
            // 日常
            enableDailyCollapse: {
                type: Boolean,
                default: true,
            },
            // 禁止日常编辑
            disableDailyModuleModify: {
                type: Boolean,
                default: false,
            },
            // 退回原因
            enableShowBackReason: {
                type: Boolean,
                default: false,
            },
            // 允许更新field的内容
            enableUpdateFieldDiffContent: {
                type: Boolean,
                default: true,
            },
            // 允许添加文书
            enableAddNewDoc: {
                type: Boolean,
                default: true,
            },
            // 禁止文书新增
            disableAddNewDoc: {
                type: Boolean,
                default: false,
            },
            // 禁止文书新增提示
            disableAddNewDocTips: {
                type: String,
                default: '',
            },
            // 禁止文书删除
            disableDeleteDoc: {
                type: Boolean,
                default: false,
            },
            // 禁止文书删除提示
            disableDeleteDocTips: {
                type: String,
                default: '',
            },
            // 禁止文书填写
            disableModifyDoc: {
                type: Boolean,
                default: false,
            },
            // 禁止文书填写提示
            disableModifyDocTips: {
                type: String,
                default: '',
            },
            // 支持设置体温单
            enableSetupTemperature: {
                type: Boolean,
                default: false,
            },
            // 支持设置血糖单
            enableSetupBloodSugar: {
                type: Boolean,
                default: false,
            },
            inPatientDetailInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                emrEditorUseDialogVisible: false,
                emrDisplayTemplateRecordList: [],
                emrOperationTemplateRecordList: [],
                sharedPageId: '',
                shareMedicalList: [],
                emrTemplateContentLoading: false,
                editLoading: false,
                deleteLoading: false,
                medicalHistoryList: [],
                collapse: null,
                currentMedicalDocumentView: MedicalDocumentViewMode.MedicalDocument,
                MedicalDocumentViewMode,
                MedicalDocumentBusinessType,
                EditorModeEnum,
                medicalTypeList: [],
                isAllExpand: true, // 是否展开或者折叠
                isOrderByLog: true, // 是否根据目录排序
                isAddNewDoc: false, // 是否为新增文档
                emrEditor: new AbcEmrEditor(),
                setCareTableData: () => {
                },
                getCareTableData: () => [],
                careTableData: {
                    data: [],
                    options: {},
                },
                currentTag: 0,
                currentType: 0,
                currentPrintPosition: null,
                sendbackDialogVisible: false,
                settlementForm: {
                    html: '',
                    uploadResult: '',
                },
                showQualityControl: false,
                contentMaxWidth: undefined,
                medicalRecordList: [],
                medicalDocumentRecordType: MedicalDocumentRecordType.CURRENT,
                hasImportMedicalRecords: false,
                importMedicalRecords: [],

                printCollapse: null,

                patientInpatientHistory: [], // 入院历史
            };
        },
        computed: {
            ...mapGetters(['userInfo']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            MedicalDocumentArchiveStatus() {
                return MedicalDocumentArchiveStatus;
            },
            // 展示数据
            currentDisplayTemplateContent() {
                return this.emrTemplateDisplayEditorData?.templateContent;
            },
            currentDisplayTemplateSetting() {
                return this.emrTemplateDisplayEditorData?.templateSetting;
            },
            emrTemplateDisplayEditorData() {
                return this.generateEmrTemplateDataForEditor(this.emrDisplayTemplateRecordList);
            },
            // 操作数据
            currentOperationTemplateContent() {
                return this.emrTemplateOperationEditorData?.templateContent;
            },
            currentOperationTemplateSetting() {
                return this.emrTemplateOperationEditorData?.templateSetting;
            },
            emrTemplateOperationEditorData() {
                return this.generateEmrTemplateDataForEditor(this.emrOperationTemplateRecordList);
            },
            medicalDocumentRecordTypeOptions() {
                const base = MedicalDocumentRecordTypeOptions[this.businessType].filter((it) => {
                    return it.value !== MedicalDocumentRecordType.IMPORT || this.hasImportMedicalRecords;
                });

                const _current = base.filter((o) => o.value === MedicalDocumentRecordType.CURRENT);
                const _import = base.filter((o) => o.value === MedicalDocumentRecordType.IMPORT);

                const history = this.patientInpatientHistory.filter((o) => {
                    return o.id !== this.businessId;
                }).sort((a, b) => {
                    // Sort by date in descending order (most recent first)
                    return new Date(b.inpatientTime) - new Date(a.inpatientTime);
                }).map((o) => {
                    return {
                        label: `${formatDate(o.inpatientTime, 'YYYY-MM-DD')} 入院`,
                        value: `${MedicalDocumentRecordType.CURRENT}_${o.id}`,
                    };
                });

                return [
                    ..._current,
                    ...history,
                    ..._import,
                ];
            },
            // 是否支持本次就诊
            isSupportMedicalVisit() {
                return this.viewDistributeConfig.EMR.isSupportMedicalVisit;
            },
            showAddMedicalBtn() {
                return !!this.sharedPageId;
            },
            useDialogMode() {
                return this.currentTag & MedicalDocumentTagEnum.ReadOnly ? EditorModeEnum.Form : EditorModeEnum.Use;
            },
            currentEnableMedicalSettlementForm() {
                return this.enableMedicalSettlementForm && this.currentQuickItem?.settlePayMode === SettlePayModeEnum.HEALTH_CARD;
            },
            currentMedicalSettlementFormUploadError() {
                return !!(this.currentEnableMedicalSettlementForm && this.settlementForm.uploadResult);
            },
            enableValidateFields() {
                return this.enableQualityControlMain && this.showQualityControl;
            },

            currentBusinessId() {
                return getBusinessId(this.medicalDocumentRecordType, this.businessId, this.patientId);
            },

            currentBusinessType() {
                return getBusinessType(this.businessType, this.medicalDocumentRecordType);
            },
            currentDisableAddNewDoc() {
                return this.disableAddNewDoc ||
                    this.medicalDocumentRecordType === MedicalDocumentRecordType.IMPORT ||
                    this.isHisAdmissionHistory;
            },

            isHisAdmissionHistory() {
                return typeof this.medicalDocumentRecordType === 'string' && this.medicalDocumentRecordType.startsWith(`${MedicalDocumentRecordType.CURRENT}_`);
            },

            currentDisableAddNewDocTips() {
                if (this.isHisAdmissionHistory) {
                    return '只能为本次住院添加病历';
                }

                if (this.medicalDocumentRecordType === MedicalDocumentRecordType.IMPORT) {
                    return '导入历史病历不支持新增';
                }
                return this.disableAddNewDocTips;
            },
        },
        watch: {
            'currentQuickItem.emrArchiveStatus': function () {
                // 当文书QL状态发生变更时需要更新患者文书列表状态
                if (this.patientOrderId !== this.currentQuickItem.id) {
                    return;
                }
                isDev && console.warn('更新患者文书列表状态');
                this.fetchPatientMedicalList();
            },
            showQualityControl(val) {
                this.emrEditor.setEnableFocusChange(val);
            },
            collapse: {
                handler() {
                    const headerWidth = this.$refs.previewHeader?.getBoundingClientRect().width;
                    if (headerWidth) {
                        this.contentMaxWidth = headerWidth;
                    }
                },
                deep: true,
            },

            patientId: {
                handler(val) {
                    if (!val) return;

                    this.fetchPatientOrderList(val);
                },
                immediate: true,
            },
        },
        created() {
            this.handleChangeCollapseItemDebounce = debounce(this.handleChangeCollapseItem, 100, true);
            this.checkHasImportMedicalRecords();
        },

        methods: {
            handleGetSignCode,

            async fetchPatientOrderList(patientId) {
                const res = await PatientOrderAPI.getInHospitalList({ patientId });
                this.patientInpatientHistory = res?.data?.rows || [];
            },

            updateFieldsNext() {
                if (this.enableValidateFields) {
                    this.setFieldValidate();
                }
            },
            getEmrEditorContainer() {
                return this.$refs.medicalDocumentPreviewContent;
            },
            getEmrEditor() {
                return this.emrEditor;
            },
            handleUpdateSingleDiff(diff) {
                this.handleScrollToFieldByIndex(diff[1].index);
            },
            handleMountedEditor(editor) {
                this.emrEditor.init(editor);
            },
            getPrintOptions() {
                const isSupportMedicalDocumentContinuePrintMode = this.currentType === MedicalDocumentRecordTypeEnum.MedicalRecordNote;
                return {
                    isSupportMedicalDocumentContinuePrintMode,
                    defaultPosition: this.currentPrintPosition,
                    onUpdatePrintPosition: async (position) => {
                        try {
                            this.currentPrintPosition = position;
                            if (!this.isAddNewDoc) {
                                await EmrAPI.updatePrintPosition(
                                    this.collapse.id,
                                    {
                                        businessId: this.businessId,
                                        businessType: this.businessType,
                                        medicalDocId: this.collapse.id,
                                        printPosition: position,
                                    },
                                );
                            }
                        } catch (e) {
                            console.error(e);
                        }
                    },
                };
            },
            printMedicalDocument() {
                this.$refs.emrEditor.print(handlePrintEmr, {
                    // 允许第一页强制分页
                    // 可以理解为病案首页的特殊处理
                    enableBreakAtFirstPage: EnableBreakAtFirstPageType.includes(this.currentType),
                    enableBreakPageHeaderAndFooter: EnableBreakPageHeaderAndFooterType.includes(this.currentType),
                }, this.getPrintOptions());
            },
            async handleClickEditButton() {
                try {
                    this.editLoading = true;
                    this.isAddNewDoc = false;
                    this.currentTag = this.collapse?.medical?.tag;
                    this.currentType = this.collapse?.medical?.type;
                    this.emrOperationTemplateRecordList = Clone(this.emrDisplayTemplateRecordList);
                    this.emrEditorUseDialogVisible = true;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.editLoading = false;
                }
            },
            async handleClickDeleteButton() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除该文书？',
                    onConfirm: async () => {
                        try {
                            this.deleteLoading = true;

                            // 删除护理记录单
                            // const careTableData = this.getCareTableData();
                            // if (this.currentType === MedicalDocumentRecordTypeEnum.CareTableRecord && careTableData.length) {
                            //     await EmrAPI.updateNursingRecordList({
                            //         list: careTableData.map((it) => ({
                            //             ...it,
                            //             opType: 2,
                            //         })),
                            //         patientId: this.patientId,
                            //         patientOrderId: this.patientOrderId,
                            //     });
                            //     this.careTableData.data = [];
                            // }
                            await EmrAPI.deleteMedicalDocument({
                                businessType: this.currentBusinessType,
                                businessId: this.currentBusinessId,
                                medicalDocId: this.collapse.id,
                            });
                            await this.initMedicalList();

                            this.currentPrintPosition = null;
                        } catch (e) {
                            console.error(e);
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        } finally {
                            this.deleteLoading = false;
                        }
                    },
                });
            },
            handleChangeCollapseItem(collapse) {
                this.collapse = collapse;
                if (!this.collapse) {
                    return;
                }
                if ([LocalMedicalDocumentCollapseId.Temperature, LocalMedicalDocumentCollapseId.BloodSugar].includes(collapse.id)) {
                    this.currentMedicalDocumentView = MedicalDocumentViewMode.Daily;
                } else if (LocalMedicalDocumentCollapseId.MedicalSettlementForm === collapse.id) {
                    this.currentMedicalDocumentView = MedicalDocumentViewMode.SettlementForm;
                } else {
                    this.currentMedicalDocumentView = MedicalDocumentViewMode.MedicalDocument;
                    const isSameSharedPage = this.emrDisplayTemplateRecordList.some((it) => it.id === collapse.id);
                    if (this._isUpdateMedicalList) {
                        console.log('强制更新文书列表');
                    }
                    if (!isSameSharedPage || this._isUpdateMedicalList) {
                        this.fetchMedicalDocumentAllSource().then(() => {
                            this._isUpdateMedicalList = false;
                        });
                    } else {
                        console.log('不需要更新文书列表');
                        this.locationToActiveMedicalRecord();
                    }
                }
                // 获取结算清单
                this.fetchSettlementForm();

                // 设置文书type和tag
                this.currentTag = this.collapse?.medical?.tag;
                this.currentType = this.collapse?.medical?.type;
            },
            getFetchMedicalDocumentAllSourceParams() {
                return [this.collapse.medicalId, this.collapse?.id];
            },
            /**
             * @desc 获取编辑过的文书信息
             * <AUTHOR>
             * @date 2023-09-03 17:35:36
             */
            async fetchMedicalDocumentAllSource() {
                try {
                    this.emrTemplateContentLoading = true;
                    const params = this.getFetchMedicalDocumentAllSourceParams();
                    const { data } = await EmrAPI.fetchMedicalDocDetail(this.currentBusinessType, this.currentBusinessId, ...params);
                    if (!isEqual(params, this.getFetchMedicalDocumentAllSourceParams())) {
                        console.warn('getFetchMedicalDocumentAllSourceParams: 请求已过期');
                        return;
                    }
                    this.sharedPageId = data.sharedPageId;
                    this.emrDisplayTemplateRecordList = data.medicalDocDetailViews ?? [];
                    this.currentPrintPosition = this.emrDisplayTemplateRecordList[0]?.printPosition;
                    await this.fetchShareMedicalList();
                    await this.locationToActiveMedicalRecord();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.emrTemplateContentLoading = false;
                }
            },

            async locationToActiveMedicalRecord() {
                await this.$nextTick();
                // 共享文书才设置高亮
                if (this.sharedPageId) {
                    const editor = await this.emrEditor.getEditor();
                    editor.commands.setEmbedDocViewFocusById(this.collapse.id);
                    const highlightDOM = this.$el.querySelector(`embed-doc-view[data-id="${this.collapse.id}"]`);
                    if (highlightDOM && this.$refs.medicalDocumentPreviewContent) {
                        // 滚动到可视区域
                        this.$refs.medicalDocumentPreviewContent.scrollTop = highlightDOM.offsetTop;
                    }
                } else {
                    // 切换时要切到0
                    if (this.$refs.medicalDocumentPreviewContent) {
                        this.$refs.medicalDocumentPreviewContent.scrollTop = 0;
                    }
                }
            },

            generatePostData(editorContent) {
                const { content } = editorContent;
                return {
                    businessId: this.currentBusinessId,
                    businessType: this.currentBusinessType,
                    emrMedicalDocs: content.map((it) => {
                        const id = it.attrs[AttributesEnum.ID];
                        const medicalId = this.emrOperationTemplateRecordList.find((item) => item.id === id)?.medicalId;
                        return {
                            content: {
                                templateContent: {
                                    type: 'doc',
                                    content: it.content,
                                },
                                templateSetting: this.currentOperationTemplateSetting ?? this.currentDisplayTemplateSetting,
                            },
                            medicalId,
                            id,
                        };
                    }),
                };
            },
            async handleAutoSave(editorContent, onSuccess, onError) {
                try {
                    await EmrAPI.creatMedicalDocument(Object.assign(this.generatePostData(editorContent), { isDraft: 1 }));
                    onSuccess();
                } catch (e) {
                    onError();
                    console.error(e);
                }
            },
            async handleSaveCatalogue(editorContent, name, onSuccess, onError) {
                const postData = {
                    name,
                    category: TemplateCategoryEnum.PUBLIC_CLINIC,
                    type: this.businessType === MedicalDocumentBusinessType.HOSPITAL ?
                        TemplateSceneTypeEnum.HOSPITAL_EMR_MEDICAL_DOC :
                        TemplateSceneTypeEnum.OUTPATIENT_EMR_MEDICAL_DOC,
                    ownerType: CATALOGUE_FILE_OWNER_TYPE.PERSONAL,
                    file: {
                        combineIds: [],
                        content: {
                            templateContent: editorContent,
                            templateSetting: this.currentDisplayTemplateSetting,
                        },
                        name,
                    },
                    isFolder: 0,
                    ownerId: this.userInfo.id,
                    parentId: this.collapse.id,
                    sort: 0,
                };

                // 取出母版id
                if (Array.isArray(postData.file.content.templateContent.content)) {
                    const combineIds = [];
                    postData.file.content.templateContent.content.forEach((it) => {
                        if ([ExtensionTypeEnum.HEADER, ExtensionTypeEnum.CONTENT, ExtensionTypeEnum.FOOTER].includes(it.type)) {
                            // 清空内容不用于保存
                            combineIds.push(it.attrs[AttributesEnum.CombineId]);
                            delete it.content;
                        }
                    });

                    postData.file.combineIds = combineIds;
                }

                // 清空引用值
                await clearFields(postData.file.content.templateContent);

                try {
                    await CategoryTemplateAPI.createCatalogue(postData);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    onSuccess && onSuccess();
                } catch (e) {
                    console.warn('保存失败', e);
                    onError && onError(e);
                }
            },
            async handleSubmit(editorContent, hideLoading, showToast = true, currentPrintPosition) {
                try {
                    const { data } = await EmrAPI.creatMedicalDocument(Object.assign(this.generatePostData(editorContent), { isDraft: 0 }));
                    await this.refreshRecordListData();
                    await this.initMedicalList();

                    this.emrEditorUseDialogVisible = false;

                    // 选中新增的数据
                    if (this.isAddNewDoc) {
                        const { emrMedicalDocRsps } = data;
                        const addNewDocId = emrMedicalDocRsps.find((doc) => {
                            return !doc.operationType;
                        })?.id;
                        if (addNewDocId) {
                            this.$refs.medicalDocumentCollapse.activeCollapseItemId = addNewDocId;
                            this.$refs.medicalDocumentCollapse.handleSelectOne();

                            if (currentPrintPosition) {
                                await EmrAPI.updatePrintPosition(
                                    addNewDocId,
                                    {
                                        businessId: this.businessId,
                                        businessType: this.businessType,
                                        medicalDocId: addNewDocId,
                                        printPosition: currentPrintPosition,
                                    },
                                );
                            }
                        }
                    }
                    showToast && this.$Toast({
                        message: '提交成功',
                        type: 'success',
                    });
                } catch (e) {
                    console.error(e);
                    this.$Toast({
                        message: '提交失败',
                        type: 'error',
                    });
                } finally {
                    hideLoading();
                }
            },
            /**
             * @desc 获取门店的文书列表
             * <AUTHOR>
             * @date 2023-09-03 11:19:59
             * @params
             * @return
             */
            async fetchMedicalDocumentList() {
                try {
                    if (!this.enableAddNewDoc) {
                        return;
                    }
                    const { data: { emrCatalogueViews = [] } } = await EmrAPI.fetchMedicalDocumentListWithPermission(this.businessType);
                    this.medicalTypeList = emrCatalogueViews;
                } catch (e) {
                    this.medicalTypeList = [];
                }
            },

            /**
             * @desc 批量选择文书 批量创建
             */
            async handleBatchSelectMedical(medicalIds) {
                this._batchCreatingLoading = this.$Loading({
                    text: '批量添加中',
                });
                for (let i = 0; i < medicalIds.length; i++) {
                    try {
                        const currentMedicalId = medicalIds[i];
                        const { data } = await EmrAPI.fetchMedicalDocDetail(this.currentBusinessType, this.currentBusinessId, currentMedicalId);
                        const medicalDocDetailViews = data.medicalDocDetailViews ?? [];
                        const waitingAddDoc = getCreateMedicalDocumentDetail(medicalDocDetailViews);
                        await mergeCommonTemplate(waitingAddDoc.content?.templateContent?.content);
                        await mergeRemoteRefFieldsData(waitingAddDoc?.content?.templateContent, this.currentBusinessType, this.currentBusinessId, this.refKeyService);
                        await EmrAPI.creatMedicalDocument({
                            businessId: this.currentBusinessId,
                            businessType: this.currentBusinessType,
                            emrMedicalDocs: [{
                                content: waitingAddDoc.content,
                                medicalId: currentMedicalId,
                                id: null,
                            }],
                        });
                    } catch (e) {
                        console.error(e);
                    }
                }

                this._batchCreatingLoading.close();
                // 更新目录
                await this.initMedicalList();
            },

            /**
             * @desc 点击病历按钮，选择了一个文书，新增
             * <AUTHOR>
             * @date 2023-09-03 14:27:19
             */
            async handleSelectMedical(medicalItem) {
                try {
                    const { data } = await EmrAPI.fetchMedicalDocDetail(this.currentBusinessType, this.currentBusinessId, medicalItem.id);
                    const medicalDocDetailViews = data.medicalDocDetailViews ?? [];
                    this.sharedPageId = data.sharedPageId;
                    this.emrOperationTemplateRecordList = medicalDocDetailViews;
                    this.currentTag = medicalItem.tag;
                    this.currentType = medicalItem.type;
                    if (medicalDocDetailViews.length) {
                        const renderMedicalItem = getCreateMedicalDocumentDetail(medicalDocDetailViews);
                        await mergeCommonTemplate(renderMedicalItem.content?.templateContent?.content);
                        await mergeRemoteRefFieldsData(renderMedicalItem?.content?.templateContent, this.currentBusinessType, this.currentBusinessId, this.refKeyService);
                        this.medicalHistoryList = [];
                        this.isAddNewDoc = true;
                        this.emrEditorUseDialogVisible = true;
                    } else {
                        this.$Toast.error('文书模版为空');
                    }
                } catch (e) {
                    console.error(e);
                }
            },

            generateEmrTemplateDataForEditor(recordList) {
                if (recordList?.length) {
                    // 构造共享文书
                    const templateContent = {
                        type: 'doc',
                        content: [],
                    };
                    recordList.forEach((record) => {
                        templateContent.content.push({
                            type: ExtensionTypeEnum.EmbedDocView,
                            attrs: {
                                [AttributesEnum.ID]: record.id,
                            },
                            content: record.content?.templateContent?.content,
                        });
                    });
                    return {
                        templateContent,
                        // 目前先使用第一个记录的设置
                        templateSetting: recordList[0]?.content?.templateSetting,
                        name: recordList[0].name,
                    };
                }
            },


            /**
             * @desc 展开或者折叠目录树
             * <AUTHOR>
             * @date 2023-09-05 10:06:44
             * @params
             * @return
             */
            handleExpand(isExpand) {
                this.isAllExpand = isExpand;
                this.$nextTick(() => {
                    this.$refs.medicalDocumentCollapse.handleAllExpand(isExpand);
                });
            },
            handleChangeExpandAll(val) {
                this.isAllExpand = val;
            },

            /**
             * @desc 根据不同的类型去排序
             * <AUTHOR>
             * @date 2023-09-05 10:15:59
             */
            async handleChangeOrderBy(type) {
                this.isAllExpand = true;
                this.isOrderByLog = type !== 'date';
                await this.initMedicalList();
            },

            /**
             * @desc 初始化文书记录，能够根据分类或者时间进行排序
             * <AUTHOR>
             * @date 2023-09-14 10:28:35
             */
            async initMedicalList() {
                this._isUpdateMedicalList = true;
                if (this.isOrderByLog) {
                    await this.$refs.medicalDocumentCollapse.fetchPatientMedicalList();
                } else {
                    await this.$refs.medicalDocumentCollapse.fetchPatientMedicalListByDate();
                }
            },

            /**
             * @desc 获取共享文书的list
             * <AUTHOR>
             * @date 2023-09-05 14:02:32
             * @params
             * @return
             */
            async fetchShareMedicalList() {
                if (!this.sharedPageId) {
                    this.shareMedicalList = [];
                    return;
                }
                try {
                    const { data } = await EmrAPI.fetchSharedPageWithPermission(this.sharedPageId);
                    this.shareMedicalList = data.medicalViewList;
                } catch (e) {
                    this.shareMedicalList = [];
                }
            },

            /**
             * @desc 在当前共享页面，新增一个文书, 获取文书详情，插入到文书详情中，打开弹窗
             * <AUTHOR>
             * @date 2023-09-05 14:09:53
             */
            async handleAddShareMedicalPage(val) {
                try {
                    const { tag } = this.shareMedicalList.find((it) => it.id === val);
                    // 获取文书详情
                    const { data } = await EmrAPI.fetchMedicalDocDetail(this.currentBusinessType, this.currentBusinessId, val);
                    const medicalView = getCreateMedicalDocumentDetail(data.medicalDocDetailViews);

                    // fix 没有页头等情况
                    await mergeCommonTemplate(medicalView.content?.templateContent?.content);
                    await mergeRemoteRefFieldsData(medicalView?.content?.templateContent, this.currentBusinessType, this.currentBusinessId, this.refKeyService);

                    this.isAddNewDoc = true;
                    this.currentTag = tag;
                    // 插入到当前的文书列表中
                    this.emrOperationTemplateRecordList = Clone(this.emrDisplayTemplateRecordList);
                    this.emrOperationTemplateRecordList.push(medicalView);
                    this.emrEditorUseDialogVisible = true;
                } catch (e) {
                    console.log('插入当前文书失败', e);
                }
            },

            handleCloseUseDialog() {
                this.emrOperationTemplateRecordList = [];
            },

            handleInitCareTableDataHandler(setCareTableData, getCareTableData) {
                this.setCareTableData = setCareTableData;
                this.getCareTableData = getCareTableData;
                this.refreshRecordListData();
            },

            async refreshRecordListData() {
                const list = await EmrAPI.fetchNursingRecordList(this.patientOrderId);
                this.careTableData = {
                    data: list,
                    options: {},
                };
                this.setCareTableData(this.careTableData);
            },

            /**
             * 退回文书
             */
            async handleClickSendBack() {
                this.sendbackDialogVisible = true;
            },

            async handleSubmitSendback(backReason) {
                try {
                    await MedicalRecordManagementAPI.sendBackArchive({
                        backReason,
                        medicalDocId: this.collapse.id,
                    });
                    this.sendbackDialogVisible = false;
                    this.$Toast({
                        message: '退回成功',
                        type: 'success',
                    });
                    await this.fetchPatientMedicalList();
                } catch (e) {
                    console.error(e);
                    this.$Toast({
                        message: e.message,
                        type: 'error',
                    });
                }
            },

            getDocumentCount() {
                return this.$refs.medicalDocumentCollapse?.getDocumentCount();
            },

            fetchPatientMedicalList() {
                return this.$refs.medicalDocumentCollapse?.fetchPatientMedicalList();
            },

            printSettlementForm() {
                handlePrintSettlementForm(this.settlementForm.html);
            },

            async fetchSettlementForm() {
                const getSettlementFormResponse = async () => {
                    try {
                        const { data } = await this.$abcSocialSecurity.settlementListPrint({
                            patientOrderId: this.patientOrderId,
                        });
                        if (!data) {
                            return {};
                        }
                        return data;
                    } catch (e) {
                        console.error(e);
                        return {};
                    }
                };
                this.settlementForm = await getSettlementFormResponse();
            },

            handleClickShowQualityControl() {
                this.showQualityControl = true;
                this.setFieldValidate();
            },

            handleCloseQualityControl() {
                this.getEmrEditor().clearAllFieldAnimation();
                this.clearFieldValidate();
                this.showQualityControl = false;
            },

            async handleClickUploadMedicalSettlementForm() {
                // TODO
                // 清单上报
                console.log(this.patientOrderId);
                const response = await this.$abcSocialSecurity.settlementListlUpload(this.patientOrderId);
                if (response.status === true) {
                    this.$Toast({
                        message: '结算清单上报成功',
                        type: 'success',
                    });
                } else {
                    this.$alert({
                        type: 'warn',
                        title: '提交失败',
                        content: response.message,
                    });
                }
            },
            async handleClickGetGroupResult() {
                // TODO
                // 分组信息查询
                await this.$abcSocialSecurity.getGroupResult(this.patientOrderId);
            },

            async checkHasImportMedicalRecords() {
                try {
                    if (this.businessType !== MedicalDocumentBusinessType.HOSPITAL) {
                        return;
                    }
                    const { data } = await EmrAPI.fetchPatientMedicalList(
                        getBusinessType(this.businessType, MedicalDocumentRecordType.IMPORT),
                        getBusinessId(MedicalDocumentRecordType.IMPORT, this.businessId, this.patientId),
                        this.departmentId,
                    );
                    if (Array.isArray(data.emrCatalogueViews)) {
                        this.hasImportMedicalRecords = data.emrCatalogueViews.length > 0;
                        this.importMedicalRecords = data.emrCatalogueViews;
                    }
                } catch (e) {
                    console.error(e);
                }
            },

            async handleFetchRecordList(next) {
                const params = this.getFetchMedicalDocumentAllSourceParams();
                const { data } = await EmrAPI.fetchMedicalDocDetail(this.currentBusinessType, this.currentBusinessId, ...params);

                if (!isEqual(params, this.getFetchMedicalDocumentAllSourceParams())) {
                    console.warn('getFetchMedicalDocumentAllSourceParams: 请求已过期');
                    return;
                }
                next(data.medicalDocDetailViews || []);
            },

            handleBatchPrint(batchPrintList) {
                const composeCount = batchPrintList.reduce((prev, current) => {
                    return prev + current.composeCount;
                }, 0);

                const doPrint = async () => {

                    const getProcessText = (index) => {
                        return `
                                <div style="text-align: center">
                                    已打印 ${index} 份 / 共 ${composeCount} 份文书
                                </div>
                            `;
                    };

                    this._batchPrintMessage = this.$message({
                        referenceEl: document.getElementById('message-el'),
                        type: 'loading',
                        title: '文书打印中',
                        content: getProcessText(0),
                        cancelText: '停止打印',
                        contentStyle: {
                            textAlign: 'center',
                        },
                        showConfirm: false,
                        noDialogAnimation: true,
                        dialogType: 'tiny',
                        needHighLevel: false,
                        onClose: () => {
                            this._batchPrintMessage = null;
                        },
                    });

                    try {
                        let currentComposeCount = 0;
                        for (const task of batchPrintList) {
                            if (!this._batchPrintMessage) {
                                break;
                            }
                            currentComposeCount += task.composeCount;
                            if ([PrintDocumentTaskType.SingleDocument, PrintDocumentTaskType.SharedDocument].includes(task.type)) {
                                const EmrEditor = await AbcEmrEditorService.loadEmrEditor();
                                const Vue = (await import('vue')).default;
                                const EmrEditorCtor = Vue.extend(EmrEditor);
                                const bussinessType = getBusinessType(this.businessType, MedicalDocumentRecordType.CURRENT);

                                const { data } = await EmrAPI.fetchMedicalDocDetail(
                                    bussinessType,
                                    this.currentBusinessId,
                                    task.medicalId,
                                    task.id,
                                );

                                const templateData = this.generateEmrTemplateDataForEditor(data.medicalDocDetailViews);
                                const currentTemplateSetting = templateData.templateSetting;

                                const emrEditor = new EmrEditorCtor({
                                    propsData: {
                                        value: templateData.templateContent,
                                        pageSize: currentTemplateSetting.pageSize,
                                        pageSizeReduce: currentTemplateSetting.pageSizeReduce,
                                        pageOrientation: currentTemplateSetting.pageOrientation,
                                        mode: EditorModeEnum.Use,
                                        enableEmbedDocView: true,
                                        enableFocusChange: false,
                                    },
                                });

                                emrEditor.$mount();
                                await emrEditor.$nextTick();
                                await emrEditor.print(handlePrintEmr, {
                                    enableBreakAtFirstPage: EnableBreakAtFirstPageType.includes(this.currentType),
                                    enableBreakPageHeaderAndFooter: EnableBreakPageHeaderAndFooterType.includes(this.currentType),
                                }, {
                                    // 自动打印开启
                                    isAutoPrint: true,
                                });

                                emrEditor.$destroy();

                                if (this._batchPrintMessage) {
                                    this._batchPrintMessage.content = getProcessText(currentComposeCount);
                                }
                            }

                            if ([PrintDocumentTaskType.BloodSugar, PrintDocumentTaskType.Temperature].includes(task.type)) {
                                this.printCollapse = task;
                                await this.$nextTick();
                                await this.$refs.printComponent.handleAutoPrint();
                                this.printCollapse = null;
                                if (this._batchPrintMessage) {
                                    this._batchPrintMessage.content = getProcessText(currentComposeCount);
                                }
                            }

                            if (PrintDocumentTaskType.MedicalSettlementForm === task.type) {
                                const getSettlementFormResponse = async () => {
                                    try {
                                        const { data } = await this.$abcSocialSecurity.settlementListPrint({
                                            patientOrderId: this.patientOrderId,
                                        });
                                        if (!data) {
                                            return {};
                                        }
                                        return data;
                                    } catch (e) {
                                        console.error(e);
                                        return {};
                                    }
                                };

                                const { html } = await getSettlementFormResponse();

                                await handlePrintSettlementForm(html, null, null, true);
                            }
                        }

                        this.$Toast({
                            message: '打印完成',
                            type: 'success',
                        });

                        this.$refs.medicalDocumentCollapse?.handleClearSelected();
                    } catch (e) {
                        this.$Toast({
                            message: `文书打印失败：${e.message}`,
                            type: 'error',
                        });
                        console.error(e);
                    } finally {
                        this.printCollapse = null;
                        this._batchPrintMessage?.close();
                        this._batchPrintMessage = null;
                    }
                };

                this.$confirm({
                    type: 'info',
                    title: '批量打印',
                    content: `共选中 ${composeCount} 份文书，是否开始批量打印`,
                    onConfirm: () => {
                        doPrint();
                    },
                });
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.medical-document-main {
    display: flex;
    height: 100%;

    .medical-document-main_float-btn {
        position: absolute;
        top: 50%;
        right: -1px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 88px;
        height: 52px;
        cursor: pointer;
        background: var(--abc-color-S2);
        border: 1px solid $P1;
        border-radius: 4px 0 0 4px;
        transform: translateY(-50%);

        .medical-document-main_float-count {
            font-weight: bold;
            color: $R2;
        }

        .medical-document-main_float-text {
            margin-top: 4px;
        }
    }

    .emr-editor-quality-control-panel {
        position: absolute;
        top: 56px;
        right: 0;
        z-index: 1;
        height: calc(100% - 56px);
        border: none;
        border-left: 1px solid $P6;
        box-shadow: none;
    }

    .collapse-container {
        display: flex;
        flex-direction: column;
        width: 360px;
        height: inherit;
        background-color: var(--abc-color-S2);
        border-right: 1px solid $P6;

        .collapse-operation {
            padding: 12px 16px 11px;
            border-bottom: 1px solid $P6;
        }

        .medical-document-collapse {
            position: relative;
            flex: 1;
            overflow-y: auto;

            @include scrollBar;
        }
    }

    .medical-document-preview-container {
        position: relative;
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: stretch;
        width: 0;
        height: inherit;

        .medical-document-preview_header {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
            height: 56px;
            padding: 16px;
            background-color: var(--abc-color-S2);
            border-bottom: 1px solid $P6;

            .medical-document-preview_header-title {
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                color: $T1;
            }

            .medical-document-back-reason {
                position: absolute;
                bottom: 0;
                left: 0;
                z-index: 1;
                display: flex;
                align-items: center;
                width: 100%;
                min-height: 40px;
                padding: 10px 16px;
                color: $Y2;
                background: $Y4;
                border-bottom: 1px solid $Y5;
                transform: translateY(100%);

                &_content {
                    display: block;
                    flex: 1;
                    max-height: 42px;
                    overflow-y: auto;
                }
            }
        }

        .medical-document-preview_content {
            flex: 1;
            height: 100%;
            overflow-x: auto;
            overflow-y: auto;
            background: #f5f5f5;
        }

        .medical-document-preview_settlement-form {
            position: relative;
            box-sizing: border-box;
            width: 210mm;
            min-height: 297mm;
            padding: 15mm;
            margin: 24px auto;
            background: var(--abc-color-S2);
            border: 1px solid $P2;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.**********);

            iframe {
                width: 100%;
                border: none;
            }
        }
    }

    .daily-preview-container {
        position: relative;
        flex: 1;
        width: 0;
        height: inherit;
    }
}

@media screen and (max-width: 1440px) {
    .medical-document-main {
        .collapse-container {
            width: 280px;
        }

        .abc-select-wrapper {
            width: 93px !important;
        }

        .collapse-operation {
            gap: 4px !important;
        }

        .create-year {
            display: none;
        }

        .abc-collapse-item_inner {
            padding: 8px !important;
        }

        .abc-collapse-item_created-name {
            width: 48px !important;
        }

        .abc-collapse-item_created {
            width: 78px !important;
        }
    }
}
</style>
