<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        content-styles="width: 657px; height: auto; max-height: 588px; padding: 24px;"
        :title="dialogTitle"
        custom-class="print-config-normal-dialog"
        append-to-body
        :before-close="beforeClose"
    >
        <div class="dialog-content clearfix">
            <div v-if="noConnectionPrint" class="no-install-print">
                <div class="no-install-tip">
                    <i class="icon iconfont cis-icon-jinggao1"></i>
                    请连接打印机！
                </div>
            </div>

            <div class="print-setting-table">
                <abc-excel-table>
                    <div class="table-header">
                        <div class="th" style="width: 112px;">
                            单据类型
                        </div>
                        <div class="th" style="width: 126px;">
                            打印机
                        </div>
                        <div class="th" :style="{ width: isHiddenPreview ? '156px' : ' 78px' }">
                            纸张尺寸
                        </div>
                        <div class="th" style="width: 78px;">
                            打印方向
                        </div>
                        <div v-if="!isHiddenPreview" class="th" style="width: 78px;">
                            预览模式
                        </div>
                        <div class="th" style="width: 135px;">
                            打印份数
                        </div>
                    </div>

                    <div class="table-body">
                        <template v-for="(item, index) in printConfigWithKey">
                            <div :key="item.key" class="tr" :class="{ 'print-count-row': item.key === 'medicine-tag' }">
                                <!-- 单据类型 -->
                                <div class="td" :class="{ 'print-count-column': item.key === 'medicine-tag' }" style="width: 112px; padding: 0 10px;">
                                    <div class="label-container">
                                        {{ getLabel(item) }}
                                    </div>
                                </div>
                                <!-- 打印机 -->
                                <div class="td select-input-padding-right-20" :class="{ 'print-count-column': item.key === 'medicine-tag' }" style="width: 126px;">
                                    <!-- todo lxl 打印初始化流程 -->
                                    <!-- 是否有setting需要判断是否开启了prescriptionVersion===1 -->
                                    <abc-select
                                        :key="`printer-select-key-${printerListIsUpdate}`"
                                        v-model="item.deviceIndex"
                                        :width="126"
                                        :inner-width="360"
                                        :index="index"
                                        custom-class="print-preview-printer-select-wrapper"
                                        placeholder="选择打印机"
                                        :setting="isNewPrescriptionVersion && isABCClient()"
                                        setting-text="添加打印机"
                                        setting-icon="a-plus13px"
                                        @change="handleChangeDevice"
                                        @set="handleOpenAddPrinterDriverDialog"
                                    >
                                        <abc-option
                                            v-for="(it) in printerList"
                                            :key="it.deviceIndex"
                                            :value="it.deviceIndex"
                                            :label="it.name"
                                        >
                                            <img
                                                v-if="isTinyPrinter(it.deviceIndex)"
                                                class="print-preview-printer-img-icon"
                                                src="~assets/images/print/printer-tiny-icon.png"
                                                alt=""
                                            />
                                            <img
                                                v-else
                                                class="print-preview-printer-img-icon"
                                                src="~assets/images/print/printer-icon.png"
                                                alt=""
                                            />
                                            <span class="print-preview-printer-select-option-title">{{ it.name + (isTinyPrinter(it.deviceIndex) ? '（小票）' : '') }}</span>
                                            <span v-if="isNewPrescriptionVersion && it.offline" class="print-preview-printer-select-option-offline">脱机</span>
                                        </abc-option>
                                    </abc-select>
                                </div>
                                <!-- 纸张尺寸 -->
                                <div
                                    class="td select-input-padding-right-20"
                                    :class="{ 'print-count-column': item.key === 'medicine-tag' }"
                                    :style="{
                                        width: isHiddenPreview ? '156px' : '78px',
                                    }"
                                >
                                    <abc-select
                                        :key="`printer-select-key-${item.deviceIndex}-${printerListIsUpdate}`"
                                        v-model="item.pageSize"
                                        custom-class="print-page-list"
                                        placeholder="尺寸"
                                        :width="isHiddenPreview ? 156 : 78"
                                        :inner-width="248"
                                        @change="handlePageSizeChange(item)"
                                    >
                                        <div v-for="(it, pageIndex) in getBusinessPageList(item)" :key="pageIndex">
                                            <li v-if="it.isElectronPage && !getBusinessPageList(item)[pageIndex - 1]?.isElectronPage" key="print-dialog-option-border" class="print-dialog-option-border"></li>
                                            <abc-option
                                                :value="it.paper.name"
                                                :label="it.paper.name"
                                            >
                                                {{ it.paper.name }}
                                                <span v-if="it.isRecommend" class="print-recommend-text">推荐</span>
                                            </abc-option>
                                        </div>
                                    </abc-select>
                                </div>
                                <!-- 打印方向 -->
                                <div
                                    class="td"
                                    :class="{ 'print-count-column': item.key === 'medicine-tag' }"
                                    style="width: 78px;"
                                >
                                    <abc-select
                                        v-if="heightLevelList(item)"
                                        v-model="item.pageHeightLevel"
                                        :width="77"
                                        placeholder="布局"
                                        class="spacing-right"
                                        :disabled="noConnectionPrint || disableHeightLevelOrOrient(item)"
                                    >
                                        <abc-option
                                            v-for="level in heightLevelList(item)"
                                            :key="level.key"
                                            :value="level.name"
                                            :label="level.name"
                                        ></abc-option>
                                    </abc-select>
                                    <abc-select
                                        v-else
                                        v-model="item.orient"
                                        placeholder="布局"
                                        :width="77"
                                        class="spacing-right"
                                        :disabled="noConnectionPrint || disableHeightLevelOrOrient(item)"
                                    >
                                        <abc-option
                                            v-for="orient in currentOrientations(item.pageSize)"
                                            :key="orient.value"
                                            :label="orient.label"
                                            :value="orient.value"
                                        ></abc-option>
                                    </abc-select>
                                </div>
                                <!-- 预览模式 -->
                                <div
                                    v-if="!isHiddenPreview"
                                    class="td"
                                    :class="{ 'print-count-column': item.key === 'medicine-tag' }"
                                    style="width: 78px;"
                                >
                                    <abc-select
                                        v-model="item.preview"
                                        :width="78"
                                        placeholder="预览"
                                    >
                                        <abc-option label="有预览" :value="1"></abc-option>
                                        <abc-option label="无预览" :value="0"></abc-option>
                                    </abc-select>
                                </div>
                                <!-- 打印份数 -->
                                <div class="td" style="width: 135px;">
                                    <template v-if="item.key === 'medicine-tag'">
                                        <div class="medicine-tag-container">
                                            <div class="medicine-tag-setting">
                                                <abc-button type="text" @click="openMedicineTagDialog(item)">
                                                    设置
                                                </abc-button>
                                            </div>
                                            <!-- 口服 -->
                                            <div>
                                                口服：{{ item.printCountData?.oral?.printCopies || 1 }}份
                                            </div>
                                            <!-- 输液 -->
                                            <div>
                                                <template v-if="item.printCountData?.infusion?.printCopies !== -1">
                                                    输液：{{ item.printCountData?.infusion?.printCopies || 1 }}份
                                                </template>
                                                <template v-else>
                                                    输液：按输液次数
                                                </template>
                                            </div>
                                            <!-- 注射 -->
                                            <div>
                                                注射：{{ item.printCountData?.injections?.printCopies || 1 }}份
                                            </div>
                                            <!-- 雾化 -->
                                            <div>
                                                雾化：{{ item.printCountData?.atomization?.printCopies || 1 }}份
                                            </div>
                                            <!-- 外用 -->
                                            <div>
                                                外用：{{ item.printCountData?.external?.printCopies || 1 }}份
                                            </div>
                                            <!-- 煎服 -->
                                            <div>
                                                <template v-if="item.printCountData?.decoction?.printCopies !== -1">
                                                    煎服：{{ item.printCountData?.decoction?.printCopies || 1 }}份
                                                </template>
                                                <template v-else>
                                                    煎服：按包装规格
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <print-count-input
                                            v-model="item.printCopies"
                                            :width="135"
                                        ></print-count-input>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </abc-excel-table>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="saveLoading" :disabled="isSaveBtnDisabled" @click="ok">
                保存
            </abc-button>
            <abc-button :disabled="saveLoading" type="blank" @click="closeDialog">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import ABCPrinterConfig from '../../config.js';
    import {
        ABCPrintConfigKeyMap,
        FEE_BILL, FEE_LIST, FEE_SOCIAL,
    } from '@/printer/constants';
    import store from 'src/store';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import {
        isDev, isLocal, isTest,
    } from '@/assets/configure/build-env.js';
    import PrintManager from '@/printer/manager/print-manager.js';
    import printConfigMixin from '@/printer/components/print-config-dialog/print-config-mixin';
    import { mapGetters } from 'vuex';

    export default {
        name: 'PrintConfigNormalDialog',
        mixins: [printConfigMixin],
        props: {
            scene: {
                type: String,
                default: 'outpatient',
            },

            isHiddenPreview: {
                type: Boolean,
                default: false,
            },

            isCloudExamination: Boolean,

            isCustomCloudReport: Boolean,

            dialogTitle: {
                type: String,
                default: '打印规则',
            },

            onConfirm: {
                type: Function,
                default: () => {},
            },

            validateBeforeConfirm: {
                type: Function,
                default: undefined,
            },
        },
        computed: {
            ...mapGetters(['isEnableEyeInspectReportV2', 'printHospitalMedicalDocumentsConfig']),

            printConfigWithKey() {
                const viewDistributeConfig = getViewDistributeConfig();
                const { containInspect = true } = viewDistributeConfig.Examination;
                const { allowExamReportPrint } = viewDistributeConfig.Print;
                const { isShowPrintHospitalizationCertificate } = viewDistributeConfig.Outpatient;

                const hospitalDoctorAdviceConfig = ['hospital-prescription', 'examine-apply-sheet', 'examination-inspect-apply-sheet'];
                const hospitalNurseAdviceConfig = [];
                const hospitalNurseOutHospitalAdviceConfig = ['charge-list'];
                if (this.printHospitalMedicalDocumentsConfig.advice?.template !== 1) {
                    hospitalDoctorAdviceConfig.unshift('hospital-doctor-medical-prescription');
                    hospitalNurseAdviceConfig.unshift('hospital-doctor-medical-prescription');
                    hospitalNurseOutHospitalAdviceConfig.unshift('hospital-doctor-medical-prescription');
                }

                const _examinationArr = [];
                if (this.isCloudExamination) {
                    if (this.isCustomCloudReport) {
                        _examinationArr.push('examination-cloud-report');
                    } else {
                        _examinationArr.push('examination-report-pdf');
                    }
                } else {
                    _examinationArr.push('examination-report');
                }
                if (containInspect) {
                    _examinationArr.push('inspect-report');
                }
                _examinationArr.push('examination-tag');
                let _outpatient = ['medical-record', 'prescription', 'treatment-execute', 'infusion-execute', 'medical-certificate'];
                if (this.isEnableEyeInspectReportV2) {
                    _outpatient.push('eye-inspect-report-custom');
                }
                let _childhealth = ['medical-record', 'prescription', 'treatment-execute', 'infusion-execute', 'child-healthy-report'];
                let _cashier = ['cashier', 'dispense', 'prescription', 'infusion-execute', 'treatment-execute', 'examination-tag', 'medicine-tag', 'patient-tag', 'fee-bill', 'fee-list'];
                let _pharmacy = ['dispense', 'prescription', 'infusion-execute', 'medicine-tag', 'patient-tag'];
                const _treatment = ['infusion-execute', 'treatment-execute', 'medicine-tag', 'patient-tag'];
                let _outpatientPr = ['medical-record', 'prescription', 'treatment-execute', 'infusion-execute'];
                let _pharmacyDialog = ['dispense', 'prescription', 'infusion-execute', 'medicine-tag', 'patient-tag'];
                /**
                 * 针对检查检验做处理
                 * 诊所管家是检查单/检验单
                 * 医院管家是检查申请单/检验申请单
                 */
                if (allowExamReportPrint) {
                    _outpatient.splice(4, 0, 'examine-apply-sheet', 'examination-inspect-apply-sheet');
                    _childhealth.splice(4, 0, 'examine-apply-sheet', 'examination-inspect-apply-sheet');
                    _cashier.splice(6, 0, 'examine-apply-sheet', 'examination-inspect-apply-sheet');
                    _treatment.splice(2, 0, 'examine-apply-sheet', 'examination-inspect-apply-sheet');
                } else {
                    _outpatient.splice(4, 0, 'examine', 'examinationInspect');
                    _childhealth.splice(4, 0, 'examine', 'examinationInspect');
                    _cashier.splice(6, 0, 'examine', 'examinationInspect');
                    _treatment.splice(2, 0, 'examine', 'examinationInspect');
                }

                if (isShowPrintHospitalizationCertificate) {
                    _outpatient.push('hospitalization-certificate');
                }

                if (store.getters?.printMedicalDocumentsConfig?.prescription?.header?.templateType &&
                    (window.$abcSocialSecurity.config.isTianjin || isDev || isTest || isLocal)) {
                    _outpatient = _outpatient.filter((item) => {
                        return item !== 'prescription';
                    });
                    _outpatient.push('tianjin-western-prescription');
                    _outpatient.push('tianjin-chinese-prescription');

                    _childhealth = _childhealth.filter((item) => {
                        return item !== 'prescription';
                    });
                    _childhealth.push('tianjin-western-prescription');
                    _childhealth.push('tianjin-chinese-prescription');

                    _cashier = _cashier.filter((item) => {
                        return item !== 'prescription';
                    });
                    _cashier.push('tianjin-western-prescription');
                    _cashier.push('tianjin-chinese-prescription');

                    _pharmacy = _pharmacy.filter((item) => {
                        return item !== 'prescription';
                    });
                    _pharmacy.push('tianjin-western-prescription');
                    _pharmacy.push('tianjin-chinese-prescription');

                    _outpatientPr = _outpatientPr.filter((item) => {
                        return item !== 'prescription';
                    });
                    _outpatientPr.push('tianjin-western-prescription');
                    _outpatientPr.push('tianjin-chinese-prescription');

                    _pharmacyDialog = _pharmacyDialog.filter((item) => {
                        return item !== 'prescription';
                    });
                    _pharmacyDialog.push('tianjin-western-prescription');
                    _pharmacyDialog.push('tianjin-chinese-prescription');
                }

                const printV2Config = PrintManager.getInstance().getFeature('printConfig');
                if (printV2Config.prescriptionVersion && !store.getters?.printMedicalDocumentsConfig?.prescription?.header?.templateType) {
                    const outpatientIndex = _outpatient.indexOf('prescription');
                    if (outpatientIndex > -1) {
                        _outpatient.splice(outpatientIndex, 1, 'prescriptionV2');
                    }

                    const childhealthIndex = _childhealth.indexOf('prescription');
                    if (childhealthIndex > -1) {
                        _childhealth.splice(childhealthIndex, 1, 'prescriptionV2');
                    }

                    const cashierIndex = _cashier.indexOf('prescription');
                    if (cashierIndex > -1) {
                        _cashier.splice(cashierIndex, 1, 'prescriptionV2');
                    }

                    const pharmacyIndex = _pharmacy.indexOf('prescription');
                    if (pharmacyIndex > -1) {
                        _pharmacy.splice(pharmacyIndex, 1, 'prescriptionV2');
                    }

                    const outpatientPrIndex = _outpatientPr.indexOf('prescription');
                    if (outpatientPrIndex > -1) {
                        _outpatientPr.splice(outpatientPrIndex, 1, 'prescriptionV2');
                    }

                    const pharmacyDialogIndex = _pharmacyDialog.indexOf('prescription');
                    if (pharmacyDialogIndex > -1) {
                        _pharmacyDialog.splice(pharmacyDialogIndex, 1, 'prescriptionV2');
                    }
                }

                const sceneMap = {
                    treatment: _treatment,
                    registration: ['registration', 'fee-bill', 'fee-list', 'patient-tag'],
                    registrationShiXian: ['registration','registration-tag', 'fee-bill', 'fee-list'],
                    outpatient: _outpatient,
                    childhealth: _childhealth,
                    'outpatient-pr': _outpatientPr,
                    cashier: _cashier,
                    pharmacy: _pharmacy,
                    'pharmacy-dialog': _pharmacyDialog,
                    examination: _examinationArr,
                    'goods-in': ['goods-in'],
                    'goods-out': ['goods-out'],
                    'goods-trans': ['goods-trans'],
                    'goods-check': ['goods-check'],
                    'goods-purchase': ['goods-purchase'],
                    'goods-apply': ['goods-apply'],
                    'pe-individual-report': ['pe-individual-report'],
                    // 体检单
                    'pe-sheet': ['pe-guide-sheet', 'examination-tag'],
                    // 检查报告
                    'eye-inspect-report': ['eye-inspect-report', 'inspect-label'],
                    'hospital-report': ['hospital-inspect', 'inspect-label'],
                    'inspect-report': ['inspect-report', 'inspect-label'],
                    // 医生站-医嘱
                    'hospital-doctor-advice': hospitalDoctorAdviceConfig,
                    // 医生站-检验/检查
                    'hospital-doctor-examination': ['examination-report'],
                    // 医生站-统计
                    'hospital-doctor-statistics': ['report'],
                    // 护士站-床位
                    'hospital-nurse-advice': hospitalNurseAdviceConfig,
                    // 护士站-床位-出院/转科
                    'hospital-nurse-out-hospital-advice': hospitalNurseOutHospitalAdviceConfig,
                    // 护士站-药品
                    'hospital-nurse-medicine': ['hospital-nurse-dispensing', 'hospital-medicine-tag'],
                    // 护士站-日常-体温表
                    'hospital-nurse-daily-temperature-table': ['temperature-table'],
                    // 护士站-日常-体温单
                    'hospital-nurse-daily-temperature': ['temperature-graph'],
                    // 护士站-日常-血糖单
                    'hospital-nurse-daily-blood-sugar': ['blood-sugar'],
                    // 护士站-费用-费用核对
                    'hospital-nurse-cost-check': ['charge-list', 'charge-list-batch'],
                    // 药房-药品-发药
                    'hospital-pharmacy-dispense-goods': ['hospital-nurse-dispensing'],
                    // 药房-患者-发药
                    'hospital-pharmacy-dispense-patient': ['hospital-nurse-dispensing', 'hospital-prescription', 'hospital-medicine-tag'],
                    // 护士-医嘱-执行
                    'hospital-nurse-advice-execute': ['hospital-nurse-prescription', 'examine-apply-sheet', 'examination-inspect-apply-sheet', 'hospital-examination-tag', 'hospital-medicine-tag'],
                    'purchase-settlement': ['settlement-application', 'settlement-review'],
                    'purchase-settlement-v2': ['settlement-detail'],// 付款明细-药店版结算单
                    'pe-charge-fee-list': ['pe-charge-fee-list'],
                };
                const currentScene = sceneMap[this.scene];
                if (!Array.isArray(currentScene)) {
                    console.error('当前场景值未定义');
                    return [];
                }

                const res = currentScene.map((key) => {
                    return this.allPrintConfig.find((it) => it.key === key);
                });
                // 对旧版本数据做兼容
                res.forEach((item) => {
                    if (item.key === 'medicine-tag') {
                        item.printCopies = 1;
                        if (!item.printCountData) {
                            item.printCountData = {
                                // 口服
                                oral: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 注射
                                injections: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 外用
                                external: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 输液
                                infusion: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 雾化
                                atomization: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                                // 煎服
                                decoction: {
                                    printCopies: 1,
                                    isPrint: 1,
                                },
                            };
                        }
                    }
                });
                return res;
            },
        },
        methods: {
            beforeClose(fn) {
                if (this.isChangePrintCountData) {
                    fn(false);
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '用药标签打印份数还未保存，确定离开？',
                        closeAfterConfirm: true,
                        onConfirm: this.no,
                    });
                } else {
                    fn(true);
                }
            },
            closeDialog() {
                if (this.isChangePrintCountData) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '用药标签打印份数还未保存，确定离开？',
                        closeAfterConfirm: true,
                        onConfirm: this.no,
                    });
                } else {
                    this.no();
                }
            },
            /**
             * @desc 获取纸张列表
             * @return
             */
            getBusinessPageList(item) {
                if (item.deviceIndex === -1) {
                    return [];
                }
                // 增加纸张缓存机制
                if (!this._cache) {
                    this._cache = {};
                }
                const { AbcTemplates } = window.AbcPackages;
                let key = this.formatCame(item.key);
                if (item.key === FEE_BILL) {
                    const { format } = store.getters.printGlobalConfig && store.getters.printGlobalConfig.billConfig || {};
                    if (format) {
                        key = this.formatCame(`medical-bill-${format}`);
                        const cacheKey = `${item.deviceName} ${JSON.stringify(AbcTemplates[key]?.pages ?? [])}`;
                        if (this._cache[cacheKey] && !this.printerListIsUpdate) {
                            return this._cache[cacheKey];
                        }
                        const result = PrintManager.getInstance().mergedPagesByDeviceName(item.deviceName, AbcTemplates[key]?.pages ?? []);
                        this._cache[cacheKey] = result;
                        return result;
                    }
                    return [];
                }
                if (item.key === FEE_LIST) {
                    const { format } = store.getters.printGlobalConfig && store.getters.printGlobalConfig.medicalListConfig || {};
                    if (format) {
                        key = this.formatCame(`medical-fee-list-${format}`);
                        const cacheKey = `${item.deviceName} ${JSON.stringify(AbcTemplates[key]?.pages ?? [])}`;
                        if (this._cache[cacheKey] && !this.printerListIsUpdate) {
                            return this._cache[cacheKey];
                        }
                        const result = PrintManager.getInstance().mergedPagesByDeviceName(item.deviceName, AbcTemplates[key]?.pages ?? []);
                        this._cache[cacheKey] = result;
                        return result;
                    }
                    return [];
                }
                if (item.key === FEE_SOCIAL) {
                    key = this.formatCame(`medical-${FEE_SOCIAL}`);
                    const cacheKey = `${item.deviceName} ${JSON.stringify(AbcTemplates[key]?.pages ?? [])}`;
                    if (this._cache[cacheKey] && !this.printerListIsUpdate) {
                        return this._cache[cacheKey];
                    }
                    const result = PrintManager.getInstance().mergedPagesByDeviceName(item.deviceName, AbcTemplates[key]?.pages ?? []);
                    this._cache[cacheKey] = result;
                    return result;
                }

                let cacheKey = `${item.deviceName} `;
                let printConfigKey = '';
                const ABCPrintConfigKeyMapKeys = Object.keys(ABCPrintConfigKeyMap);
                for (const objKey of ABCPrintConfigKeyMapKeys) {
                    if (item.key === ABCPrintConfigKeyMap[objKey].subKey) {
                        printConfigKey = objKey;
                        break;
                    }
                }
                const { printOptions } = getViewDistributeConfig().Print;
                const printOptionsKeys = Object.keys(printOptions);
                let templateKey = '';
                for (const objKey of printOptionsKeys) {
                    if (printConfigKey === printOptions[objKey].printConfigKey) {
                        templateKey = printOptions[objKey].templateKey;
                        break;
                    }
                }
                cacheKey += `${JSON.stringify(AbcTemplates?.[templateKey]?.pages ?? [])}`;

                if (this._cache[cacheKey] && !this.printerListIsUpdate) {
                    return this._cache[cacheKey];
                }
                const result = PrintManager.getInstance().mergedPagesByDeviceName(item.deviceName, AbcTemplates?.[templateKey]?.pages ?? []);
                this._cache[cacheKey] = result;
                return result;
            },
            async ok() {
                let isValid = true;
                const updatedData = {
                    prescription: this.prescriptionConfig,
                    ticket: this.ticketConfig,
                    bill: this.billConfig,
                    inventory: this.inventoryConfig,
                    statistics: this.statisticsConfig,
                };
                // 校验函数
                if (this.validateBeforeConfirm) {
                    isValid = this.validateBeforeConfirm(updatedData);
                }
                if (!isValid) {
                    return;
                }
                // 保存打印配置
                ABCPrinterConfig.setPrintConfig(updatedData);
                // 保存配置后的回调
                this.onConfirm && this.onConfirm(updatedData);
                this.visible = false;
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';
@import "src/styles/mixin";

.print-config-normal-dialog {
    .print-setting-table {
        .table-header {
            .th {
                &:not(:last-child) {
                    border-right: 1px solid $P6 !important;
                }
            }
        }

        .table-body {
            .tr {
                & + .tr {
                    border-top: 1px solid $P6 !important;
                }

                .td {
                    &:not(:last-child) {
                        border-right: 1px solid $P6 !important;
                    }

                    .medicine-tag-container {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        padding: 0 10px;

                        &:hover {
                            .medicine-tag-setting {
                                display: flex;
                            }
                        }

                        .medicine-tag-setting {
                            position: absolute;
                            display: none;
                            align-items: center;
                            justify-content: center;
                            width: calc(100% - 20px);
                            height: 100%;
                            background-color: $S2;
                            opacity: 0.89;
                        }
                    }

                    .label-container {
                        display: flex;
                        align-items: center;
                        height: 40px;
                    }
                }

                .select-input-padding-right-20 {
                    .abc-input__inner {
                        padding-right: 20px !important;
                    }
                }

                .print-count-column {
                    align-items: flex-start !important;
                }
            }

            .print-count-row {
                height: 130px !important;
            }
        }
    }

    .dialog-content {
        .auto-print-config {
            margin-top: 24px;

            .select-container {
                display: flex;

                .title {
                    width: 100px;
                    margin-right: 24px;
                }
            }

            .select-pharmacy-container {
                margin-top: 16px;

                .title {
                    float: left;
                    width: 100px;
                    margin-right: 24px;
                }

                .pharmacy-checkbox {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 12px 18px;
                    float: right;
                    width: calc(100% - 124px);

                    .pharmacy-check-box {
                        width: 109px !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        overflow: hidden !important;

                        .abc-checkbox__label {
                            @include ellipsis;
                        }
                    }
                }
            }
        }
    }

    .dialog-footer {
        position: relative;

        .print-tips {
            position: absolute;
            left: 0;
            font-size: 12px;
            line-height: 16px;
            color: $T3;
        }
    }

    .no-install-print {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        margin: 0 auto 16px auto;
        font-size: 16px;
        color: $Y2;
        background-color: $Y4;
        border: 1px solid $Y5;
        border-radius: var(--abc-border-radius-small);

        i {
            color: $Y2;
        }

        span {
            color: $theme2;
            cursor: pointer;
        }

        ul {
            margin: 10px;
        }

        li {
            line-height: 24px;
            color: $T2;
        }
    }

    .installed-print {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        margin: 12px auto;
        font-size: 16px;
        color: $G2;
        background-color: $G4;
        border: 1px solid $G5;
        border-radius: var(--abc-border-radius-small);
    }

    .print-setting-item {
        display: flex;
        align-items: center;
        margin: 12px 0;

        .label {
            width: 100px;
        }

        .abc-select-wrapper,
        .abc-input-wrapper {
            margin-left: 12px;
        }

        .print-copies-unit {
            margin-left: 4px;
        }
    }

    .print-count-select {
        .count-icon {
            position: absolute;
            top: 50%;
            left: 18px;
            z-index: 2;
            margin-top: -7px;
        }
    }
}
</style>
