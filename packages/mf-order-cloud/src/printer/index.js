import PrintManager from './manager/print-manager.js';
import PrinterService from './service.js';
import PrinterTask from './task';

export const PRINT_SUCCESS = 0;
export const PRINT_CANCEL = -1;
export default class AbcPrinter {
    static globalConfigIsInit = false;

    static async abcPrint(propsList, uuid) {
        if (typeof propsList === 'function') {
            propsList = await propsList();
            if (!propsList?.length) {
                // 不存在可预览的打印任务直接返回
                console.error('不存在可预览的打印任务');
                return;
            }
        }
        // 参数不为数组
        if (!(propsList instanceof Array)) {
            propsList = [propsList];
        }
        const resPropsList = propsList;

        for (let i = 0; i < resPropsList.length; i++) {
            try {
                const props = resPropsList[i];
                const { printConfig } = props;

                const {
                    deviceName,
                    pageSize,
                } = printConfig;
                if (pageSize && !PrintManager.getInstance().mergedPagesByDeviceName(deviceName, props.templateKey.pages).some((it) => it.paper.name === pageSize)) {

                    console.warn('当前选择纸张不在模板支持的列表中');
                    printConfig.deviceIndex = -1;
                }
                await AbcPrinter.print(props, uuid);
            } catch (e) {
                console.error(e);
            }
        }
    }

    /**
     * @desc 对外打印函数
     * <AUTHOR>
     * @date 2021-07-16 09:16:17
     */
    static print(props) {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve) => {
            AbcPrinter.setGlobalConfig();

            const printTask = new PrinterTask(props);
            await printTask.initAbcPrintInstance();
            const printService = PrinterService.getPrintService();
            printService.pushQueue(printTask, () => {
                resolve({
                    status: PRINT_SUCCESS,
                });
            });
        });
    }

    /**
     * 设置打印错误上报日志的元数据
     */
    static setErrorLogMetaData() {
        window.AbcPackages.AbcPrint.setErrorLogMetaData({
            userId: '',
            clinicId: '',
            chainId: '',
        });
    }

    /**
     * @desc 设置打印全局配置
     * <AUTHOR>
     * @date 2021-08-25 16:15:30
     */
    static setGlobalConfig() {
        if (AbcPrinter.globalConfigIsInit) return;
        AbcPrinter.setErrorLogMetaData();
        return window.AbcPackages.AbcPrint.setGlobalConfig({
            globalConfig: {},
        });
    }
}

