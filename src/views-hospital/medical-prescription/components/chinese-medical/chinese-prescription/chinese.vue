<template>
    <div
        class="prescription-table-wrapper chinese-table"
        :class="{ 'is-disabled': disabledForm }"
    >
        <div class="prescription-header">
            <div class="prescription-title">
                <img class="icon-wrapper" src="~assets/images/icon/traditional.png" alt="" />
                <h5>中药处方{{ translateH }}</h5>

                <abc-select
                    ref="specSelect"
                    v-model="adviceRule.type"
                    :width="60"
                    :disabled="disabledForm"
                    class="unit-select"
                    @enter="enterEvent"
                    @change="changeSpecification"
                >
                    <abc-option label="饮片" :value="AdviceRuleType.CHINESE_MEDICINE_TABLETS"></abc-option>
                    <abc-option label="颗粒" :value="AdviceRuleType.CHINESE_MEDICINE_GRANULES"></abc-option>
                </abc-select>

                <vendors-popover
                    v-if="showChangePharmacyType"
                    ref="vendor-popover"
                    :disabled="disabledForm"
                    :vendor-id.sync="adviceRule.vendorId"
                    :specification="specification"
                    :pharmacy-type.sync="adviceRule.pharmacyType"
                    :pharmacy-no.sync="adviceRule.pharmacyNo"
                    :pharmacy-name.sync="adviceRule.pharmacyName"
                    :process-price.sync="adviceRule.processPrice"
                    :ingredient-price.sync="adviceRule.ingredientPrice"
                    :usage-scope-id.sync="adviceRule.usageScopeId"
                    :vendor-name.sync="adviceRule.vendorName"
                    :dose-count="adviceRule.dosageCount"
                    :medicine-state-scope-id.sync="adviceRule.medicineStateScopeId"
                    :form-items="adviceRule.items"
                    :show-more-pharmacy-list="false"
                    @changeSpecification="changeSpecification"
                    @changeUsage="selectChineseMedicineUsage"
                    @changeMedicineState="calcBagsNumber"
                ></vendors-popover>
            </div>

            <abc-text
                v-if="totalWeightInfo.desc"
                size="mini"
                theme="gray"
                tag="div"
            >
                {{ totalWeightInfo.desc }}
                <abc-tooltip-info
                    v-if="otherPharmacyTotalTips"
                    :content="otherPharmacyTotalTips"
                    :icon-size="12"
                    placement="top"
                >
                </abc-tooltip-info>
            </abc-text>
            <div class="operation">
                <abc-button
                    type="ghost"
                    size="small"
                    style="height: 22px; margin-right: 4px; font-size: 13px;"
                    @click="selectDialogVisible = true"
                >
                    查找中药
                </abc-button>
                <abc-checkbox-button
                    v-if="showDecoction && isOpenSource"
                    :value="adviceRule.isNeedProcess"
                    control
                    size="mini"
                    style="margin-right: 6px;"
                    @click="clickDecoction"
                >
                    加工
                </abc-checkbox-button>
            </div>
        </div>

        <div class="prescription-table" style="overflow: visible;">
            <draggable
                v-model="medicineList"
                :disabled="disabledForm"
                ghost-class="abc-sortable-ghost"
                @start="startDrag"
                @end="dragging = false"
            >
                <transition-group
                    name="list-complete"
                    :css="false"
                    class="table-body"
                    tag="div"
                    mode="out-in"
                >
                    <div
                        v-for="(medicine, index) in medicineList"
                        ref="tableTd"
                        :key="medicine.id || medicine.keyId || index"
                        class="table-td"
                        :class="{
                            'is-last-row': isLastRow(index),
                            'is-last-col': (index + 1) % 4 === 0,
                        }"
                    >
                        <div class="name-wrapper">
                            <template v-if="currentIndex === index || !medicine.medicineCadn">
                                <!-- 本地药房处方卡片 -->
                                <medicine-auto-complete
                                    ref="chinese-autocomplete"
                                    :show-detail-price="showDetailPrice"
                                    :specification="specification"
                                    :pharmacy-no="adviceRule.pharmacyNo"
                                    input-placeholder="药名"
                                    :default-keyword="medicine.medicineCadn"
                                    :inner-width="500"
                                    :treat-online-clinic-id="treatOnlineClinicId"
                                    :prescription-form-items="medicineList"
                                    :patient-info="patientInfo"
                                    :medical-record="medicalRecord"
                                    :need-check-stock="needCheckStock"
                                    :shebao-card-info="shebaoCardInfo"
                                    :is-open-source="isOpenSource"
                                    no-icon
                                    :support-mix="adviceRule.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY && chinesePrescriptionSupportMix"
                                    :search-by-root="showWarnTips(medicine)"
                                    @left="handleNameLeft(index)"
                                    @right="handleNameRight(index)"
                                    @up="handleNameUp(index)"
                                    @down="handleNameDown(index)"
                                    @focus="currentIndex = index"
                                    @blur="($autocomplete) => blurHandle(medicine, $autocomplete)"
                                    @select="changeMedicine"
                                    @dblclick.native="handleDoubleClick"
                                ></medicine-auto-complete>
                            </template>

                            <div
                                v-else
                                class="cadn"
                                :class="{ 'is-shortage': showWarnTips(medicine) }"
                                @click="editCurrentMedicine(index)"
                            >
                                <div
                                    :key="`${medicine.goodsId }_${ index}`"
                                    v-abc-goods-hover-popper="{
                                        goods: medicine,
                                        openDelay: 500,
                                        dontShow: dragging,
                                        showStock: needCheckStock,
                                        showPrice: showDetailPrice,
                                        showShebaoCode: true,
                                    }"
                                    class="name"
                                >
                                    {{ medicine.medicineCadn }}
                                </div>

                                <div
                                    v-if="showWarnTips(medicine)"
                                    :data-tipsy="warnTips(medicine)"
                                    class="shortage-tips abc-tipsy abc-tipsy--n"
                                >
                                    <i class="iconfont cis-icon-Attention"></i>
                                </div>

                                <div v-if="medicine.productInfo && medicine.productInfo.medicalFeeGrade" class="medical-fee-grade">
                                    {{ medicine.productInfo.medicalFeeGrade | medicalFeeGrade2Str }}
                                </div>

                                <div v-if="compareRepeat(medicine)" class="repeat-item">
                                    重复
                                </div>
                            </div>
                        </div>

                        <div v-if="isShowMedicalSort" class="show-index-wrapper">
                            {{ index + 1 }}
                        </div>

                        <div
                            v-if="!disabledForm && currentIndex !== index && medicine.medicineCadn"
                            class="delete-icon-wrapper"
                        >
                            <delete-icon
                                @delete="deleteMedicine(index)"
                            ></delete-icon>
                        </div>

                        <div class="count-wrapper">
                            <abc-form-item
                                :required="!disabledForm && !!medicine.medicineCadn"
                                :validate-event="validateNumberWithoutZero"
                            >
                                <abc-input
                                    ref="chineseMedicineInputs"
                                    v-model.number="medicine.unitCount"
                                    v-abc-focus-selected
                                    placeholder="用量"
                                    :disabled="disabledForm"
                                    size="small"
                                    type="number"
                                    :input-custom-style="{
                                        padding: '3px 16px 3px 0', margin: '0 -1px 0 0'
                                    }"
                                    class="count"
                                    :max-length="6"
                                    :config="{
                                        formatLength: 2, supportZero: true
                                    }"
                                    @left="handleCountLeft(index)"
                                    @right="handleCountRight(index)"
                                    @up="handleCountUp(index)"
                                    @down="handleCountDown(index)"
                                    @focus="currentCountIndex = index"
                                    @blur="currentCountIndex = -1"
                                    @enter="enterEvent"
                                    @input="calcUsageDays"
                                >
                                </abc-input>
                                <span class="input-append-unit">{{ medicine.unit || 'g' }}</span>
                            </abc-form-item>
                        </div>

                        <abc-form-item
                            v-if="medicine.medicineCadn"
                            class="chinese-special-requirement"
                            :class="{
                                'show-input': showSpecialRequirement(medicine, index)
                            }"
                        >
                            <select-usage
                                ref="requirementInput"
                                v-model="medicine.remark"
                                :pr-form-item="medicine"
                                type="specialRequirement"
                                placeholder="煎法"
                                placement="bottom-start"
                                :can-change-charge-type="adviceRule.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY"
                                :readonly="false"
                                :tabindex="-1"
                                :disabled="disabledForm"
                                :index="index"
                                :is-open-source="isOpenSource"
                                @left="handleRemarkLeft(index)"
                                @right="handleRemarkRight(index)"
                                @up="handleRemarkUp(index)"
                                @down="handleRemarkDown(index)"
                                @input="() => handleRemarkInput(medicine, index)"
                                @enter="enterEvent"
                                @change="selectChineseMedicineSpecial"
                            >
                            </select-usage>
                        </abc-form-item>

                        <div v-if="showMedicalSpec(medicine)" class="diff-spec">
                            {{ medicine.productInfo | getSpec }}
                        </div>
                    </div>
                </transition-group>
            </draggable>
        </div>

        <div class="prescription-footer" style="justify-content: space-between;">
            <div class="chinese-description">
                <div class="dose-count">
                    <abc-form-item
                        :required="needRequired && !disabledForm"
                    >
                        <abc-input
                            ref="doseCount"
                            v-model="adviceRule.dosageCount	"
                            v-abc-focus-selected
                            class="count-center"
                            :width="82"
                            :input-custom-style="{ padding: '3px 21px 3px 6px' }"
                            :disabled="disabledForm"
                            :max-length="4"
                            type="number"
                            size="small"
                            @right="changeInputPointer('usages')"
                            @enter="enterEvent"
                            @change="handleDoseCountChange"
                        >
                        </abc-input>
                        <div class="input-append-unit" style="font-size: 14px;">
                            剂
                        </div>
                    </abc-form-item>
                </div>

                <div class="usages">
                    <abc-form-item :required="needRequired && !disabledForm">
                        <select-usage
                            v-model="adviceGroup.usage"
                            placeholder="用法"
                            :width="50"
                            :max-length="10"
                            size="small"
                            placement="bottom-start"
                            :disabled="disabledForm"
                            :options="chineseMedicineConfig.usages"
                            @left="changeInputPointer('dose-count')"
                            @right="changeInputPointer(isSpecialUsages ? 'freq' : 'daily-dosage')"
                            @enter="enterEvent"
                            @change="selectChineseMedicineUsage"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <div v-if="!isSpecialUsages" class="daily-dosage">
                    <abc-form-item>
                        <abc-autocomplete
                            v-model="adviceRule.dailyDosage"
                            :width="70"
                            :inner-width="72"
                            :max-length="10"
                            readonly
                            placeholder="剂量"
                            :disabled="disabledForm"
                            :focus-show="true"
                            size="small"
                            :fetch-suggestions="chineseMedicineDailyDosage"
                            :filter-suggestions="chineseMedicineDailyDosage"
                            @left="changeInputPointer('usages')"
                            @right="changeInputPointer('freq')"
                            @enter="enterEvent"
                            @enterEvent="selectChineseMedicineDailyDosage"
                        >
                            <template slot="suggestions" slot-scope="props">
                                <dt
                                    class="suggestions-item"
                                    :class="{ selected: props.index === props.currentIndex }"
                                    @click="selectChineseMedicineDailyDosage(props.suggestion)"
                                >
                                    {{ props.suggestion.name }}
                                </dt>
                            </template>
                        </abc-autocomplete>
                    </abc-form-item>
                </div>

                <div class="freq">
                    <abc-form-item>
                        <select-usage
                            v-model="adviceGroup.freq"
                            placeholder="频率"
                            :width="72"
                            :max-length="10"
                            size="small"
                            placement="bottom-start"
                            :disabled="disabledForm"
                            :options="freqOptions"
                            @left="changeInputPointer('daily-dosage')"
                            @right="changeInputPointer('usage-level')"
                            @enter="enterEvent"
                            @change="selectChineseMedicineFreq"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <div class="usage-level">
                    <abc-form-item>
                        <select-usage
                            v-model="adviceRule.singleDosageCount"
                            placeholder="服用量"
                            :width="82"
                            :max-length="10"
                            size="small"
                            placement="bottom-start"
                            :disabled="disabledForm"
                            :options="usageLevelOptions"
                            @left="changeInputPointer('freq')"
                            @right="changeInputPointer(isSpecialUsages ? 'usage-days' : 'requirement')"
                            @enter="enterEvent"
                            @input="calcUsageDays"
                            @change="selectChineseMedicineUsageLevel"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <div v-if="isSpecialUsages" class="usage-days">
                    <abc-form-item>
                        <select-usage
                            v-model="adviceRule.usageDays"
                            placeholder="服用天数"
                            :width="70"
                            :inner-width="72"
                            :max-length="10"
                            size="small"
                            placement="bottom-start"
                            :readonly="false"
                            :disabled="disabledForm"
                            :options="chineseMedicineConfig.usageDays"
                            @left="changeInputPointer('usage-level')"
                            @right="changeInputPointer('requirement')"
                            @enter="enterEvent"
                            @change="selectChineseMedicineUsageDays"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <div class="requirement">
                    <abc-form-item>
                        <select-usage
                            v-model="adviceRule.remark"
                            placeholder="备注"
                            :width="114"
                            :max-length="200"
                            size="small"
                            placement="bottom-start"
                            :readonly="false"
                            :disabled="disabledForm"
                            :options="requirementOptions"
                            @left="changeInputPointer(isSpecialUsages ? 'usage-days' : 'usage-level')"
                            @enter="enterEvent"
                            @change="selectChineseMedicineRequirement"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>
            </div>

            <abc-money :value="displayTotalPrice" is-show-space :to-fixed2="false">
            </abc-money>
        </div>

        <div v-if="showProcessInfo" class="prescription-external">
            <div class="external-info-item">
                <label>加工</label>
                <div class="content" @click="handleClickProcessInfo">
                    {{ processInfoStr }}
                </div>
            </div>
        </div>

        <local-process-dialog
            v-if="showProcessDialog"
            v-model="showProcessDialog"
            :process-remark="adviceRule.processInfo.remark"
            :fee-type-id="adviceRule.processInfo.feeTypeId"
            :fee-category-id="adviceRule.processInfo.feeCategoryId"
            :usage-type="adviceRule.processInfo.usageType"
            :usage-sub-type="adviceRule.processInfo.usageSubType"
            :process-bag-unit-count="adviceRule.processInfo.perDosageBagCount"
            :total-process-count="adviceRule.processInfo.totalBagCount"
            :process-price="adviceRule.processInfo.price"
            :dosage-count="adviceRule.dosageCount"
            :group-item="adviceGroup"
            :advice-item="adviceRule"
            @confirm="confirmProcessHandler"
        ></local-process-dialog>

        <common-prescription-dialog
            v-if="dialogVisible"
            v-model="dialogVisible"
            type="chinese"
            :prescription-form="form"
        ></common-prescription-dialog>

        <goods-select-dialog
            v-if="selectDialogVisible"
            v-model="selectDialogVisible"
            :scene-type="SearchSceneTypeEnum.hospital"
            :ward-area-id="patientWardId"
            :default-category-key="defaultCategoryKey"
            :category-range="[defaultCategoryKey]"
            @onSelectGoods="batchSelect"
        ></goods-select-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import CDSSAPI from 'api/cdss/index';
    import ChargeAPI from 'api/charge';

    // vue lib
    import { mapGetters } from 'vuex';
    import Draggable from 'vuedraggable';
    import { CATEGORY_TYPE_ENUM } from 'src/views/common/goods-search/constants';
    import { SearchSceneTypeEnum } from 'views/common/enum.js';

    // components
    import DeleteIcon from '@/views/layout/delete-icon/delete-icon';
    import MedicineAutoComplete from '@/views/layout/prescription/common/chinese-medicine-autocomplete.vue';
    import CommonPrescriptionDialog from '@/views/layout/prescription/common/common-prescription-dialog';
    import SelectUsage from '@/views/layout/select-group/index.vue';
    import LocalProcessDialog from './local-process-dialog.vue';
    import VendorsPopover from 'src/views/air-pharmacy/vendors-popover';
    import GoodsSelectDialog from 'src/views/layout/goods-select-dialog/index.vue';

    // utils
    import {
        validateMobile, validateNumber, isDisabledGoods, validateNumberWithoutZero,
    } from 'utils/validate';
    import { numToChinese } from 'utils/index';
    import { formatMoney } from 'src/filters/index';
    import {
        GoodsTypeIdEnum,
        PharmacyTypeEnum,
    } from '@abc/constants';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import { ChargeFormStatusEnum } from '@/service/charge/constants.js';
    import {
        AdviceRuleType,
        DailyDosageList,
        ST_FREQ,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import { calcChineseStopTime } from '@/views-hospital/medical-prescription/utils/chinese-calcuelate.js';
    import { getFreqInfoByFirst } from '@/views-hospital/medical-prescription/utils/format-advice.js';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import { getPrescriptionItemStruct } from 'views/layout/prescription/utils';

    export default {
        components: {
            MedicineAutoComplete,
            DeleteIcon,
            CommonPrescriptionDialog,
            Draggable,
            SelectUsage,
            LocalProcessDialog,
            VendorsPopover,
            GoodsSelectDialog,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            adviceGroup: Object,
            adviceRule: Object,
            // 判断开出来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            needCheckStock: {
                type: Boolean,
                default: true,
            },
            showDetailPrice: {
                type: Boolean,
                default: true,
            },
            showTotalPrice: {
                type: Boolean,
                default: true,
            },
            formsLength: Number,
            formIndex: Number,
            status: Number,
            disabled: Boolean,
            patientInfo: Object,
            medicalRecord: Object,
            treatOnlineClinicId: [String, Number],
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            canChangePharmacyType: {
                type: Boolean,
                default: true,
            },
            adviceRuleType: Number,
            adviceGroupType: Number,
            displayTotalPrice: {
                type: [String, Number],
                default: '',
            },
        },
        data() {
            return {
                CATEGORY_TYPE_ENUM,
                SearchSceneTypeEnum,
                AdviceRuleType,
                ChargeFormStatusEnum,
                PharmacyTypeEnum,
                commonPrescriptionName: '',

                currentIndex: -1,
                currentCountIndex: -1,

                showDeleteConfirm: false,
                dialogVisible: false,
                showProcessDialog: false,
                showAirProcessDialog: false,
                dragging: false,
                validateNumber,
                validateMobile,

                prescriptionTotal: 0,
                minimum: null,
                vendorAvailableMedicalStates: [],

                selectDialogVisible: false,
                defaultCategoryKey: undefined,
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'chineseMedicineConfig',
                'currentClinic',
                'clinicConfig',
                'chinesePRUsageDefault',
                'dispensingConfig',
                'allProcessUsages',
                'clinicBasic',
                'pharmacyRuleList',
            ]),
            ...mapGetters('airPharmacy', [
                'clinicCanUseAirPharmacy',
            ]),
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            otherPharmacyTotalTips() {
                const {
                    usage,
                } = this.adviceGroup;
                const { totalCount } = this.totalWeightInfo; // 总重量
                let produceCount = 0;
                switch (usage) {
                    case '制膏':
                        produceCount = (totalCount * 0.4).toFixed(1);
                        return `预计成品总重${produceCount}g（出膏量约为药材总重的40%）`;
                    case '制丸':
                        produceCount = (totalCount * 0.8).toFixed(1);
                        return `预计成品总重${produceCount}g（制丸成品约为药材总重的80%）`;
                    case '打粉':
                        produceCount = (totalCount * 0.8).toFixed(1);
                        return `预计成品总重${produceCount}g（打粉成品约为药材总重的80%）`;
                    default:
                        return '';
                }
            },
            patientWardId() {
                return this.$abcPage.$store.currentPatientWardId;
            },
            piecesDefaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList, {
                    wardAreaId: this.patientWardId,
                    goodsInfo: {
                        typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                    },
                    processInfo: this.adviceRule?.processInfo,
                });
            },
            granuleDefaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList, {
                    wardAreaId: this.patientWardId,
                    goodsInfo: {
                        typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE,
                    },
                    processInfo: this.adviceRule?.processInfo,
                });
            },

            executeTimeList() {
                return this.allExecuteTimeList?.map((item) => {
                    return {
                        ...item,
                        name: item.code,
                    };
                }) || [];
            },

            specification() {
                return this.adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_GRANULES ? '中药颗粒' : '中药饮片';
            },

            chinesePrescriptionSupportMix() {
                const {
                    settings,
                } = this.clinicBasic.outpatient;
                // 中药支持 饮片颗粒混开
                return settings.chinesePrescriptionSupportMix;
            },

            // 空处方删除不需要确认弹窗
            deleteNeedConfirm() {
                return this.medicineList.some((item) => {
                    return item.medicineCadn;
                });
            },

            requirementOptions() {
                return this.chineseMedicineConfig.requirement.slice();
            },

            translateH() {
                if (this.formsLength === 1) return '';
                return numToChinese(this.formIndex + 1);
            },

            /**
             * @desc 能否切换药房类型
             */
            showChangePharmacyType() {
                return this.isOpenSource && this.canChangePharmacyType;
            },

            showDecoction() {
                return this.dispensingConfig && this.dispensingConfig.isDecoction;
            },
            disabledForm() {
                return this.disabled;
            },
            needRequired() {
                return !!this.adviceRule.items.filter((item) => item.goodsId || item.medicineCadn).length;
            },
            /**
             * @desc 显示显示重量信息
             */
            totalWeightInfo() {
                const kinds = [];
                let count = 0;
                let totalCount = 0;
                let str = '';

                this.medicineList.forEach((item) => {
                    if (item.medicineCadn && item.unitCount) {
                        if (item.goodsId && item.unit === 'g') {
                            count += +item.unitCount || 0;
                        }
                        if (kinds.indexOf(item.medicineCadn) === -1) {
                            kinds.push(item.medicineCadn);
                        }
                    }
                });
                if (kinds.length) {
                    totalCount = count * (this.adviceRule.dosageCount || 0);

                    count = Number(count.toFixed(2));
                    totalCount = Number(totalCount.toFixed(2));

                    str += `${kinds.length} 味, 单剂 ${count}g, 总计 ${totalCount}g`;

                }
                return {
                    desc: str,
                    totalCount,
                };
            },

            medicineList: {
                get() {
                    return this.adviceRule.items;
                },
                set(val) {
                    this.adviceRule.items = val;
                },
            },
            // 特殊的用法，需要增加服用天数字段，隐藏剂量字段
            isSpecialUsages() {
                const _arr = ['制膏', '制丸', '打粉'];
                return _arr.indexOf(this.adviceGroup.usage) > -1;
            },

            freqOptions() {
                return this.executeTimeList.filter((item) => {
                    return item?.enableAdviceRuleTypes?.includes(this.adviceRuleType) && item.code !== ST_FREQ;
                });
            },

            usageLevelOptions() {
                const {
                    usageLevel,
                    zhiGaoUsageLevel,
                    zhiWanUsageLevel,
                    daFenUsageLevel,
                    keLiChongFuUsageLevel,
                } = this.chineseMedicineConfig;

                if (this.specification?.indexOf('颗粒') > -1 && this.adviceGroup.usage === '冲服') {
                    return keLiChongFuUsageLevel;
                }

                switch (this.adviceGroup.usage) {
                    case '制膏':
                        return zhiGaoUsageLevel;
                    case '制丸':
                        return zhiWanUsageLevel;
                    case '打粉':
                        return daFenUsageLevel;
                    default:
                        return usageLevel;
                }
            },

            /**
             * @desc 单位为g的一剂总重量
             */
            doseTotalWeight() {
                return this.medicineList.reduce((arr, cur) => {
                    if (cur.medicineCadn && cur.unitCount && cur.unit === 'g') {
                        arr = arr + (+cur.unitCount);
                    }
                    return arr;
                }, 0);
            },

            showProcessInfo() {
                const {
                    pharmacyType, // 药房
                    isNeedProcess,
                } = this.adviceRule;
                if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                    return isNeedProcess;
                }
                return false;
            },

            /**
             * @desc 加工展示文案，空中药房，本地药房展示规则区分
             */
            processInfoStr() {
                const {
                    perDosageBagCount = 1,
                    totalBagCount,
                    usageType,
                    usageSubType,
                    price = 0,

                } = this.adviceRule?.processInfo || {};

                const usageTypeInfo = this.allProcessUsages.find((it) => it.type === usageType);
                if (!usageTypeInfo) return '';

                const _arr = [];

                const usageSubTypeInfo = usageTypeInfo.children.find((it) => it.subType === usageSubType);
                if (usageSubTypeInfo) {
                    _arr.push(usageSubTypeInfo.name);
                } else {
                    _arr.push(usageTypeInfo.name);
                }
                // 加工方式为煎药
                if (usageType === 1) {
                    if (perDosageBagCount) {
                        _arr.push(`1剂煎 ${perDosageBagCount} 袋`);
                    }
                    if (totalBagCount) {
                        _arr.push(`共 ${totalBagCount} 袋`);
                    }
                }
                _arr.push(`加工费 ${this.$t('currencySymbol')} ${formatMoney(price)}`);

                return _arr.join('，');
            },
            /**
             * @desc 中药处方药品显示序号满足：1、chain 维度的配置中开启，2、本地药房
             * <AUTHOR>
             * @date 2022/08/26 16:28:54
             */
            isShowMedicalSort() {
                return this.clinicBasic.isChinesePrescriptionShowSort && this.adviceRule.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY;
            },
            dailyDosageList() {
                return DailyDosageList;
            },
        },
        watch: {
            'adviceRule.type': {
                handler(val) {
                    if (val === AdviceRuleType.CHINESE_MEDICINE_TABLETS) {
                        this.defaultCategoryKey = CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_PIECES;
                    } else if (val === AdviceRuleType.CHINESE_MEDICINE_GRANULES) {
                        this.defaultCategoryKey = CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_GRANULE;
                    }
                },
                immediate: true,
            },
        },
        created() {
            this.$store.dispatch('initAllProcessUsages');
            if (!this.disabledForm) {
                this.insertAfterCM();
            }
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            validateNumberWithoutZero,
            /**
             * @desc 当处方上的规格和药品不一致需要展示规格
             * <AUTHOR>
             * @date 2022-10-12 14:32:29
             */
            showMedicalSpec(item) {
                const medicineSpec = item.cMSpec || (item.productInfo && item.productInfo.cMSpec);
                return this.specification !== medicineSpec;
            },
            showSpecialRequirement(medicine, index) {
                return medicine.remark ||
                    this.currentIndex === index ||
                    this.currentCountIndex === index;
            },

            handleDoseCountChange() {
                this.adviceRule.items.forEach((item) => {
                    item.doseCount = this.adviceRule.dosageCount;
                });
                this.calcBagsNumber();
                this.calcProcessFee();
                this.calcUsageDays();
                this.calcStopTime();
            },
            async calcProcessFee() {
                if (!this.adviceRule.isNeedProcess) return;
                const currentData = {
                    feeTypeId: this.adviceRule.processInfo.feeTypeId,
                    usageType: this.adviceRule.processInfo.usageType,
                    usageSubType: this.adviceRule.processInfo.usageSubType,
                    processRemark: this.adviceRule.processInfo.remark,
                    processBagUnitCount: this.adviceRule.processInfo.perDosageBagCount,
                    totalProcessCount: this.adviceRule.processInfo.totalBagCount,
                };

                const postData = {
                    doseCount: this.adviceRule.dosageCount,
                    items: this.adviceRule.items.filter((item) => {
                        return item.medicineCadn && item.unitCount;
                    }).map((item) => {
                        return {
                            name: item.medicineCadn,
                            productId: item.goodsId,
                            productSubType: item.subType,
                            productType: item.type,
                            unit: item.unit,
                            unitCount: item.unitCount,
                        };
                    }) || [],
                    keyId: this.adviceGroup.keyId,
                    processInfo: {
                        processBagUnitCount: currentData.processBagUnitCount,
                        subType: currentData.usageSubType,
                        type: currentData.usageType,
                        feeTypeId: currentData.currentFeeTypeId,
                    },
                };
                const res = await ChargeAPI.localPharmacyCalcProcessFee({
                    forms: [postData],
                });
                if (!res) return;
                const { data } = res;
                const { forms } = data || {};
                if (!forms) return;
                const form = forms[0];

                this.adviceRule.processInfo.price = form.processPrice;
            },
            /**
             * @desc 计算出院时间 停止时间 = 开始时间 + Math.ceil(（总剂数）/ (每日剂数));
             * <AUTHOR>
             * @date 2023-02-28 10:40:40
             */
            calcStopTime() {
                const stopTime = calcChineseStopTime(this.adviceGroup, this.isSpecialUsages);
                console.log('stopTime', stopTime);
                this.$set(this.adviceGroup, 'stopTime', stopTime);
            },
            /**
             * @desc 饮片=》煎服，颗粒=》冲服
             * <AUTHOR>
             * @date 2020/04/03 17:28:25
             * @params
             * @return
             */
            changeSpecification(newSpecification) {
                const oldSpecification = newSpecification === AdviceRuleType.CHINESE_MEDICINE_TABLETS ?
                    AdviceRuleType.CHINESE_MEDICINE_GRANULES : AdviceRuleType.CHINESE_MEDICINE_TABLETS;
                this.adviceRule.type = newSpecification;
                const newSpecificationStr = newSpecification === AdviceRuleType.CHINESE_MEDICINE_TABLETS ? '中药饮片' : '中药颗粒';
                if (this.specification.indexOf('颗粒') > -1) {
                    if (this.adviceGroup.usage === '煎服') {
                        this.$nextTick(() => {
                            this.selectChineseMedicineUsage('冲服');
                        });
                    }
                } else if (this.specification.indexOf('饮片') > -1) {
                    if (this.adviceGroup.usage === '冲服') {
                        this.$nextTick(() => {
                            this.selectChineseMedicineUsage('煎服');
                        });
                    }
                }
                this.$nextTick(() => {
                    this.calcBagsNumber();
                });

                const _diffs = [];
                this.medicineList.forEach((item) => {
                    if (item.medicineCadn) {
                        const { cMSpec } = item;
                        if (cMSpec !== newSpecificationStr) {
                            _diffs.push(item.medicineCadn);
                        }
                    }
                });
                // 获取本地默认药房号
                const defaultPharmacy = newSpecificationStr === '中药颗粒' ? this.granuleDefaultPharmacy : this.piecesDefaultPharmacy;
                if (_diffs.length) {
                    const newSpec = newSpecificationStr.replace('中药', '');

                    this.$confirm({
                        title: `处方药态转换为${newSpec}`,
                        content: [
                            `是否将处方中的所有药品转换为${newSpec}？`,
                        ],
                        customClass: 'change-spec-confirm-dialog',
                        contentStyles: 'width: 360px;padding: 24px',
                        confirmText: '确认转换',
                        onConfirm: async () => {
                            Object.assign(this.adviceRule, {
                                pharmacyType: defaultPharmacy.type,
                                pharmacyNo: defaultPharmacy.no,
                                pharmacyName: defaultPharmacy.name,
                            });
                        },
                        onCancel: () => {
                            this.adviceRule.type = oldSpecification;
                        },
                    });
                } else {
                    Object.assign(this.adviceRule, {
                        pharmacyType: defaultPharmacy.type,
                        pharmacyNo: defaultPharmacy.no,
                        pharmacyName: defaultPharmacy.name,
                    });
                }
            },

            isLastRow(index) {
                const len = this.medicineList.length;
                const row = Math.ceil(len / 4);
                const lastRow = Math.ceil((index + 1) / 4);
                return lastRow >= row;
            },

            startDrag() {
                // 没有name的情况下没有requirementInput
                if (this.$refs.requirementInput) {
                    const inputs = this.$refs.requirementInput;
                    const len = inputs.length;
                    for (let i = 0; i < len; i++) {
                        if (inputs[i].showSuggestions) {
                            inputs[i].handleClose();
                            break;
                        }
                    }
                }

                this.dragging = true;
                $('#medicine-hover-popover').remove();
            },

            showDeleteIcon(medicine) {
                return !this.disabledForm && (medicine.goodsId || medicine.medicineCadn);
            },

            /**
             * @desc 药品禁用 || 库存不足 || 无库存信息
             * <AUTHOR> Yang
             * @date 2021-03-30 15:46:13
             */
            showWarnTips(chineseMedicine) {
                // 自备不校验库存
                if (chineseMedicine.chargeFlag === OutpatientChargeTypeEnum.NO_CHARGE) return false;

                // 不检查库存
                if (!this.needCheckStock) return false;

                // 禁用状态
                if (this.disabledForm) return false;
                const {
                    noStocks,
                    medicineCadn,
                    name,
                    unitCount,
                    stockPieceCount,
                } = chineseMedicine;
                const dosageCount = this.adviceRule.dosageCount || 1;

                // 填写name unitCount后才判断库存信息
                if ((medicineCadn || name)) {
                    const isShortage = dosageCount * unitCount > stockPieceCount;
                    return isDisabledGoods(chineseMedicine).flag || isShortage || noStocks;
                }
                return false;

            },

            warnTips(item) {
                const {
                    noStocks,
                    stockPieceCount,
                    unit,
                } = item;
                if (noStocks) return '无库存';
                const isShortageTips = `库存不足(${stockPieceCount || 0}${unit || 'g'})`;
                return isDisabledGoods(item).tips || isShortageTips;
            },

            editCurrentMedicine(index) {
                if (this.disabledForm) return false;
                this.currentIndex = index;
                this.$nextTick(() => {
                    this.$el.querySelectorAll('.table-td')[index].querySelector('.medicine-autocomplete input').focus();
                });
            },

            /** ----------------------------------------------------------------------
             *  删除中药处方的按钮
             */
            deletePres() {
                this.$emit('close', this.formIndex);
            },

            /**
             * @desc 显式删除中药
             * <AUTHOR>
             * @date 2019/05/29 11:33:28
             */
            deleteMedicine(index) {
                this.medicineList.splice(index, 1);
            },

            /** ----------------------------------------------------------------------
             * 中药每日剂量
             */
            chineseMedicineDailyDosage(queryString, cb) {
                // 调用 callback 返回建议列表的数据
                cb(this.dailyDosageList);
            },

            selectChineseMedicineSpecial(specialRequirement, index) {
                this.medicineList[index].remark = specialRequirement;
            },
            selectChineseMedicineUsage(usage) {
                if (this.adviceGroup.usage === usage) return false;

                this.adviceGroup.usage = usage;

                const {
                    dailyDosage,
                    freq,
                    usageLevel,
                    usageDays,
                } = this.chinesePRUsageDefault[usage] || {};

                this.adviceRule.dailyDosage = dailyDosage || '';
                this.adviceGroup.freq = freq || '';
                this.adviceRule.usageLevel = usageLevel || '';
                this.adviceRule.singleDosageCount = usageLevel || '';
                this.$set(this.adviceRule, 'usageDays', usageDays || '');

                if (this.isSpecialUsages) {
                    this.adviceRule.dailyDosage = '';
                } else {
                    this.$set(this.adviceRule, 'usageDays', '');
                }
                this.calcUsageDays();
                this.calcStopTime();
            },
            selectChineseMedicineDailyDosage(freq) {
                this.adviceRule.dailyDosage = freq.name;
                this.calcBagsNumber();
                this.calcStopTime();
            },

            async selectChineseMedicineFreq(freq) {
                this.adviceGroup.freq = freq;
                this.adviceGroup.freqInfo = getFreqInfoByFirst(this.allExecuteTimeList, freq, true);
                if (!this.adviceGroup.freqInfo) {
                    this.$set(this.adviceGroup.freqInfo, 'firstDayTimings', []);
                    this.$set(this.adviceGroup.freqInfo, 'firstDayFrequency', 0);
                }
                await this.$nextTick();
                this.calcBagsNumber();
                this.calcUsageDays();
                this.calcStopTime();
            },

            selectChineseMedicineUsageLevel(usageLevel) {
                this.adviceRule.usageLevel = usageLevel;
                this.adviceRule.singleDosageCount = usageLevel;
                this.calcBagsNumber();
                this.calcUsageDays();
                this.calcStopTime();
            },

            selectChineseMedicineUsageDays(usageDays) {
                this.adviceRule.usageDays = usageDays;
                this.calcStopTime();
            },

            selectChineseMedicineRequirement(requirement) {
                this.adviceRule.remark = requirement;
            },

            /**
             * @desc 选择中药回调函数
             * <AUTHOR>
             * @date 2019/10/09 15:02:25
             * @params
             * @return
             */
            selectMedicine(newMedicine) {
                this._fromAutocomple = true; // 设置快速回到药品搜索框 flag

                let index = this.findRepeatIndex(newMedicine);
                if (index === -1) {
                    this.medicineList.push(newMedicine);
                }

                // 有重复药品直接聚焦到该药品数量输入框
                index = index > -1 ? index : this.medicineList.length - 1;
                this.$nextTick(() => {
                    this._timer = setTimeout(() => {
                        this.$refs.tableTd[index]
                            .querySelector('.count-wrapper input')
                            .focus();
                    }, 1);
                });
            },

            blurHandle(medicine, $autocomplete) {
                this.currentIndex = -1;
                if (!medicine.medicineCadn) {
                    if ($autocomplete &&
                        typeof $autocomplete.clearQueryString === 'function') {
                        $autocomplete.clearQueryString();
                    }
                }
            },
            handleDoubleClick(event) {
                if (!event.target.value) return;
                event.target.selectionStart = 0;
                event.target.selectionEnd = event.target.value.length;
            },
            async changeMedicine(newMedicine) {
                newMedicine = MedicalPrescriptionService.getAdviceGoodsItem(newMedicine);
                const repeatIndex = this.findRepeatIndex(newMedicine);
                // 有重复的做toast提示
                if (repeatIndex > -1 && this.currentIndex !== repeatIndex) {
                    this.$Toast({
                        message: `处方中已添加${newMedicine.medicineCadn}`,
                        type: 'error',
                        duration: 1500,
                        referenceEl: this.$el.querySelectorAll('.table-td')[this.currentIndex].querySelector('.name-wrapper'),
                    });
                    return false;
                }

                const index = this.currentIndex;
                if (index === -1) return;
                const oldMedicine = this.medicineList[index];
                // 当老的药品和新换的药品 goodsId name 一样则不更新keyId
                if (oldMedicine.goodsId === newMedicine.goodsId && oldMedicine.medicineCadn === newMedicine.medicineCadn) {
                    newMedicine.keyId = oldMedicine.keyId || newMedicine.keyId;
                }
                this.medicineList.splice(index, 1, newMedicine);
                this.currentIndex = -1;
                this.$nextTick(() => {
                    this._timer = setTimeout(() => {
                        this.$el
                            .querySelectorAll('.table-td')[index]
                            .querySelector('.count-wrapper input')
                            .focus();
                    }, 1);
                });
                this.insertAfterCM();
                if (newMedicine.medicineCadn) {
                    // 自动填写 煎法
                    const { data } = await CDSSAPI.fetchSpecialRequirementRememberSetting({
                        goodsId: newMedicine.goodsId,
                        cadn: newMedicine.medicineCadn,
                    });
                    if (data && data.isRemember) {
                        newMedicine.remark = data.specialRequirement;
                    }
                }
            },

            /**
             * @desc 在后面新增一个中药item
             * <AUTHOR>
             * @date 2020/04/23 11:31:58
             */
            insertAfterCM() {
                if (!this.medicineList.find((item) => !item.goodsId && !item.medicineCadn)) {
                    this.medicineList.push({
                        unit: 'g',
                        goodsId: null,
                        medicineCadn: '',
                        name: '',
                        remark: '',
                        unitCount: '',
                    });
                }
            },
            /** ----------------------------------------------------------------------
             *  比较是否有重复项目
             */
            compareRepeat(item) {
                if (this.disabledForm) return false;
                let count = 0;
                this.medicineList.forEach((m) => {
                    if (m.medicineCadn &&
                        m.medicineCadn === item.medicineCadn &&
                        (m.goodsId || '') === (item.goodsId || '')) {
                        count++;
                    }
                });
                return count >= 2;
            },

            /**
             * @desc 找到已有药品的index
             * <AUTHOR>
             * @date 2018/07/10 15:45:07
             */
            findRepeatIndex(item) {
                return this.medicineList.findIndex((m) => {
                    return m.medicineCadn &&
                        m.medicineCadn === item.medicineCadn &&
                        (m.goodsId || '') === (item.goodsId || '');
                });
            },

            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                // 快速回到药品搜索input
                if (this._fromAutocomple) {
                    this.$nextTick(() => {
                        this.$el.querySelector('.medicine-autocomplete input').focus();
                        this._fromAutocomple = false;
                    });
                    return false;
                }

                // 找到所有的非disabled的input输入框
                const inputs = $(this.$el).find('.abc-input__inner').not(':disabled');

                const targetIndex = inputs.index(e.target);

                let nextInput = inputs[targetIndex + 1];
                if (!nextInput) {
                    this.$el.querySelector('.medicine-autocomplete input').focus();
                    return false;
                }

                if (nextInput.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }
                nextInput && this.$nextTick(() => {
                    nextInput.select();
                    nextInput.focus();
                });
            },

            async clickDecoction() {
                if (this.adviceRule.isNeedProcess) {
                    this.adviceRule.isNeedProcess = 0;
                    this.adviceRule.processInfo = {
                        perDosageBagCount: '',
                        remark: '',
                        totalBagCount: '',
                        usageType: '',
                        usageSubType: '',
                        feeTypeId: '',
                        feeCategoryId: '',
                    };
                } else {
                    await this.calcBagsNumber(true);
                    this.showProcessDialog = true;
                }
            },

            // 保存模板
            saveCommon() {
                if (this.medicineList.filter((item) => item.medicineCadn).length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '处方药品为空，不能保存为模板',
                    });
                    return false;
                }
                this.dialogVisible = true;
            },

            /**
             * @desc 确认本地加工信息
             * <AUTHOR>
             * @date 2022-06-17 16:17:07
             */
            async confirmProcessHandler(usageInfo) {
                const {
                    feeTypeId,
                    usageType,
                    usageSubType,
                    processRemark,
                    processBagUnitCount,
                    totalProcessCount,
                    price,
                    displayName,
                    feeCategoryId,
                } = usageInfo;
                this.adviceRule.isNeedProcess = 1;
                this.adviceRule.processInfo = {
                    perDosageBagCount: processBagUnitCount,
                    remark: processRemark,
                    totalBagCount: totalProcessCount,
                    feeTypeId,
                    usageType,
                    usageSubType,
                    price,
                    displayName,
                    feeCategoryId,
                };

                const defaultPharmacy = this.adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_TABLETS ?
                    this.piecesDefaultPharmacy :
                    this.granuleDefaultPharmacy;
                Object.assign(this.adviceRule, {
                    pharmacyType: defaultPharmacy.type,
                    pharmacyNo: defaultPharmacy.no,
                    pharmacyName: defaultPharmacy.name,
                });
            },

            handleRemarkInput(item, index) {
                if (item.remark && item.remark.indexOf('【自备】') > -1) {
                    this.$set(item, 'chargeFlag', OutpatientChargeTypeEnum.NO_CHARGE);
                    this.$Toast({
                        message: '备注为【自备】，该药品将不会纳入划价收费',
                        duration: 1500,
                        referenceEl: this.$refs.requirementInput[index].$el,
                    });
                } else {
                    this.$set(item, 'chargeFlag', OutpatientChargeTypeEnum.DEFAULT);
                }
            },


            handleNameLeft(index) {
                index -= 1;
                if (index < 0) return;
                this.moveCountPointer(index);
            },
            handleNameRight(index) {
                this.moveCountPointer(index);
            },
            handleNameUp(index) {
                index -= 4;
                if (index < 0) return;
                this.moveNamePointer(index);
            },
            handleNameDown(index) {
                index += 4;
                if (index > this.medicineList.length - 1) {
                    return;
                }
                this.moveNamePointer(index);
            },

            handleCountLeft(index) {
                this.moveNamePointer(index);
            },
            handleCountRight(index) {
                index += 1;
                if (index > this.medicineList.length - 1) return;
                this.moveNamePointer(index);
            },
            handleCountUp(index) {
                index -= 4;
                if (index < 0) return;
                this.moveRemarkPointer(index);
            },
            handleCountDown(index) {
                this.moveRemarkPointer(index);
            },

            handleRemarkLeft(index) {
                index -= 1;
                if (index < 0) return;
                this.moveRemarkPointer(index);
            },
            handleRemarkRight(index) {
                index += 1;
                if (index > this.medicineList.length - 1) return;
                this.moveRemarkPointer(index);
            },
            handleRemarkUp(index) {
                this.moveCountPointer(index);
            },
            handleRemarkDown(index) {
                index += 4;
                if (index > this.medicineList.length - 1) {
                    this.changeInputPointer('dose-count');
                    return;
                }
                this.moveCountPointer(index);
            },

            changeInputPointer(className) {
                const $input = this.$el.querySelector(`.${className}`);
                if ($input) {
                    const nextInput = $input.querySelector('input');
                    this.focusSelectHandle(nextInput);
                }
            },


            moveNamePointer(index) {
                this.currentIndex = index;
                this.$nextTick(() => {
                    const $tableTd = this.$el.querySelectorAll('.table-td');
                    const nextInput = $tableTd[index].querySelector('.name-wrapper input');
                    this.focusSelectHandle(nextInput);
                });
            },
            moveCountPointer(index) {
                const $tableTd = this.$el.querySelectorAll('.table-td');
                const nextInput = $tableTd[index].querySelector('.count-wrapper input');
                this.focusSelectHandle(nextInput);
            },
            moveRemarkPointer(index) {
                this.currentCountIndex = index;
                this.$nextTick(() => {
                    const $tableTd = this.$el.querySelectorAll('.table-td');
                    const nextInput = $tableTd[index].querySelector('.chinese-special-requirement input');
                    this.focusSelectHandle(nextInput);
                });
            },

            focusSelectHandle($input) {
                this._timer && clearTimeout(this._timer);
                if (!$input) return;
                $input.selectionStart = 0;
                $input.selectionEnd = $input.value ? $input.value.length : 0;
                this._timer = setTimeout(() => {
                    $input.focus && $input.focus();
                    $input.select && $input.select();
                }, 0);
            },

            handleClickProcessInfo() {
                if (this.disabledForm) return false;
                this.showProcessDialog = true;
            },
            /**
             * @desc 计算一剂煎几袋、总袋数
             * @desc 用法用量：x日y剂，1日z次
             * @desc 每剂煎药袋数 =(z * x) / y;
             */
            async calcBagsNumber(needCalculate = false) {
                try {
                    const { freq } = this.adviceGroup;
                    const {
                        dailyDosage,
                        dosageCount,
                        pharmacyType,
                        singleDosageCount,
                        isNeedProcess,
                    } = this.adviceRule;
                    if (!isNeedProcess && !needCalculate) return;
                    if (!freq || !dailyDosage) {
                        this.$set(this.adviceRule.processInfo, 'perDosageBagCount', '');
                        this.$set(this.adviceRule.processInfo, 'totalBagCount', '');
                        return;
                    }
                    const {
                        bagUnitCount,
                        bagTotalCount,
                    } = await ChargeAPI.calculateBagCount({
                        doseCount: dosageCount,
                        dailyDosage,
                        freq,
                        pharmacyType,
                        usageLevel: singleDosageCount,
                        type: this.specification === '中药饮片' ? 1 : 2,
                    });
                    this.$set(this.adviceRule.processInfo, 'perDosageBagCount', bagUnitCount);
                    this.$set(this.adviceRule.processInfo, 'totalBagCount', bagTotalCount);
                } catch (e) {
                    console.error(e);
                }
            },

            // 批量选药
            batchSelect(selectedList) {
                selectedList.forEach((item) => {
                    this.currentIndex = this.medicineList.length - 1;
                    const prescriptionItem = getPrescriptionItemStruct(item);
                    this.changeMedicine(prescriptionItem);
                });
            },

            calcUsageDays() {
                if (!this.isSpecialUsages) return;
                const {
                    dosageCount,
                    singleDosageCount,
                } = this.adviceRule;
                const {
                    usage,
                    freq,
                } = this.adviceGroup;
                const finishedRateMap = {
                    '制膏': 0.4,
                    '制丸': 0.8,
                    '打粉': 0.8,
                };
                const finishedRate = finishedRateMap[usage];

                const matchedUL = singleDosageCount.match(/^每次([0-9]+)g$/);
                const _usageLevelValue = matchedUL ? matchedUL[1] : 0;
                const matchedFreq = freq.match(/^1日([0-9]+)次$/);
                const _freqValue = matchedFreq ? matchedFreq[1] : 0;

                const value = this.doseTotalWeight *
                    (dosageCount || 0) *
                    finishedRate / (_usageLevelValue * _freqValue);

                if (value && !isNaN(value) && isFinite(value)) {
                    const days = Math.ceil(value);
                    this.$set(this.adviceRule, 'usageDays', `约服${days}天`);
                } else {
                    this.$set(this.adviceRule, 'usageDays', '');
                }
            },
        },
    };
</script>
