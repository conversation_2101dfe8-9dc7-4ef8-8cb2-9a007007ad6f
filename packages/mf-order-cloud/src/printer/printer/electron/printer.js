import Printer from '../index.js';
import { ABC_ELECTION } from '../../utils/electron.js';

export default class ElectronPrinter extends Printer {
    constructor(printTask) {
        super(printTask);
        this.printTask = printTask;
    }


    silentPrint() {
        const {
            template,
            printConfig,
        } = this.printTask;
        ABC_ELECTION.print(printConfig, template, this.printTask.extra);
    }
}
