<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="keywords" content="ABC数字医疗云、ABC诊所管家、电子病历、诊所系统、诊所软件、门诊系统、医疗软件、病历软件、医疗系统、连锁诊所、智能诊断、用药审核、处方安全、诊所运营、诊所开办、在线咨询、在线问诊、诊所开店、诊所创收"/>
    <meta name="description" content="ABC数字医疗云、ABC诊所管家，移动互联网时代好用的诊所管家，包含预约挂号、电子病历、检验检查、药品进销存、患者管理、连锁管理，向诊所提供强大的CIS系统和微诊所解决方案。">
    <title>ABC数字医疗云</title>
</head>
<body>
<!--[if lte IE 10]>
<div id="ie-low-version" style="width:100%; line-height: 30px; padding: 10px 0;color: #fff; background-color: #f56730;border: 1px solid #f56730; position: fixed; top: 0; left: 0; z-index: 19900206; text-align: center;">为了获取更好的使用体验，请升级为IE10以上版本或chrome浏览器。</div>
<![endif]-->
<div id="app"></div>

<% htmlWebpackPlugin.options.cdnConfig && htmlWebpackPlugin.options.cdnConfig.forEach(function(item){ %>
<script type="text/javascript" src="<%= item.url %>" crossorigin="anonymous"></script>
<% }) %>
<script defer type="text/javascript" src="//static-common-cdn.abcyun.cn/js/aliyun-oss-sdk-4.4.4.min.js" crossorigin="anonymous"></script>
<script type="text/javascript">
    window.onload = function() {
        if(location.host === '') {
            location.href = 'abcyun.cn'
        }
        if(location.host === 'test.abczs.cn') {
            document.title = '测试环境';
        }
    }
</script>
<script>
    window.nvcCallback = null;
    window.NVC_Opt = {
        appkey:'FFFF0N00000000009829',
        scene:'ic_other',
        renderTo:'#captcha',
        trans: {"key1": "code0", "nvcCode":200},
        elements: [
            '//img.alicdn.com/tfs/TB17cwllsLJ8KJjy0FnXXcFDpXa-50-74.png',
            '//img.alicdn.com/tfs/TB17cwllsLJ8KJjy0FnXXcFDpXa-50-74.png'
        ],
        success:function(data){
            // data为getNVCVal()的值，此函数为二次验证滑动或者刮刮卡通过后的回调函数
            // data跟业务请求一起上传，由后端请求AnalyzeNvc接口，接口会返回100或者900
            window.nvcCallback && window.nvcCallback(data);
        },
        // bg_back_prepared: '//img.alicdn.com/tps/TB1skE5SFXXXXb3XXXXXXXXXXXX-100-80.png',
        // bg_front: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAMAAADY1yDdAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAADUExURefk5w+ruswAAAAfSURBVFjD7cExAQAAAMKg9U9tCU+gAAAAAAAAAIC3AR+QAAFPlUGoAAAAAElFTkSuQmCC',
        // obj_ok: '//img.alicdn.com/tfs/TB1rmyTltfJ8KJjy0FeXXXKEXXa-50-74.png',
        // bg_back_pass: '//img.alicdn.com/tfs/TB1KDxCSVXXXXasXFXXXXXXXXXX-100-80.png',
        // obj_error: '//img.alicdn.com/tfs/TB1q9yTltfJ8KJjy0FeXXXKEXXa-50-74.png',
        // bg_back_fail: '//img.alicdn.com/tfs/TB1w2oOSFXXXXb4XpXXXXXXXXXX-100-80.png',
        // upLang:{"cn":{
        //         _ggk_guide: "请摁住鼠标左键，刮出两面盾牌",
        //         _ggk_success: "恭喜您成功刮出盾牌<br/>继续下一步操作吧",
        //         _ggk_loading: "加载中",
        //         _ggk_fail: ['呀，盾牌不见了<br/>请', "javascript:noCaptcha.reset()", '再来一次', '或', "http://survey.taobao.com/survey/QgzQDdDd?token=%TOKEN", '反馈问题'],
        //         _ggk_action_timeout: ['我等得太久啦<br/>请', "javascript:noCaptcha.reset()", '再来一次', '或', "http://survey.taobao.com/survey/QgzQDdDd?token=%TOKEN", '反馈问题'],
        //         _ggk_net_err: ['网络实在不给力<br/>请', "javascript:noCaptcha.reset()", '再来一次', '或', "http://survey.taobao.com/survey/QgzQDdDd?token=%TOKEN", '反馈问题'],
        //         _ggk_too_fast: ['您刮得太快啦<br/>请', "javascript:noCaptcha.reset()", '再来一次', '或', "http://survey.taobao.com/survey/QgzQDdDd?token=%TOKEN", '反馈问题']
        //     }
        // }
    }

</script>
<script src="//g.alicdn.com/sd/nvc/1.1.112/guide.js" crossorigin="anonymous"></script>
<script src="//g.alicdn.com/sd/smartCaptcha/0.0.4/index.js" crossorigin="anonymous"></script>
<script src="//g.alicdn.com/sd/quizCaptcha/0.0.1/index.js" crossorigin="anonymous"></script>
</body>
</html>
