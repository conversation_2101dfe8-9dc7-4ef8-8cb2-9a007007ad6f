import ElectronPrinter from './printer/electron/printer.js';
export default class PrinterService {
    constructor() {
        this.queue = [];
        this.printingTask = null;
    }

    pushQueue(task, callback) {
        if (typeof callback === 'function') {
            this.callback = callback;
        }
        if (task instanceof Array) {
            this.queue = this.queue.concat(task);
        } else {
            this.queue.push(task);
        }
        this.checkQueue();
    }

    checkQueue() {
        if (this.queue.length && !this.printingTask) {
            this.printingTask = this.queue.pop();
            this.print();
        }
    }

    async print() {
        console.log('print start');
        await this.printingTask.getTemplateStr();
        this.printingTask.getPageWidthAndHeight();
        let printInstance = null;

        printInstance = new ElectronPrinter(this.printingTask);

        printInstance?.silentPrint?.();

        // eslint-disable-next-line abc/no-timer-id
        setTimeout(() => {
            console.log('print finish');
            if (this.callback) {
                this.callback();
                this.callback = null;
            }
            if (typeof this.printingTask.onPrintSuccess === 'function') {
                this.printingTask.onPrintSuccess();
            }
            this.printingTask = null;
            this.checkQueue();
        },100);
    }

    static getPrintService() {
        if (!this.service) {
            this.service = new PrinterService();
        }
        return this.service;
    }
}
