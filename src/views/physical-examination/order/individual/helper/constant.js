
import { formatDate } from '@abc/utils-date';
import {
    OrderStatusEnum,
    PEBusinessTypeEnum,
    PEBusinessTypeLabel,
    PEPayStatusEnum,
} from 'views/physical-examination/constants';
import { MaritalStatusLabel } from 'views/crm/constants';


export const PE_BUSINESS_TYPE_OPTIONS = Object.freeze([
    {
        label: '普通体检', value: PEBusinessTypeEnum.GENERAL,
    },
    {
        label: '公卫体检', value: PEBusinessTypeEnum.PUBLIC_HEALTH,
    },
]);

export const PE_PAY_STATUS_OPTIONS = Object.freeze([
    {
        label: '未结算', value: PEPayStatusEnum.UNCHARGED,
    },
    {
        label: '部分结算', value: PEPayStatusEnum.PART_CHARGED,
    },
    {
        label: '已结算', value: PEPayStatusEnum.CHARGED,
    },
    {
        label: '已退费', value: PEPayStatusEnum.REFUNDED,
    },
    {
        label: '已关闭', value: PEPayStatusEnum.CLOSED,
    },
]);

export const PE_ORDER_STATUS_OPTIONS = Object.freeze([
    {
        label: '待预约', value: OrderStatusEnum.WAIT_RESERVE,
    },
    {
        label: '待登记', value: OrderStatusEnum.WAIT_REGISTER,
    },
    {
        label: '体检中', value: OrderStatusEnum.PE_ING,
    },
    {
        label: '待交表', value: OrderStatusEnum.WAIT_SUBMIT_FORM,
    },
    {
        label: '已交表', value: OrderStatusEnum.WAIT_EXAM_REPORT,
    },
    {
        label: '待总评', value: OrderStatusEnum.WAIT_REPORT,
    },
    {
        label: '待审核', value: OrderStatusEnum.WAIT_REPORT_CHECK,
    },
    {
        label: '已驳回', value: OrderStatusEnum.EXAM_REPORT_REJECTED,
    },
    {
        label: '待发布', value: OrderStatusEnum.WAIT_REPORT_RELEASED,
    },
    {
        label: '已发布', value: OrderStatusEnum.REPORT_RELEASED,
    },
]);

export const renderConfig = Object.freeze(
    {
        hasInnerBorder: false,
        list: [
            {
                label: '订单号',
                key: 'orderNo',
                style: {
                    textAlign: 'left',
                    width: '142px',
                    maxWidth: '142px',
                },
                // eslint-disable-next-line no-unused-vars
                customRender(h, row) {
                    return (
                        <div style="color: #005ed9;" class="table-cell">{ row.orderNo }</div>
                    );
                },
            },
            {
                label: '客户姓名',
                key: 'patientName',
                style: {
                    textAlign: 'left',
                    width: '120px',
                    maxWidth: '120px',
                },
            },
            {
                label: '性别',
                key: 'patientSex',
                style: {
                    textAlign: 'center',
                    width: '48px',
                    maxWidth: '48px',
                },
            },
            {
                label: '年龄',
                key: 'patientAge',
                style: {
                    textAlign: 'center',
                    width: '54px',
                    maxWidth: '54px',
                },
            },
            // todo
            {
                label: '婚姻状况',
                key: 'marital',
                style: {
                    textAlign: 'center',
                    width: '88px',
                    maxWidth: '88px',
                },
                dataFormatter: (val) => {
                    return MaritalStatusLabel[val] || '';
                },
            },
            {
                label: '体检套餐',
                key: 'name',
                style: {
                    textAlign: 'left',
                    flex: 1,
                },
            },
            {
                label: '体检类型',
                key: 'businessType',
                style: {
                    textAlign: 'center',
                    width: '88px',
                    maxWidth: '88px',
                },
                dataFormatter: (val) => {
                    return PEBusinessTypeLabel[val] || '';
                },
            },
            {
                label: '下单时间',
                key: 'orderCreated',
                style: {
                    textAlign: 'left',
                    width: '162px',
                    maxWidth: '162px',
                },
                dataFormatter: (val) => {
                    return formatDate(val, 'YYYY-MM-DD HH:mm');
                },
            },
            {
                label: '订单金额',
                key: 'totalFee',
                style: {
                    textAlign: 'right',
                    width: '110px',
                    maxWidth: '110px',
                },
                colType: 'money',
            },
            {
                label: '销售人',
                key: 'salesEmployee',
                style: {
                    textAlign: 'left',
                    width: '88px',
                    maxWidth: '88px',
                },
                dataFormatter: (val) => {
                    return val?.name ?? '';
                },
            },
            {
                label: '结算状态',
                key: 'chargeStatusDisplayName',
                style: {
                    textAlign: 'center',
                    width: '88px',
                    maxWidth: '88px',
                },
            },
            {
                label: '预约时间',
                key: 'businessTime',
                style: {
                    textAlign: 'left',
                    width: '162px',
                    maxWidth: '162px',
                },
                dataFormatter: (val) => {
                    return formatDate(val, 'YYYY-MM-DD');
                },
            },
            {
                label: '订单状态',
                key: 'status',
                style: {
                    textAlign: 'center',
                    width: '88px',
                    maxWidth: '88px',
                },
                dataFormatter: (val) => {
                    return PE_ORDER_STATUS_OPTIONS.find((item) => item.value === val)?.label ?? '';
                },
            },
        ],
    },
);

export const ORDER_FORM_TYPE = Object.freeze(
    {
        PE_COMPOSE: 0, // 体检套餐
        ADDITIONS: 10, // 增项
        GROUP_PE_COMPOSE: 20, // 团检体检套餐
    },
);

export const OPERATE_WAY = Object.freeze({
    ADD: 0, // 新增
    EDIT: 1, // 编辑
});

export const GOODS_JSON_SUB_TYPE = Object.freeze(
    {
        NORMAL: 1, // 普通体检套餐
        PUBLIC_HEALTH: 2, // 公卫体检
        INDUSTRIAL_DISEASE: 3, // 职业病体检
        COLLEGE_STUDENT: 4, // 大学生体检
        DRIVING_LICENSE: 5, // 驾照体检
    },
);

export const GOODS_JSON_TYPE = Object.freeze({
    PHYSICAL_EXAMINATION: 27,// 体检的检查检验大类
    PHYSICAL_EXAMINATION_COMPOSE: 28, // 体检的套餐大类
});

export const CHECK_TYPE = {
    INSPECTION: 2, // 检查
    TESTING: 1, // 检验
};

export const DEVICE_TYPE_OPTIONS = Object.freeze([
    {
        label: '未知分类', value: 0,
    },
    {
        label: 'CT', value: 1,
    },
    {
        label: 'DR', value: 2,
    },
    {
        label: 'CR', value: 3,
    },
    {
        label: '透视', value: 4,
    },
    {
        label: '心电图', value: 5,
    },
    {
        label: '骨密度', value: 6,
    },
    {
        label: '试光类', value: 7,
    },
    {
        label: '彩超', value: 8,
    },
    {
        label: 'MR', value: 9,
    },
    {
        label: '内窥镜', value: 10,
    },
    {
        label: 'B超', value: 11,
    },
    {
        label: '脑电图', value: 12,
    },
    {
        label: '其他', value: 13,
    },
    {
        label: '一般检查', value: 14,
    },
    {
        label: '内科检查', value: 15,
    },
    {
        label: '外科检查', value: 16,
    },
    {
        label: '耳鼻喉检查', value: 17,
    },
    {
        label: '口腔检查', value: 18,
    },
]);

export const OPERATE_ITEM_TYPE = Object.freeze({
    NONE: '0',
    DEL: '1',
    OTHER: '2',
});


