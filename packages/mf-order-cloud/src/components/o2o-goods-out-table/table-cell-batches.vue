<template>
    <abc-table-cell
        class="o2o-goods-out-table-cell-batches"
        :clickable="!disabled && !requiredBatchInfos && !goodsIsDisabled"
        style="height: 100%; padding: 0;"
        @click.native.prevent="handleClick"
    >
        <abc-flex v-if="goodsIsDisabled" style="padding: 0 12px;">
            -
        </abc-flex>
        <abc-form-item v-else-if="batchInfos.length === 0" style="width: 100%; height: 100%;" :required="requiredBatchInfos">
            <abc-input
                readonly
                placeholder="选择批次"
                :input-custom-style="{ cursor: disabled ? '' : 'pointer' }"
                adaptive-width
            ></abc-input>
        </abc-form-item>
        <abc-flex
            v-else
            vertical
            style="width: 100%; height: 100%; padding: 0 12px; cursor: pointer;"
            align="flex-start"
            justify="center"
            gap="4"
        >
            <abc-flex
                v-for="it in batchInfos"
                :key="it.batchId"
                justify="space-between"
                style="width: 100%;"
                gap="4"
            >
                <abc-text class="ellipsis" :title="it.batchNo" :theme="disabled ? '' : 'primary-light'">
                    {{ it.batchNo }}
                </abc-text>
                <div class="count" style="margin-left: auto;">
                    <abc-text :theme="disabled ? '' : 'primary-light'">
                        ×{{ it.packageCount || it.pieceCount }}
                    </abc-text>
                </div>
            </abc-flex>
        </abc-flex>
    </abc-table-cell>
</template>

<script>
    export default {
        name: 'TableCellBatches',
        props: {
            item: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            requiredBatchInfos: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            goodsIsDisabled() {
                return !this.productInfo || this.productInfo?.v2DisableStatus > 10 || this.item.isCancelled;
            },
            productInfo() {
                return this.item.productInfo;
            },
            batchInfos() {
                return this.item.batchInfos || [];
            },
        },
        methods: {
            handleClick() {
                if (this.disabled || this.goodsIsDisabled) return;
                this.$emit('click', this.item);
            },
        },
    };
</script>
