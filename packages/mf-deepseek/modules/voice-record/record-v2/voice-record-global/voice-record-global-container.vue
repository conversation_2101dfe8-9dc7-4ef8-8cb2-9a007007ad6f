<template>
    <div class="voice-record-global-container">
        <voice-record-dialog
            v-if="dialogVisible"
            v-model="dialogVisible"
            :switch-setting="switchSetting"
            :outpatient-info="outpatientInfo"
            :patient-info="patientInfo"
            :attachment-id="currentAttachmentId"
            @re-record="handleReRecord"
        >
        </voice-record-dialog>

        <voice-record-tips-modal
            v-if="showTipsModal"
            v-model="showTipsModal"
            @close="handleTipsModalClose"
        ></voice-record-tips-modal>
    </div>
</template>

<script>
    import { useAsrTipsStore } from '../../hooks/use-asr-tips';
    import { storeToRefs } from 'MfBase/pinia';
    import VoiceRecordTipsModal from '../voice-record-tips-modal.vue';

    export default {
        name: 'VoiceRecordGlobalContainer',
        components: {
            VoiceRecordTipsModal,
            VoiceRecordDialog: () => import('../voice-record-dialog.vue'),
        },
        props: {
            switchSetting: {
                type: Object,
            },
            outpatientInfo: {
                type: Object,
                required: true,
            },
            patientInfo: {
                type: Object,
                required: true,
            },
            attachmentId: {
                type: String,
            },
        },
        setup() {
            // 使用 pinia store 获取所有响应式数据和方法
            const store = useAsrTipsStore();
            const {
                isReadTips,
            } = storeToRefs(store);

            const {
                markReadTips,
            } = store;

            return {
                isReadTips,
                markReadTips,
            };
        },
        data() {
            return {
                showTipsModal: false,
                dialogVisible: false,
                currentAttachmentId: this.attachmentId,
            };
        },
        computed: {
            outpatientSheetId() {
                return this.outpatientInfo?.id;
            },
        },
        watch: {
            dialogVisible(value) {
                if (!value) {
                    // 弹窗销毁，触发事件
                    this.$emit('destroy');
                }
            },
        },
        methods: {
            async showRecordDialog() {
                await this.checkReadTips();
                this.dialogVisible = true;
            },
            async checkReadTips() {
                return new Promise((resolve) => {
                    if (this.isReadTips) {
                        resolve();
                        return;
                    }
                    this.showTipsModal = true;
                    this.$once('close-tips', () => {
                        this.markReadTips();
                        resolve();
                    });
                });
            },
            handleTipsModalClose() {
                this.$emit('close-tips');
            },
            handleReRecord() {
                this.currentAttachmentId = '';
            },
        },
    };
</script>
