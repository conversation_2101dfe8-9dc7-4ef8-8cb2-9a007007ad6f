<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        custom-class="authorized-ec-dialog"
        append-to-body
        size="small"
        :show-footer="false"
    >
        <div class="authorized-ec-dialog__title">
            选择平台并登录网店
        </div>
        <div class="authorized-ec-dialog__content">
            <div
                v-for="item in ecTypes"
                :key="item.ecName"
                class="authorized-ec-item"
                @click="handleThirdAuthorize(item)"
            >
                <img
                    width="48"
                    height="48"
                    :src="item.ecLogo"
                    alt=""
                />
                <div class="name">
                    {{ item.ecName }}
                </div>
            </div>
        </div>
    </abc-modal>
</template>


<script>
    import { getTargetHost } from 'MfBase/build-env';
    import {
        ECTypeEnum,
    } from '@/utils/constants';
    import DialogEcBind from '../dialog-ec-bind/index';

    export default {
        props: {
            ecTypes: {
                type: Array,
                default: () => [],
            },
            shortId: {
                type: String,
                default: '',
            },
            authCode: {
                type: String,
                default: '',
            },
            /**
             * 绑定的门店
             */
            bindClinic: {
                type: Object,
                default: null,
            },
        },
        data() {
            return {
                loading: false,
                tableData: [],
                visible: false,
                thirdPartyAuthorizationUrl: '',
            };
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        methods: {
            async handleThirdAuthorize(item) {
                if (item.ecType === ECTypeEnum.PDD) {
                    await this.authPdd(item);
                } else if (item.ecType === ECTypeEnum.MT) {
                    await new DialogEcBind({
                        detail: item,
                        bindClinic: this.bindClinic,
                        closeDialog: () => {
                            this.visible = false;
                        },
                    }).generateDialogAsync({ parent: this });
                }
            },
            async authPdd(item) {
                let authWindow;

                const onMessage = (message) => {
                    if (message.origin.indexOf('global') !== -1) {
                        if (message.data === 'ec_auth_success') {
                            this.$abcEventBus.$emit('ec-auth-success');
                            this.visible = false;
                            window.removeEventListener('message', onMessage);
                            authWindow.close();
                        }
                    }
                };

                window.addEventListener('message', onMessage);

                const redirectUri = encodeURIComponent(`https://${getTargetHost()}/ec-oauth-callback`);
                const state = encodeURIComponent(`${this.shortId}@${location.hostname}`);
                const thirdUrl = 'https://fuwu.pinduoduo.com/service-market/auth';
                const mmsUrl = 'https://mms.pinduoduo.com';

                const url = `${thirdUrl}?response_type=code&client_id=${item.clientId}&redirect_uri=${redirectUri}&state=${state}`;
                if (window.remote?.session) {
                    try {
                        const mmsCookies = await window.remote.session.defaultSession.cookies.get({ url: mmsUrl });
                        for (const cookie of mmsCookies) {
                            await window.remote.session.defaultSession.cookies.remove(mmsUrl, cookie.name);
                        }
                    } catch (error) {
                        console.error(error);
                    }

                    try {
                        const cookies = await window.remote.session.defaultSession.cookies.get({ url: thirdUrl });
                        for (const cookie of cookies) {
                            await window.remote.session.defaultSession.cookies.remove(thirdUrl, cookie.name);
                            console.log('removed success');
                        }
                    } catch (error) {
                        console.error(error);
                    }
                }
                const currentWidth = window.innerWidth;
                const currentHeight = window.innerHeight;

                const newWidth = Math.round(currentWidth * 0.8);
                const newHeight = Math.round(currentHeight * 0.8);

                authWindow = window.open(url, '_blank', `width=${newWidth},height=${newHeight}`);
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
.authorized-ec-dialog {
    left: 40%;
    width: 300px;
    height: auto;

    &__title {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: center;
        justify-content: center;
        margin: 24px 0;

        .authorized-ec-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 280px;
            height: 180px;
            cursor: pointer;
            border: 1px solid #e0e5ee;
            border-radius: 6px;

            &:hover {
                background: var(--abc-color-cp-grey4);
            }

            .name {
                margin-top: 6px;
            }
        }
    }
}
</style>
