<template>
    <abc-table-cell
        class="o2o-goods-out-table-cell-goods"
        style="height: 100%;"
        :clickable="productInfo && !disabled"
        @click.native="handleBindGoods"
    >
        <abc-flex vertical gap="2">
            <template v-if="productInfo">
                <abc-popover
                    v-if="productInfo.v2DisableStatus > 10 && !disabled"
                    theme="yellow"
                    placement="top"
                    trigger="hover"
                    style="width: 100%;"
                >
                    <abc-flex slot="reference" align="center" gap="8">
                        <abc-text class="ellipsis" theme="warning-light">
                            {{ productInfo.displayName }}
                        </abc-text>
                        <abc-link @click.stop="handleBindGoods">
                            重新绑定
                        </abc-link>
                    </abc-flex>
                    <div>
                        商品已停用，请启用或更换绑定商品
                    </div>
                </abc-popover>
                <abc-flex v-else align="center" :title="productInfo.displayName">
                    <abc-text class="ellipsis">
                        {{ productInfo.displayName }}
                    </abc-text>
                </abc-flex>

                <abc-flex align="center" gap="8">
                    <abc-text :theme="item.isCancelled ? 'gray-light' : 'gray'" size="mini">
                        {{ productInfo.displaySpec }}
                    </abc-text>
                    <abc-text :theme="item.isCancelled ? 'gray-light' : 'gray'" size="mini">
                        {{ productInfo.manufacturer }}
                    </abc-text>
                    <abc-text :theme="item.isCancelled ? 'gray-light' : 'gray'" size="mini">
                        {{ productInfo.shortId }}
                    </abc-text>
                </abc-flex>
            </template>
            <template v-else>
                <abc-flex align="center" gap="8">
                    <abc-text theme="warning-light" class="ellipsis">
                        未绑定ABC商品
                    </abc-text>
                    <abc-link v-if="!disabled" @click.stop="handleBindGoods">
                        绑定
                    </abc-link>
                </abc-flex>
            </template>
        </abc-flex>
    </abc-table-cell>
</template>

<script>
    import DialogBindGoodsO2O from '@/components/dialog-bind-goods-o2o';
    export default {
        name: 'TableCellGoods',
        props: {
            item: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            mallName: {
                type: String,
                default: '',
            },
        },
        computed: {
            ecGoodsSku() {
                return this.item.ecGoodsSku || {};
            },
            productInfo() {
                return this.item.productInfo;
            },
        },
        methods: {
            handleBindGoods() {
                if (this.disabled) return;
                new DialogBindGoodsO2O({
                    clinicId: this.item.clinicId,
                    ecMallId: this.ecGoodsSku.mallId,
                    mallName: this.mallName,
                    goodsId: this.item.ecGoodsId,
                    imageUrl: this.item.extGoodsImg,
                    goodsName: this.item.extGoodsName || '-',
                    status: this.ecGoodsSku.status,
                    goodsSkuId: this.item.ecGoodsSkuId,
                    desc: this.ecGoodsSku.externalSkuId || '-',
                    price: this.ecGoodsSku.price,

                    hisGoodsList: this.ecGoodsSku.relHisGoodsList || [],
                    onOpen: () => {
                        this.$emit('open-bind-goods');
                    },
                    onClose: () => {
                        this.$emit('close-bind-goods');
                    },
                }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>



