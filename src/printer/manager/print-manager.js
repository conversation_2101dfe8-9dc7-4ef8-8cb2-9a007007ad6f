import ABCClientManager from './abc-client-manager.js';
import LODOPClientManager from '@/printer/manager/lodop-client-manager.js';
import { getPrintJson } from '@/printer/print-init/print-asstes.js';
import { loadScript } from 'utils/dom.js';
import {
    isPrintDev,
} from '@/assets/configure/build-env.js';
import { PrintMode } from '@/printer/constants.js';
import Clone from 'utils/clone';
import {
    isABCClient, isBrowser,
} from '@/utils';
import LocalStore from 'utils/localStorage-handler';

export default class PrintManager {
    static instance = null;

    /**
     * @return {PrintManager}
     */
    static getInstance() {
        if (!PrintManager.instance) {
            PrintManager.instance = new PrintManager();
        }
        return PrintManager.instance;
    }

    constructor() {
        this.initFeature();
        this.init();
    }

    feature = {
        isEnablePrintV2: false,
        isBrowser: false,
        isABCClient: false,
        isLoadABCPrintPackage: false,
        isLoadDevelopPrintPackage: !process.env.PRINT_DEV,
        isOffsetFixedLeftTopKeyList: ['fee-bill'],
        isNewVersionElectronPrint: true,
        printConfig: {
            prescriptionVersion: 0,
        },
        electronPrintTaskSplit: false,
    };

    abcClientManager = null;
    lodopClientManager = null;

    initFeature() {
        this.setFeature('isABCClient', isABCClient());
        this.setFeature('isBrowser', isBrowser());
        if (isABCClient()) {
            this.setFeature('isNewVersionElectronPrint', typeof window.electron?.printer?.print === 'function');
        }
        this.feature.electronPrintTaskSplit = LocalStore.getBoolean('electronPrintTaskSplit', false);
    }

    setFeature(key, value) {
        this.feature[key] = value;
    }

    getFeature(key) {
        return this.feature[key];
    }

    updateFeature(key, value) {
        if (key === 'isEnablePrintV2') {
            if (!window.AbcPackages?.AbcPrint) {
                window.addEventListener('load', () => {
                    window.AbcPackages?.AbcPrint.setEnablePrintV2(value);
                });
            } else {
                window.AbcPackages?.AbcPrint.setEnablePrintV2(value);
            }
        }
        this.setFeature(key, value);
        this.init();
    }

    updateObjectFeature(key, value) {
        if (value) {
            Object.assign(this.feature[key], value);
        }
    }

    setElectronPrintTaskSplitFeature(value) {
        this.feature.electronPrintTaskSplit = !!value;
        LocalStore.setBoolean('electronPrintTaskSplit', value);
    }

    isNewPrescriptionVersion() {
        return !!this.feature.printConfig.prescriptionVersion;
    }

    isEnableElectronPrint() {
        return this.getFeature('isEnablePrintV2') || this.isNewPrescriptionVersion();
    }

    isNewPharmacyCashierVersion() {
        return !!this.feature.printConfig.pharmacyCashierVersion;
    }

    loadABCPrintPackage() {
        if (this.getFeature('isLoadABCPrintPackage')) {
            return;
        }
        const isLoadDevelopPrintPackage = (isPrintDev && this.getFeature('isLoadDevelopPrintPackage'));
        if (isLoadDevelopPrintPackage) {
            console.log('%c 请开发打印的同学运行yarn dev:print', 'background: red; padding: 4px;font-size: 18px;color: white; font-weight: bold;');
        }
        const jsSrcList = isLoadDevelopPrintPackage ? [
            {
                'js': '//static-dev-cdn.abczs.cn/abc-print/loader.js',
            },
        ] : getPrintJson();
        const timestamp = new Date().getTime();
        jsSrcList?.forEach((jsSrc) => {
            loadScript(`${jsSrc.js}?t=${timestamp}`, '');
        });
        this.setFeature('isLoadABCPrintPackage', true);
    }

    init() {
        // 避免内部调用时printManager未初始化
        this._timer = setTimeout(() => {
            this.abcClientManager = ABCClientManager.getInstance();
            this.lodopClientManager = LODOPClientManager.getInstance();
            this.loadABCPrintPackage();
            delete this._timer;
        });
    }

    /**
     * 获取打印机列表
     * @param {1 | 2} [mode] 指定打印模式 PrintMode.Lodop:Lodop打印 PrintMode.Electron:客户端打印
     * @return {*[]}
     */
    getPrinterList(mode) {
        const lodopPrinterList = this.lodopClientManager.getPrinterList();
        const clientPrinterList = this.abcClientManager.getPrinterList();

        if (mode === PrintMode.Lodop) {
            // 指定 Lodop 打印时，优先从 Lodop 获取打印机列表，获取失败兜底从客户端获取
            if (lodopPrinterList && lodopPrinterList.length) return lodopPrinterList;
            if (clientPrinterList && clientPrinterList.length) return clientPrinterList;
            return [];
        }
        if (mode === PrintMode.Electron || this.isEnableElectronPrint()) {
            // 指定客户端打印或者开启了新版打印时，优先从客户端获取打印机列表，获取失败兜底从 Lodop 获取
            if (clientPrinterList && clientPrinterList.length) return clientPrinterList;
            if (lodopPrinterList && lodopPrinterList.length) return lodopPrinterList;
            return [];
        }

        if (lodopPrinterList && lodopPrinterList.length) return lodopPrinterList;
        if (clientPrinterList && clientPrinterList.length) return clientPrinterList;
        return [];
    }

    /**
     * 检查打印机名称和 index 是否匹配
     * @param {number} deviceIndex 打印机索引
     * @param {string} deviceName 打印机名称
     * @param {1 | 2} mode 打印模式 1:Lodop打印 2:客户端打印
     * @return {boolean} 是否匹配
     */
    checkDeviceIndexByDeviceName(deviceIndex, deviceName, mode) {
        return this.getPrinterList(mode).some((printer) => {
            return printer.deviceIndex === deviceIndex && printer.deviceName === deviceName;
        });
    }

    getOffsetByDeviceName(deviceName) {
        return this.abcClientManager.getOffsetByDeviceName(deviceName);
    }

    mergedPagesByDeviceName(deviceName, customPages) {
        customPages = Clone(customPages);
        const noAbcPrintCustomPageList = this.abcClientManager.getPageListByDeviceName(deviceName)
            .filter((electronPage) => !customPages.some((customPage) => electronPage.paper.name === customPage.paper.name));
        customPages.sort((a) => (a.isRecommend ? -1 : 1));
        return customPages.concat(noAbcPrintCustomPageList);
    }

    formatPagesByTemplate(customPages) {
        customPages = Clone(customPages);
        customPages.sort((a) => (a.isRecommend ? -1 : 1));
        return customPages;
    }

    isElectronPageSize(deviceName, paperName) {
        return this.abcClientManager.isElectronPageSize(deviceName, paperName);
    }

    supportTinyPrinter(i) {
        if (this.isEnableElectronPrint()) {
            return this.abcClientManager.supportTinyPrinter(i);
        }
        return this.lodopClientManager.supportTinyPrinter(i);
    }

    async checkPrintInitStatus() {
        // 检查是否为新版打印
        if (this.isEnableElectronPrint()) {
            // 强制升级模式下浏览器中使用桥接打印
            if (this.getFeature('isBrowser')) {
                // 桥接为可用状态
                // 优先使用桥接打印
                if (this.abcClientManager.getFeature('isABCClientPrintServerOnline')) {
                    this.abcClientManager.setFeature('isBridgePrintEnable', true);
                    return true;
                }
                this.abcClientManager.setFeature('isBridgePrintEnable', false);
                await this.abcClientManager.tryLaunchABCClientPrintServerAndShowInstallDialog();
                return false;
            }
            // 客户端中使用普通打印
            this.abcClientManager.setFeature('isBridgePrintEnable', false);
            return true;
        }
        // 再次检查LODOP是否加载成功
        this.lodopClientManager.checkIsLODOPReady();
        if (this.lodopClientManager.getFeature('isLodopReady')) {
            return true;
        }
        await this.lodopClientManager.tryLaunchLodopAndShowInstallDialog();
        return false;
    }

    // 调试
    debug(printConfig, template, mode, extra) {
        if (mode === PrintMode.Lodop) {
            this.lodopClientManager.debug(printConfig, template, extra);
        } else if (mode === PrintMode.Electron || this.isEnableElectronPrint()) {
            this.abcClientManager.debug(printConfig, template, extra);
        } else {
            this.lodopClientManager.debug(printConfig, template, extra);
        }
    }

    // 获取打印机物理偏移
    getPrinterOffset(deviceName) {
        return this.abcClientManager.getPrinterOffset(deviceName);
    }

    getPrinterInfoByDeviceName(deviceName) {
        return this.abcClientManager.getPrinterInfoByDeviceName(deviceName);
    }
}
