<template>
    <abc-table-cell @click.native="handleAllocation">
        <abc-flex
            v-if="item"
            justify="space-between"
            align="center"
            style="width: 100%;"
        >
            <abc-flex v-if="hasSetStock" vertical align="flex-start">
                <abc-text v-if="item.hisGoodsInfo">
                    {{ displayStockStr }}
                </abc-text>
                <abc-text theme="gray">
                    <template v-if="item.assignStockType === AllocationStockType.CUSTOM">
                        自定义库存
                    </template>
                    <template v-else-if="item.assignStockType === AllocationStockType.RATIO">
                        比例：{{ item.assignStockRatio || 0 }}%
                    </template>
                    <template v-else-if="item.assignStockType === AllocationStockType.SHARE">
                        共享
                    </template>
                </abc-text>
            </abc-flex>
            <abc-text v-else theme="gray-light">
                分配库存
            </abc-text>
            <abc-button
                v-if="!disabledOperation"
                icon="s-edit-line"
                variant="text"
                theme="default"
            ></abc-button>
        </abc-flex>
    </abc-table-cell>
</template>

<script>
    import DialogAllocationStock from '@/components/dialog-allocation-stock';
    import { AllocationStockType } from '@/utils/constants';

    export default {
        name: 'TableCellAllocationStock',
        props: {
            item: {
                type: Object,
                required: true,
            },
            clinicId: {
                type: String,
                required: true,
            },
            ecType: {
                type: Number,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                AllocationStockType,
            };
        },
        computed: {
            disabledOperation() {
                return !this.item.hisGoodsId || this.disabled;
            },
            hasSetStock() {
                return this.item.hisGoodsId && (this.item?.assignedStockPackageCount !== null || this.item?.assignedStockPieceCount !== null);
            },
            displayStockStr() {
                const {
                    assignedStockPackageCount,
                    assignedStockPieceCount,
                    hisGoodsInfo,
                } = this.item;
                const {
                    packageUnit = '',
                    pieceUnit = '',
                } = hisGoodsInfo || {};
                const _arr = [];
                if (packageUnit) {
                    _arr.push(`${assignedStockPackageCount || 0}${packageUnit}`);
                }
                if (assignedStockPieceCount || !packageUnit) {
                    _arr.push(`${assignedStockPieceCount}${pieceUnit}`);
                }
                return _arr.join('');
            },
        },
        methods: {
            handleAllocation() {
                if (this.disabledOperation) return;
                new DialogAllocationStock({
                    ecType: this.ecType,
                    clinicId: this.clinicId,
                    hisGoodsId: this.item.hisGoodsId,
                    defaultList: this.item.id ? [] : [this.item],
                    currentItem: this.item,
                    onChange: (data) => {
                        if (data) {
                            Object.assign(this.item, data);
                        }
                        this.$emit('change', data);
                    },
                }).generateDialogAsync();
            },
        },
    };
</script>

