export default class Printer {
    constructor(props) {
        this.props = props;
        this.init(props);
    }

    /**
     * @desc 初始化
     * <AUTHOR>
     * @date 2021-07-19 16:18:10
     */
    init(props) {
        console.log('初始化', props);
    }

    /**
     * @desc 静默打印（适用于收费单 等）
     * <AUTHOR>
     * @date 2021-07-19 16:16:17
     */
    silentPrint() {
        console.log('静默打印');
    }
}
