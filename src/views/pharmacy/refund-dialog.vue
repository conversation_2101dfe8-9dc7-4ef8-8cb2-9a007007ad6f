<template>
    <!--退药弹出框-->
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="_canRefund ? '选择退药项目' : '没有可退药项目'"
        size="hugely"
        data-cy="pharmacy-form-refund-dialog"
        content-styles="min-height: 608px; max-height: 804px;"
    >
        <div class="refund-dialog-content clearfix">
            <abc-form v-if="_canRefund" ref="refundForm">
                <!--商品-->
                <div v-for="goods in goodsList" :key="goods.id" class="abc-table-list">
                    <div class="table-title">
                        <ul>
                            <abc-checkbox
                                v-model="goods.checkedAll"
                                :disabled="isDisabled"
                                style="margin-right: 8px;"
                                @change="fullCheckChange(goods)"
                            ></abc-checkbox>
                            <li class="title">
                                商品
                            </li>
                            <li class="batches">
                                退药批次
                            </li>
                            <li class="item">
                                退药数量
                            </li>
                        </ul>
                    </div>
                    <div class="table-content">
                        <ul>
                            <label v-for="m in goods.dispensingFormItems" :key="m.id">
                                <li>
                                    <div class="ellipsis">
                                        <abc-checkbox
                                            v-model="m.checked"
                                            :checked="m.checked"
                                            :label="m"
                                            :disabled="isDisabled"
                                            style="margin-right: 8px;"
                                            @change="changeCheck(goods)"
                                        ></abc-checkbox>
                                        <span :title="m.name" class="ellipsis">{{ m.name }}</span>
                                    </div>
                                    <div class="specification ellipsis" :title="m.productInfo | getSpec">
                                        {{ m.productInfo | getSpec }}
                                    </div>
                                    <div class="batches" @click.prevent="onSelectBatch(m)">
                                        <template v-if="showBatches(m)">
                                            <template v-if="m.dispensingFormItemBatches.length <= 2">
                                                <p
                                                    v-for="it in m.dispensingFormItemBatches"
                                                    :key="it.batchId"
                                                    style="color: var(--abc-color-theme1); cursor: pointer;"
                                                >
                                                    {{ it.batchNo || '-' }}
                                                </p>
                                            </template>
                                            <template v-else>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    {{ m.dispensingFormItemBatches[0].batchNo || '-' }}
                                                </p>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    等{{ m.dispensingFormItemBatches.length }}个批次
                                                </p>
                                            </template>
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </div>
                                    <div class="item">
                                        <abc-form-item
                                            v-if="m.checked"
                                            required
                                            :validate-event="validate(m)"
                                            :error-style="errorStyle"
                                        >
                                            <abc-input
                                                v-abc-focus-selected
                                                :value="m._unitCount"
                                                class="count-center"
                                                :width="50"
                                                :config="countConfig(m)"
                                                :disabled="isDisabled"
                                                type="number"
                                                size="small"
                                                @change="(val)=>changeCount(m,val)"
                                            >
                                                <span slot="append">{{ m.unit }}</span>
                                            </abc-input>
                                        </abc-form-item>

                                        <template v-else> {{ m.canRefundUnitCount }}{{ m.unit }} </template>
                                    </div>
                                </li>
                            </label>
                        </ul>
                    </div>
                </div>

                <!--材料-->
                <div v-for="item in materialList" :key="item.id" class="abc-table-list">
                    <div class="table-title">
                        <ul>
                            <abc-checkbox
                                v-model="item.checkedAll"
                                :disabled="isDisabled"
                                style="margin-right: 8px;"
                                @change="fullCheckChange(item)"
                            ></abc-checkbox>
                            <li class="title">
                                材料
                            </li>
                            <li class="batches">
                                退药批次
                            </li>
                            <li class="item">
                                退药数量
                            </li>
                            <li v-if="traceCodeCollectionCheck" class="trace-code">
                                追溯码
                            </li>
                        </ul>
                    </div>
                    <div class="table-content">
                        <ul>
                            <template v-for="m in item.dispensingFormItems">
                                <li :key="m.id">
                                    <label style="display: inline-flex; flex: 1; gap: 8px;">
                                        <div class="ellipsis">
                                            <abc-checkbox
                                                v-model="m.checked"
                                                :checked="m.checked"
                                                :label="m"
                                                :disabled="isDisabled"
                                                style="margin-right: 8px;"
                                                @change="handleChangeItemCheck(m, item)"
                                            ></abc-checkbox>
                                            <span :title="m.name" class="ellipsis">{{ m.name }}</span>
                                        </div>
                                        <div class="specification ellipsis" :title="m.productInfo | getSpec">
                                            {{ m.productInfo | getSpec }}
                                        </div>
                                    </label>
                                    <div class="batches" @click.prevent="onSelectBatch(m)">
                                        <template v-if="!m.checked && traceCodeCollectionCheck"></template>
                                        <template v-else-if="showBatches(m)">
                                            <template v-if="m.dispensingFormItemBatches.length <= 2">
                                                <p
                                                    v-for="it in m.dispensingFormItemBatches"
                                                    :key="it.batchId"
                                                    style="color: var(--abc-color-theme1); cursor: pointer;"
                                                >
                                                    {{ it.batchNo || '-' }}
                                                </p>
                                            </template>
                                            <template v-else>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    {{ m.dispensingFormItemBatches[0].batchNo || '-' }}
                                                </p>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    等{{ m.dispensingFormItemBatches.length }}个批次
                                                </p>
                                            </template>
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </div>
                                    <div class="item">
                                        <abc-form-item
                                            v-if="m.checked"
                                            required
                                            :validate-event="validate(m)"
                                            :error-style="errorStyle"
                                        >
                                            <abc-input
                                                v-abc-focus-selected
                                                :value="m._unitCount"
                                                class="count-center"
                                                :width="50"
                                                :config="countConfig(m)"
                                                :disabled="isDisabled"
                                                type="number"
                                                size="small"
                                                @change="(val)=>changeCount(m,val)"
                                            >
                                                <span slot="append">{{ m.unit }}</span>
                                            </abc-input>
                                        </abc-form-item>
                                    </div>
                                    <div v-if="traceCodeCollectionCheck" class="trace-code" @click.prevent.stop="">
                                        <template v-if="isCompatibleHistoryData(m) || isNoTraceCodeGoods(m)">
                                            <template v-if="!m.checked && isNoTraceCodeGoods(m)"></template>
                                            <abc-select
                                                v-else-if="canSetTraceCode(m)"
                                                v-model="m.selectedTraceCodeList"
                                                multiple
                                                correct-multiple-clear-value
                                                multi-label-mode="text"
                                                :width="240"
                                                :inner-width="250"
                                                with-search
                                                clearable
                                                :max-tag="1"
                                                size="small"
                                                :disabled="m.disabledTraceCodeList"
                                                disabled-input-enter
                                                :fetch-suggestions="(key) => handleSearch(key, m)"
                                            >
                                                <abc-option
                                                    v-for="(option, key) in m._traceableCodeListOptions"
                                                    :key="key"
                                                    :value="option.keyId"
                                                    :label="option.no"
                                                >
                                                    {{ option.no }}
                                                    <abc-text style="margin-left: 8px;" theme="gray">
                                                        {{ option.unit ? `${option.count}${option.unit}` : `x${option.count}` }}
                                                    </abc-text>
                                                </abc-option>
                                            </abc-select>
                                            <abc-text v-else theme="gray" style="padding-left: 8px;">
                                                {{ isNoTraceCodeGoods(m) ? '无码商品' : '发药时未采集追溯码' }}
                                            </abc-text>
                                        </template>
                                        <template v-else>
                                            <refund-trace-code-operation ref="traceCodeOperation" :code="m" @submit="(list)=>handleRefundTraceCodeOperationSubmit(m,list)"></refund-trace-code-operation>
                                        </template>
                                    </div>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>

                <!--西药处方-->
                <div v-for="(western, index) in westernList" :key="western.id" class="abc-table-list">
                    <div class="table-title">
                        <ul>
                            <abc-checkbox
                                v-model="western.checkedAll"
                                :disabled="isDisabled"
                                style="margin-right: 8px;"
                                @change="fullCheckChange(western)"
                            ></abc-checkbox>
                            <li class="title">
                                西药处方{{ translateH(westernList.length, index) }}
                            </li>
                            <li class="specification">
                                规格
                            </li>
                            <li class="batches">
                                退药批次
                            </li>
                            <li class="item">
                                退药数量
                            </li>
                            <li v-if="traceCodeCollectionCheck" class="trace-code">
                                追溯码
                            </li>
                        </ul>
                    </div>
                    <div class="table-content">
                        <ul>
                            <template v-for="m in western.dispensingFormItems">
                                <li v-if="showCol(m)" :key="m.id" :data-cy="`refund-dispense-item-goods-${m.name}`">
                                    <label style="display: inline-flex; flex: 1; gap: 8px;">
                                        <div class="ellipsis">
                                            <abc-checkbox
                                                v-model="m.checked"
                                                :checked="m.checked"
                                                :label="m"
                                                :disabled="isDisabled"
                                                style="margin-right: 8px;"
                                                data-cy="refund-dispense-item-checkbox"
                                                @change="handleChangeItemCheck(m, western)"
                                            ></abc-checkbox>
                                            <span :title="m.name" class="ellipsis">{{ m.name }}</span>
                                        </div>

                                        <div class="specification ellipsis" :title="m.productInfo | getSpec">
                                            {{ m.productInfo | getSpec }}
                                        </div>
                                    </label>

                                    <div class="batches" @click.prevent="onSelectBatch(m)">
                                        <template v-if="!m.checked && traceCodeCollectionCheck"></template>
                                        <template v-else-if="showBatches(m)">
                                            <template v-if="m.dispensingFormItemBatches.length <= 2">
                                                <p
                                                    v-for="it in m.dispensingFormItemBatches"
                                                    :key="it.batchId"
                                                    style="color: var(--abc-color-theme1); cursor: pointer;"
                                                >
                                                    {{ it.batchNo || '-' }}
                                                </p>
                                            </template>
                                            <template v-else>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    {{ m.dispensingFormItemBatches[0].batchNo }}
                                                </p>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    等{{ m.dispensingFormItemBatches.length }}个批次
                                                </p>
                                            </template>
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </div>

                                    <div class="item">
                                        <abc-form-item
                                            v-if="m.checked"
                                            required
                                            :validate-event="validate(m)"
                                            :error-style="errorStyle"
                                        >
                                            <abc-input
                                                v-abc-focus-selected
                                                :value="m._unitCount"
                                                class="count-center"
                                                :width="50"
                                                :config="countConfig(m)"
                                                :disabled="isDisabled"
                                                type="number"
                                                size="small"
                                                data-cy="refund-dispense-item-count"
                                                @change="(val)=>changeCount(m,val)"
                                            >
                                                <span slot="append">{{ m.unit }}</span>
                                            </abc-input>
                                        </abc-form-item>
                                    </div>
                                    <div v-if="traceCodeCollectionCheck" class="trace-code" @click.prevent.stop="">
                                        <template v-if="isCompatibleHistoryData(m) || isNoTraceCodeGoods(m)">
                                            <template v-if="!m.checked && isNoTraceCodeGoods(m)"></template>
                                            <abc-select
                                                v-else-if="canSetTraceCode(m)"
                                                v-model="m.selectedTraceCodeList"
                                                multiple
                                                correct-multiple-clear-value
                                                multi-label-mode="text"
                                                :width="240"
                                                :inner-width="250"
                                                with-search
                                                clearable
                                                :max-tag="1"
                                                size="small"
                                                :disabled="m.disabledTraceCodeList"
                                                disabled-input-enter
                                                :fetch-suggestions="(key) => handleSearch(key, m)"
                                            >
                                                <abc-option
                                                    v-for="(option, key) in m._traceableCodeListOptions"
                                                    :key="key"
                                                    :value="option.keyId"
                                                    :label="option.no"
                                                >
                                                    {{ option.no }}
                                                    <abc-text style="margin-left: 8px;" theme="gray">
                                                        {{ option.unit ? `${option.count}${option.unit}` : `x${option.count}` }}
                                                    </abc-text>
                                                </abc-option>
                                            </abc-select>
                                            <abc-text v-else theme="gray" style="padding-left: 8px;">
                                                {{ isNoTraceCodeGoods(m) ? '无码商品' : '发药时未采集追溯码' }}
                                            </abc-text>
                                        </template>
                                        <template v-else>
                                            <refund-trace-code-operation ref="traceCodeOperation" :code="m" @submit="(list)=>handleRefundTraceCodeOperationSubmit(m,list)"></refund-trace-code-operation>
                                        </template>
                                    </div>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>

                <!--外治处方-->
                <div v-for="(extern, index) in externList" :key="extern.id" class="abc-table-list">
                    <div class="table-title">
                        <ul>
                            <abc-checkbox
                                v-model="extern.checkedAll"
                                :disabled="isDisabled"
                                style="margin-right: 8px;"
                                @change="fullCheckChange(extern)"
                            ></abc-checkbox>
                            <li class="title">
                                外治处方{{ translateH(externList.length, index) }}
                            </li>
                            <li class="specification">
                                规格
                            </li>
                            <li class="batches">
                                退药批次
                            </li>
                            <li class="item">
                                退药数量
                            </li>
                        </ul>
                    </div>
                    <div class="table-content">
                        <ul>
                            <label v-for="m in extern.dispensingFormItems" :key="m.id">
                                <li v-if="showCol(m)">
                                    <div class="ellipsis">
                                        <abc-checkbox
                                            v-model="m.checked"
                                            :checked="m.checked"
                                            :label="m"
                                            :disabled="isDisabled"
                                            style="margin-right: 8px;"
                                            @change="changeCheck(extern)"
                                        ></abc-checkbox>
                                        <span :title="m.name" class="ellipsis">{{ m.name }}</span>
                                    </div>

                                    <div class="specification ellipsis" :title="m.productInfo | getSpec">
                                        {{ m.productInfo | getSpec }}
                                    </div>
                                    <div class="batches" @click.prevent="onSelectBatch(m)">
                                        <template v-if="showBatches(m)">
                                            <template v-if="m.dispensingFormItemBatches.length <= 2">
                                                <p
                                                    v-for="it in m.dispensingFormItemBatches"
                                                    :key="it.batchId"
                                                    style="color: var(--abc-color-theme1); cursor: pointer;"
                                                >
                                                    {{ it.batchNo || '-' }}
                                                </p>
                                            </template>
                                            <template v-else>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    {{ m.dispensingFormItemBatches[0].batchNo || '-' }}
                                                </p>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    等{{ m.dispensingFormItemBatches.length }}个批次
                                                </p>
                                            </template>
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </div>
                                    <div class="item">
                                        <abc-form-item
                                            v-if="m.checked"
                                            required
                                            :validate-event="validate(m)"
                                            :error-style="errorStyle"
                                        >
                                            <abc-input
                                                v-abc-focus-selected
                                                :value="m._unitCount"
                                                class="count-center"
                                                :width="50"
                                                :config="countConfig(m)"
                                                :disabled="isDisabled"
                                                type="number"
                                                size="small"
                                                @change="(val)=>changeCount(m,val)"
                                            >
                                                <span slot="append">{{ m.unit }}</span>
                                            </abc-input>
                                        </abc-form-item>

                                        <template v-else> {{ m.canRefundUnitCount }}{{ m.unit }} </template>
                                    </div>
                                </li>
                            </label>
                        </ul>
                    </div>
                </div>

                <!--输液处方-->
                <div v-for="(infusion, index) in infusionList" :key="infusion.id" class="abc-table-list">
                    <div class="table-title">
                        <ul>
                            <abc-checkbox
                                v-model="infusion.checkedAll"
                                :disabled="isDisabled"
                                style="margin-right: 8px;"
                                @change="fullCheckChange(infusion)"
                            ></abc-checkbox>
                            <li class="title">
                                输液处方{{ translateH(infusionList.length, index) }}
                            </li>
                            <li class="specification">
                                规格
                            </li>
                            <li class="batches">
                                退药批次
                            </li>
                            <li class="item">
                                退药数量
                            </li>
                            <li v-if="traceCodeCollectionCheck" class="trace-code">
                                追溯码
                            </li>
                        </ul>
                    </div>
                    <div class="table-content">
                        <ul>
                            <template v-for="m in infusion.dispensingFormItems">
                                <li v-if="showCol(m)" :key="m.id">
                                    <label style="display: inline-flex; flex: 1; gap: 8px;">
                                        <div class="ellipsis">
                                            <abc-checkbox
                                                v-model="m.checked"
                                                :checked="m.checked"
                                                :label="m"
                                                :disabled="isDisabled"
                                                style="margin-right: 8px;"
                                                @change="handleChangeItemCheck(m, infusion)"
                                            ></abc-checkbox>
                                            <span :title="m.name" class="ellipsis">{{ m.name }}</span>
                                        </div>
                                        <div class="specification ellipsis" :title="m.productInfo | getSpec">
                                            {{ m.productInfo | getSpec }}
                                        </div>
                                    </label>
                                    <div class="batches" @click.prevent="onSelectBatch(m)">
                                        <template v-if="!m.checked && traceCodeCollectionCheck"></template>
                                        <template v-else-if="showBatches(m)">
                                            <template v-if="m.dispensingFormItemBatches.length <= 2">
                                                <p
                                                    v-for="it in m.dispensingFormItemBatches"
                                                    :key="it.batchId"
                                                    style="color: var(--abc-color-theme1); cursor: pointer;"
                                                >
                                                    {{ it.batchNo || '-' }}
                                                </p>
                                            </template>
                                            <template v-else>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    {{ m.dispensingFormItemBatches[0].batchNo || '-' }}
                                                </p>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    等{{ m.dispensingFormItemBatches.length }}个批次
                                                </p>
                                            </template>
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </div>
                                    <div class="item">
                                        <abc-form-item
                                            v-if="m.checked"
                                            required
                                            :validate-event="validate(m)"
                                            :error-style="errorStyle"
                                        >
                                            <abc-input
                                                v-abc-focus-selected
                                                :value="m._unitCount"
                                                class="count-center"
                                                :width="50"
                                                :config="countConfig(m)"
                                                :disabled="isDisabled"
                                                type="number"
                                                size="small"
                                                @change="(val)=>changeCount(m,val)"
                                            >
                                                <span slot="append">{{ m.unit }}</span>
                                            </abc-input>
                                        </abc-form-item>
                                    </div>
                                    <div v-if="traceCodeCollectionCheck" class="trace-code" @click.prevent.stop="">
                                        <template v-if="isCompatibleHistoryData(m) || isNoTraceCodeGoods(m)">
                                            <template v-if="!m.checked && isNoTraceCodeGoods(m)"></template>
                                            <abc-select
                                                v-else-if="canSetTraceCode(m)"
                                                v-model="m.selectedTraceCodeList"
                                                multiple
                                                correct-multiple-clear-value
                                                :width="240"
                                                :inner-width="250"
                                                multi-label-mode="text"
                                                with-search
                                                clearable
                                                :max-tag="1"
                                                size="small"
                                                :disabled="m.disabledTraceCodeList"
                                                disabled-input-enter
                                                :fetch-suggestions="(key) => handleSearch(key, m)"
                                            >
                                                <abc-option
                                                    v-for="(option, key) in m._traceableCodeListOptions"
                                                    :key="key"
                                                    :value="option.keyId"
                                                    :label="option.no"
                                                >
                                                    {{ option.no }}
                                                    <abc-text style="margin-left: 8px;" theme="gray">
                                                        {{ option.unit ? `${option.count}${option.unit}` : `x${option.count}` }}
                                                    </abc-text>
                                                </abc-option>
                                            </abc-select>
                                            <abc-text v-else theme="gray" style="padding-left: 8px;">
                                                {{ isNoTraceCodeGoods(m) ? '无码商品' : '发药时未采集追溯码' }}
                                            </abc-text>
                                        </template>
                                        <template v-else>
                                            <refund-trace-code-operation ref="traceCodeOperation" :code="m" @submit="(list)=>handleRefundTraceCodeOperationSubmit(m,list)"></refund-trace-code-operation>
                                        </template>
                                    </div>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>

                <!--中药-->
                <div v-for="(chinese, index) in chineseList" :key="chinese.id" class="refund-dialog-list">
                    <div class="abc-table-list">
                        <div class="table-title">
                            <ul>
                                <abc-checkbox
                                    v-model="chinese.checkedAll"
                                    :disabled="isDisabled"
                                    style="margin-right: 8px;"
                                    @change="fullCheckChange(chinese, true, index)"
                                ></abc-checkbox>
                                <li class="title">
                                    中药{{ translateH(chineseList.length, index) }}

                                    <ul v-if="chinese.checkedAll && chinese.formSupportUnDispenseByDose" class="abc-table-dosage">
                                        <li>
                                            共{{ chineseDoseCount(index) }}剂，退
                                        </li>
                                        <li class="table-doseCount">
                                            <abc-input-number
                                                ref="chinesedosagecount"
                                                v-model="chinese.dispensingFormItems[0].doseCount"
                                                v-abc-focus-selected
                                                fixed-button
                                                button-placement="left"
                                                size="tiny"
                                                :width="38"
                                                :disabled="isDisabled || detailProvide.isVirtualPharmacy"
                                                :disabled-add-btn="chinese.dispensingFormItems[0].doseCount >= chineseDoseCount(index)"
                                                class="dose-cose-wrapper"
                                                :class="{
                                                    addDisabled: chinese.dispensingFormItems[0].doseCount >= chineseDoseCount(index), desDisabled: chinese.dispensingFormItems[0].doseCount === 1
                                                }"
                                                @input="dosageChange(chinese, index)"
                                            >
                                            </abc-input-number>
                                        </li>
                                        <li>
                                            剂
                                        </li>
                                    </ul>
                                </li>
                                <li class="item" style="width: 140px;">
                                    退药数量
                                </li>
                                <li v-if="traceCodeCollectionCheck" class="trace-code">
                                    追溯码
                                </li>
                            </ul>
                        </div>
                        <div class="table-content">
                            <ul>
                                <template v-for="(m, n) in chinese.dispensingFormItems">
                                    <li v-if="showCol(m)" :key="m.id">
                                        <label style="display: inline-flex; flex: 1; gap: 8px;">
                                            <div class="ellipsis">
                                                <abc-checkbox
                                                    v-model="m.checked"
                                                    :checked="m.checked"
                                                    :label="m"
                                                    :disabled="isDisabled"
                                                    style="margin-right: 8px;"
                                                    @change="changeCheck(chinese, true, index, n)"
                                                ></abc-checkbox>
                                                <span :title="m.name" class="ellipsis">{{ m.name }}</span>
                                            </div>
                                            <div class="item" style="width: 140px;">
                                                {{ m.doseCount }}剂
                                                <div style="display: inline-block; color: #8d9aa8; text-align: center;">
                                                    ×
                                                </div>
                                                <template> {{ m.unitCount }}{{ m.unit || 'g' }} </template>
                                            </div>
                                        </label>
                                        <div v-if="traceCodeCollectionCheck" class="trace-code" @click.prevent.stop="">
                                            <template v-if="isCompatibleHistoryData(m) || isNoTraceCodeGoods(m)">
                                                <template v-if="!m.checked && isNoTraceCodeGoods(m)"></template>
                                                <abc-select
                                                    v-else-if="canSetTraceCode(m)"
                                                    v-model="m.selectedTraceCodeList"
                                                    multiple
                                                    correct-multiple-clear-value
                                                    :width="240"
                                                    :inner-width="250"
                                                    multi-label-mode="text"
                                                    with-search
                                                    clearable
                                                    :max-tag="1"
                                                    size="small"
                                                    :disabled="m.disabledTraceCodeList"
                                                    disabled-input-enter
                                                    :fetch-suggestions="(key) => handleSearch(key, m)"
                                                >
                                                    <abc-option
                                                        v-for="(option, key) in m._traceableCodeListOptions"
                                                        :key="key"
                                                        :value="option.keyId"
                                                        :label="option.no"
                                                    >
                                                        {{ option.no }}
                                                        <abc-text style="margin-left: 8px;" theme="gray">
                                                            {{ option.unit ? `${option.count}${option.unit}` : `x${option.count}` }}
                                                        </abc-text>
                                                    </abc-option>
                                                </abc-select>
                                                <abc-text v-else theme="gray" style="padding-left: 8px;">
                                                    {{ isNoTraceCodeGoods(m) ? '无码商品' : '发药时未采集追溯码' }}
                                                </abc-text>
                                            </template>
                                            <template v-else>
                                                <refund-trace-code-operation ref="traceCodeOperation" :code="m" @submit="(list)=>handleRefundTraceCodeOperationSubmit(m,list)"></refund-trace-code-operation>
                                            </template>
                                        </div>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </div>
                </div>

                <!--套餐-->
                <div v-for="form in composeList" :key="form.id" style="margin-bottom: 24px;">
                    <div v-for="item in form.dispensingFormItems" :key="item.id" class="abc-table-list">
                        <div class="table-title">
                            <ul>
                                <abc-checkbox
                                    v-model="item.checkedAll"
                                    :disabled="isDisabled"
                                    style="margin-right: 8px;"
                                    @change="composeFullCheckChange(item)"
                                ></abc-checkbox>
                                <li class="title">
                                    {{ item.name }}
                                </li>
                                <li class="specification">
                                    规格
                                </li>
                                <li class="batches">
                                    退药批次
                                </li>
                                <li class="item">
                                    退药数量
                                </li>
                                <li v-if="traceCodeCollectionCheck" class="trace-code">
                                    追溯码
                                </li>
                            </ul>
                        </div>
                        <div class="table-content">
                            <ul>
                                <template v-for="child in item.composeChildren">
                                    <li :key="child.id">
                                        <label style="display: inline-flex; flex: 1; gap: 8px;">
                                            <div class="ellipsis">
                                                <abc-checkbox
                                                    v-model="child.checked"
                                                    :checked="child.checked"
                                                    :label="child"
                                                    :disabled="isDisabled"
                                                    style="margin-right: 8px;"
                                                    @change="changeChildCheck(item, child)"
                                                ></abc-checkbox>
                                                <span :title="child.name" class="ellipsis">{{ child | formatGoodsName }}</span>
                                            </div>

                                            <div class="specification ellipsis" :title="child.productInfo | getSpec">
                                                {{ child.productInfo | getSpec }}
                                            </div>
                                        </label>
                                        <div class="batches"></div>
                                        <div class="item">
                                            <abc-form-item
                                                v-if="child.checked"
                                                required
                                                :validate-event="validate(child)"
                                                :error-style="errorStyle"
                                            >
                                                <abc-input
                                                    v-abc-focus-selected
                                                    :value="child._unitCount"
                                                    class="count-center"
                                                    :width="50"
                                                    :config="countConfig(child)"
                                                    :disabled="isDisabled"
                                                    type="number"
                                                    size="small"
                                                    @change="(val)=>changeCount(child,val)"
                                                >
                                                    <span slot="append">{{ child.unit }}</span>
                                                </abc-input>
                                            </abc-form-item>
                                        </div>
                                        <div v-if="traceCodeCollectionCheck" class="trace-code" @click.prevent.stop="">
                                            <template v-if="isCompatibleHistoryData(child) || isNoTraceCodeGoods(child)">
                                                <template v-if="!child.checked && isNoTraceCodeGoods(child)"></template>
                                                <abc-select
                                                    v-else-if="canSetTraceCode(child)"
                                                    v-model="child.selectedTraceCodeList"
                                                    multiple
                                                    multi-label-mode="text"
                                                    :width="240"
                                                    :inner-width="250"
                                                    with-search
                                                    clearable
                                                    :max-tag="1"
                                                    size="small"
                                                    :disabled="child.disabledTraceCodeList"
                                                    disabled-input-enter
                                                    :fetch-suggestions="(key) => handleSearch(key, child)"
                                                >
                                                    <abc-option
                                                        v-for="(option, key) in child._traceableCodeListOptions"
                                                        :key="key"
                                                        :value="option.keyId"
                                                        :label="option.no"
                                                    >
                                                        {{ option.no }}
                                                        <abc-text style="margin-left: 8px;" theme="gray">
                                                            {{ option.unit ? `${option.count}${option.unit}` : `x${option.count}` }}
                                                        </abc-text>
                                                    </abc-option>
                                                </abc-select>
                                                <abc-text v-else theme="gray" style="padding-left: 8px;">
                                                    {{ isNoTraceCodeGoods(child) ? '无码商品' : '发药时未采集追溯码' }}
                                                </abc-text>
                                            </template>
                                            <template v-else>
                                                <refund-trace-code-operation ref="traceCodeOperation" :code="child" @submit="(list)=>handleRefundTraceCodeOperationSubmit(child,list)"></refund-trace-code-operation>
                                            </template>
                                        </div>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </div>
                </div>

                <!--赠品-->
                <div v-for="item in giftList" :key="item.id" class="abc-table-list">
                    <div class="table-title">
                        <ul>
                            <abc-checkbox
                                v-model="item.checkedAll"
                                :disabled="isDisabled"
                                style="margin-right: 8px;"
                                @change="fullCheckChange(item)"
                            ></abc-checkbox>
                            <li class="title">
                                赠品
                            </li>
                            <li class="specification">
                                规格
                            </li>
                            <li class="batches">
                                退药批次
                            </li>
                            <li class="item">
                                退药数量
                            </li>
                            <li v-if="traceCodeCollectionCheck" class="trace-code">
                                追溯码
                            </li>
                        </ul>
                    </div>
                    <div class="table-content">
                        <ul>
                            <template v-for="m in item.dispensingFormItems">
                                <li :key="m.id">
                                    <label style="display: inline-flex; flex: 1; gap: 8px;">
                                        <div class="ellipsis">
                                            <abc-checkbox
                                                v-model="m.checked"
                                                :checked="m.checked"
                                                :label="m"
                                                :disabled="isDisabled"
                                                style="margin-right: 8px;"
                                                @change="handleChangeItemCheck(m, item)"
                                            ></abc-checkbox>
                                            <span :title="m.name" class="ellipsis">{{ m.name }}</span>
                                        </div>
                                        <div class="specification ellipsis" :title="m.productInfo | getSpec">
                                            {{ m.productInfo | getSpec }}
                                        </div>
                                    </label>

                                    <div class="batches" @click.prevent="onSelectBatch(m)">
                                        <template v-if="!m.checked && traceCodeCollectionCheck"></template>
                                        <template v-else-if="showBatches(m)">
                                            <template v-if="m.dispensingFormItemBatches.length <= 2">
                                                <p
                                                    v-for="it in m.dispensingFormItemBatches"
                                                    :key="it.batchId"
                                                    style="color: var(--abc-color-theme1); cursor: pointer;"
                                                >
                                                    {{ it.batchNo || '-' }}
                                                </p>
                                            </template>
                                            <template v-else>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    {{ m.dispensingFormItemBatches[0].batchNo }}
                                                </p>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    等{{ m.dispensingFormItemBatches.length }}个批次
                                                </p>
                                            </template>
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </div>
                                    <div class="item">
                                        <abc-form-item
                                            v-if="m.checked"
                                            required
                                            :validate-event="validate(m)"
                                            :error-style="errorStyle"
                                        >
                                            <abc-input
                                                v-abc-focus-selected
                                                :value="m._unitCount"
                                                class="count-center"
                                                :width="50"
                                                size="small"
                                                :config="countConfig(m)"
                                                :disabled="isDisabled"
                                                type="number"
                                                @change="(val)=>changeCount(m,val)"
                                            >
                                                <span slot="append">{{ m.unit }}</span>
                                            </abc-input>
                                        </abc-form-item>
                                    </div>
                                    <div v-if="traceCodeCollectionCheck" class="trace-code" @click.prevent.stop="">
                                        <template v-if="isCompatibleHistoryData(m) || isNoTraceCodeGoods(m)">
                                            <template v-if="!m.checked && isNoTraceCodeGoods(m)"></template>
                                            <abc-select
                                                v-else-if="canSetTraceCode(m)"
                                                v-model="m.selectedTraceCodeList"
                                                multiple
                                                correct-multiple-clear-value
                                                multi-label-mode="text"
                                                :width="240"
                                                :inner-width="250"
                                                with-search
                                                clearable
                                                :max-tag="1"
                                                size="small"
                                                :disabled="m.disabledTraceCodeList"
                                                disabled-input-enter
                                                :fetch-suggestions="(key) => handleSearch(key, m)"
                                            >
                                                <abc-option
                                                    v-for="(option, key) in m._traceableCodeListOptions"
                                                    :key="key"
                                                    :value="option.keyId"
                                                    :label="option.no"
                                                >
                                                    {{ option.no }}
                                                    <abc-text style="margin-left: 8px;" theme="gray">
                                                        {{ option.unit ? `${option.count}${option.unit}` : `x${option.count}` }}
                                                    </abc-text>
                                                </abc-option>
                                            </abc-select>
                                            <abc-text v-else theme="gray" style="padding-left: 8px;">
                                                {{ isNoTraceCodeGoods(m) ? '无码商品' : '发药时未采集追溯码' }}
                                            </abc-text>
                                        </template>
                                        <template v-else>
                                            <refund-trace-code-operation ref="traceCodeOperation" :code="m" @submit="(list)=>handleRefundTraceCodeOperationSubmit(m,list)"></refund-trace-code-operation>
                                        </template>
                                    </div>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>

                <!--配镜处方-->
                <div v-for="(item, index) in eyeglasses" :key="item.id" class="abc-table-list">
                    <div class="table-title">
                        <ul>
                            <abc-checkbox
                                v-model="item.checkedAll"
                                :disabled="isDisabled"
                                style="margin-right: 8px;"
                                @change="fullCheckChange(item)"
                            ></abc-checkbox>
                            <li class="title">
                                眼镜 {{ translateH(westernList.length, index) }}
                            </li>
                            <li class="specification">
                                规格
                            </li>
                            <li class="batches">
                                退药批次
                            </li>
                            <li class="item">
                                退药数量
                            </li>
                        </ul>
                    </div>
                    <div class="table-content">
                        <ul>
                            <label v-for="m in item.dispensingFormItems" :key="m.id">
                                <li v-if="showCol(m)">
                                    <div class="ellipsis">
                                        <abc-checkbox
                                            v-model="m.checked"
                                            :checked="m.checked"
                                            :label="m"
                                            :disabled="isDisabled"
                                            style="margin-right: 8px;"
                                            @change="changeCheck(item)"
                                        ></abc-checkbox>
                                        <span :title="m.name" class="ellipsis">{{ m.name }}</span>
                                    </div>

                                    <div class="specification ellipsis" :title="m.productInfo | getSpec">
                                        {{ m.productInfo | getSpec }}
                                    </div>
                                    <div class="batches" @click.prevent="onSelectBatch(m)">
                                        <template v-if="showBatches(m)">
                                            <template v-if="m.dispensingFormItemBatches.length <= 2">
                                                <p
                                                    v-for="it in m.dispensingFormItemBatches"
                                                    :key="it.batchId"
                                                    style="color: var(--abc-color-theme1); cursor: pointer;"
                                                >
                                                    {{ it.batchNo || '-' }}
                                                </p>
                                            </template>
                                            <template v-else>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    {{ m.dispensingFormItemBatches[0].batchNo || '-' }}
                                                </p>
                                                <p style=" color: var(--abc-color-theme1); cursor: pointer;">
                                                    等{{ m.dispensingFormItemBatches.length }}个批次
                                                </p>
                                            </template>
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </div>

                                    <div class="item">
                                        <abc-form-item
                                            v-if="m.checked"
                                            required
                                            :validate-event="validate(m)"
                                            :error-style="errorStyle"
                                        >
                                            <abc-input
                                                v-abc-focus-selected
                                                :value="m._unitCount"
                                                class="count-center"
                                                :width="50"
                                                :config="countConfig(m)"
                                                :disabled="isDisabled"
                                                type="number"
                                                size="small"
                                                @change="(val)=>changeCount(m,val)"
                                            >
                                                <span slot="append">{{ m.unit }}</span>
                                            </abc-input>
                                        </abc-form-item>

                                        <template v-else> {{ m.canRefundUnitCount }}{{ m.unit }} </template>
                                    </div>
                                </li>
                            </label>
                        </ul>
                    </div>
                </div>
            </abc-form>
            <div v-else class="table-empty">
                <div class="abc-content-empty-wrapper abc-content-empty--normal with-icon" style="top: 0;">
                    <div class="empty-icon">
                        <svg aria-hidden="true" class="abc-icon iconfont cis-icon-s-emptyIcon-normal abc-iconfont-svg" style="font-size: 64px; line-height: 64px;">
                            <use xlink:href="#icon-s-emptyIcon-normal" />
                        </svg>
                    </div>
                    <div class="empty-label">
                        暂无退药项目
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button
                theme="danger"
                variant="ghost"
                data-cy="refund-dispense-dialog-confirm-btn"
                :disabled="refundDisabled"
                :loading="getRefundSocialAllFlagLoading"
                @click="confirmRefund"
            >
                退药
            </abc-button>
            <abc-button variant="ghost" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>
<script type="text/ecmascript-6">
    import clone from 'utils/clone';
    import DispensaryAPI from '@/api/dispensary';
    import Logger from 'utils/logger';

    import { getSpec } from 'src/filters/index';
    import { SourceFormTypeEnum } from '@/service/charge/constants';
    import { GoodsTypeEnum } from '@abc/constants';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import { dispenseItemStatusEnum } from 'views/pharmacy/constants';
    import { PriceType } from 'views/common/inventory/constants';
    // import DialogBatchesSelector from '@/views-pharmacy/charge/components/dialog-batches-selector';
    import DialogRefundBatchesSelector from '@/views-pharmacy/charge/components/refund/dialog-refund-batches-selector';
    import { mapGetters } from 'vuex';
    import TraceCode from '@/service/trace-code/service';
    import {
        createGUID,
    } from '@/utils';
    import RefundTraceCodeOperation from 'views/pharmacy/refund-trace-code-operation.vue';

    export default {
        name: 'ChargeDialog',
        components: {
            RefundTraceCodeOperation,
        },
        inject: {
            detailProvide: {
                default: {
                    isVirtualPharmacy: false,
                },
            },
        },
        props: {
            value: Boolean,
            // eslint-disable-next-line vue/require-default-prop
            dispenseId: String,
            dispensingForms: Array,
            canPartUnDispense: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                showDialog: this.value,
                refundDialogVisible: false,
                refundLoading: false,

                westernList: [],
                infusionList: [],
                chineseList: [],
                externList: [],
                goodsList: [],
                materialList: [],
                giftList: [],
                composeList: [],
                refundData: [],
                eyeglasses: [],
                errorStyle: {
                    'margin-left': '-70px',
                },
                getRefundSocialAllFlagLoading: false,
            };
        },

        computed: {
            ...mapGetters([
                'traceCodeConfig',
                'dispensingConfig',
                'clinicBasic',
            ]),
            isPrecheckRefundAllSocialItem() {
                const { shebao } = this.clinicBasic.charge || {};
                const { allRefund } = shebao || {};
                return allRefund;
            },
            // 开启追溯码功能
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            // 是否整单发退药模式
            isWholeBillCharge() {
                return !!this.dispensingConfig.wholeSheetOperateEnable;
            },
            isDisabled() {
                // 后续如果其他业务也需要控制禁用勾选与输入在这里加逻辑
                return this.isWholeBillCharge || !this.canPartUnDispense;
            },
            // 没选择项目时，不可点击退药
            refundDisabled() {
                const refundList = [];
                let refundDisabled = true;
                this.getRefundList(this.westernList, refundList);
                this.getRefundList(this.externList, refundList);
                this.getRefundList(this.infusionList, refundList);
                this.getRefundList(this.chineseList, refundList);
                this.getRefundList(this.goodsList, refundList);
                this.getRefundList(this.materialList, refundList);
                this.getRefundList(this.giftList, refundList);
                this.getRefundList(this.composeList, refundList);
                this.getRefundList(this.eyeglasses, refundList);

                refundList.forEach((item) => {
                    if (item.dispensingFormItems.length) {
                        refundDisabled = false;
                    }
                });
                return refundDisabled;
            },
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        async created() {
            this._HArray = [ '一', '二', '三', '四', '五', '六', '七', '八', '九' ];
            clone(this.dispensingForms).forEach((form) => {
                const obj = Object.assign(form, { checkedAll: this.isDisabled || false });

                obj.dispensingFormItems = obj.dispensingFormItems.filter((item) => {
                    item.composeChildren = (item.composeChildren || []).filter((child) => {
                        const canRefund = this.getCanRefundCount(item.composeChildren || [], child);
                        child.checked = this.isDisabled || false;
                        child.canRefundUnitCount = canRefund;
                        child._unitCount = canRefund;
                        // 处理退药批次
                        const dispensed = item.composeChildren.find((x) => x.status === dispenseItemStatusEnum.DISPENSED && x.id === child.id);
                        const { dispensingFormItemBatches } = dispensed || {};
                        child.dispensingFormItemBatches = dispensingFormItemBatches;
                        // 用来退追溯码
                        child.selectedTraceCodeList = [];
                        this.handleSearch('', child);

                        // 退药： 已发的（但不是部分发；部分发的会拼到form上 - 且状态是已发；） + 部分发的 + 可退数大于0
                        return child.status === dispenseItemStatusEnum.DISPENSED && canRefund > 0;
                    });

                    item.checkedAll = item.composeChildren.filter((it) => {
                        return it.checked;
                    }).length === item.composeChildren.length;

                    const canRefund = this.getCanRefundCount(obj.dispensingFormItems, item, obj.sourceFormType);

                    item.checked = this.isDisabled ? true : item.sourceItemType === OutpatientChargeTypeEnum.NO_CHARGE;
                    item.canRefundUnitCount = canRefund;
                    item._unitCount = canRefund; // 非中药处方要可以退的数量
                    // 处理退药批次
                    const dispensed = obj.dispensingFormItems.find((x) => x.status === dispenseItemStatusEnum.DISPENSED && x.id === item.id);
                    const { dispensingFormItemBatches } = dispensed || {};
                    item.dispensingFormItemBatches = dispensingFormItemBatches;

                    if (obj.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                        item.doseCount = canRefund; // 中药处方要退的数量
                    }

                    // 用来退追溯码
                    item.selectedTraceCodeList = [];
                    this.handleSearch('', item);

                    // 同上
                    return item.composeChildren.length ||
                        (((item.status === dispenseItemStatusEnum.DISPENSED && !item.partDispense) ||
                            item.status === dispenseItemStatusEnum.PART) && canRefund > 0);
                });

                if (obj.dispensingFormItems.length) {
                    switch (form.sourceFormType) {
                        case SourceFormTypeEnum.PRESCRIPTION_WESTERN:
                            this.westernList.push(obj);
                            break;

                        case SourceFormTypeEnum.PRESCRIPTION_EXTERNAL:
                            this.externList.push(obj);
                            break;
                        case SourceFormTypeEnum.PRESCRIPTION_INFUSION:
                            this.infusionList.push(obj);
                            break;

                        case SourceFormTypeEnum.PRESCRIPTION_CHINESE:
                            this.chineseList.push(obj);
                            break;

                        case SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM:
                            this.goodsList.push(obj);
                            break;

                        case SourceFormTypeEnum.MATERIAL:
                            this.materialList.push(obj);
                            break;

                        case SourceFormTypeEnum.GIFT:
                            this.giftList.push(obj);
                            break;

                        case SourceFormTypeEnum.COMPOSE:
                            this.composeList.push(obj);
                            break;

                        case SourceFormTypeEnum.EYEGLASSES:
                            this.eyeglasses.push(obj);
                            break;

                        default:

                    }
                }

                // 整单发退药时,会自动全选所有药品，需要将追溯码都退掉
                if (obj.checkedAll) {
                    obj.dispensingFormItems.forEach((item) => {
                        this.initSelectedTraceCodeList(item);
                    });
                }
            });
            this._chineseList = clone(this.chineseList);

            this._canRefund = !!(this.westernList.length ||
                this.infusionList.length ||
                this.externList.length ||
                this.chineseList.length ||
                this.goodsList.length ||
                this.giftList.length ||
                this.composeList.length ||
                this.eyeglasses.length ||
                this.materialList.length);
        },

        beforeDestroy() {
            this.destroyDialog();
        },

        methods: {
            canSetTraceCode(item) {
                if (!item.traceableCodeList || item.traceableCodeList.length === 0) return false;
                return TraceCode.formItemSupportTraceCode(item);
            },
            handleSearch(key, item) {
                key = key.trim();
                const _arr = [];
                item.traceableCodeList?.forEach((it) => {
                    if (it.no?.indexOf(key) > -1 && it.used === 1) {
                        _arr.push(it);
                    }
                });
                item._traceableCodeListOptions = _arr.map((it) => {
                    it.keyId = it.keyId || createGUID();
                    it.count = it.count || 1;
                    return it;
                });
            },
            // 可以退的数量 = 已发 - 已退
            getCanRefundCount(items, item, formType) {
                const { id } = item;
                const dispensed = items.filter((o) => o.status === dispenseItemStatusEnum.DISPENSED && o.id === id);
                const refund = items.filter((o) => o.status === dispenseItemStatusEnum.RETURN && o.id === id);

                if (formType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                    return dispensed[0]?.doseCount ?? 0 - refund[0]?.doseCount ?? 0;
                }

                return dispensed[0]?.unitCount ?? 0 - refund[0]?.unitCount ?? 0;
            },

            /**
             * @desc 是否展示退费项目
             * 1. 自备不展示
             * <AUTHOR>
             * @date 2022-03-16 15:32:16
             */
            showCol(item) {
                return item.sourceItemType !== OutpatientChargeTypeEnum.NO_CHARGE;
            },

            chineseDoseCount(index) {
                const chineseDoseCount = clone(this._chineseList[index].dispensingFormItems[0].doseCount);
                return chineseDoseCount;
            },

            dosageChange(val, index) {
                if (!val.dispensingFormItems[0].doseCount) {
                    val.dispensingFormItems.forEach((item) => {
                        item.doseCount = this.chineseDoseCount(index);
                    });
                    return false;
                }

                if (val.dispensingFormItems[0].doseCount > this.chineseDoseCount(index)) {
                    val.dispensingFormItems.forEach((item) => {
                        item.doseCount = this.chineseDoseCount(index);
                    });
                    this.$Toast({
                        message: `退药数量不能超过原发药数量(${this.chineseDoseCount(index)}剂)`,
                        type: 'info',
                    });
                    this.$refs.chinesedosagecount[0]._data.currentValue = this.chineseDoseCount(index);
                } else {
                    val.dispensingFormItems.forEach((item) => {
                        item.doseCount = val.dispensingFormItems[0].doseCount;
                    });
                }
                if (val.checkedAll) {
                    val.dispensingFormItems.forEach((it) => {
                        // 中药的 _unitCount 其实是doseCount
                        it._unitCount = val.dispensingFormItems[0].doseCount;
                        this.initSelectedTraceCodeList(it);
                    });
                }
            },
            /**
             * @desc 开药量 是否允许输入 小数的判断
             * @desc 允许拆零的西药/中成药，当开药量单位选择 小单位 且 小单位是ml 时，可以输入 3位小数
             * <AUTHOR>
             * @date 2019/03/24 12:44:49
             */
            countConfig(item) {
                const isWM = item.productType === 1; // 是西药/中成药
                const isMaterial = item.productType === GoodsTypeEnum.MATERIAL; // 是物资
                const dismounting = !!item.productInfo.dismounting; // 是西药/中成药
                const isFormUnit = item.unit === item.productInfo.pieceUnit; // 开药量单位选择小单位
                const formUnitIsML = item.productInfo.pieceUnit === 'ml'; // 开药量单位选择小单位
                if (isWM && dismounting && isFormUnit && formUnitIsML) {
                    return {
                        formatLength: 3, max: 9999999,
                    };
                }
                if (isWM || isMaterial) {
                    return {
                        formatLength: 2, max: 9999999,
                    };
                }
                return { max: 9999999 };

            },

            getSpec,
            /**
             * @desc 全选/反选
             * <AUTHOR>
             * @date 2019/03/19 17:05:50
             */
            fullCheckChange(form, isChinese = false, index) {
                form.dispensingFormItems.forEach((item) => {
                    // 自备药品自动选中
                    if (item.sourceItemType === OutpatientChargeTypeEnum.NO_CHARGE) {
                        item.checked = true;
                    } else {
                        item.checked = form.checkedAll;
                    }
                    if (item.checked) {
                        this.initSelectedTraceCodeList(item);
                    } else {
                        item._unitCount = +item.canRefundUnitCount;
                    }
                });
                if (isChinese) {
                    form.dispensingFormItems.forEach((item, i) => {
                        item.doseCount = this._chineseList[index].dispensingFormItems[i].doseCount;
                    });
                }
            },
            composeFullCheckChange(item) {
                item.composeChildren.forEach((child) => {
                    child.checked = item.checkedAll;
                    if (child.checked) {
                        this.initSelectedTraceCodeList(child);
                    }
                });
            },

            handleChangeItemCheck(item, form) {
                if (!item.checked) {
                    item._unitCount = +item.canRefundUnitCount;
                }
                this.changeCheck(form);
                this.initSelectedTraceCodeList(item);
            },

            /**
             * <AUTHOR>
             * @date 2019/04/06 19:23:29
             * @isChinese 是否为中药处方
             */
            changeCheck(form, isChinese = false, index) {
                form.checkedAll = form.dispensingFormItems.filter((item) => {
                    return item.checked;
                }).length === form.dispensingFormItems.length;

                if (isChinese) {
                    form.dispensingFormItems.forEach((item, i) => {
                        if (!form.checkedAll) {
                            item.doseCount = this._chineseList[index].dispensingFormItems[i].doseCount;
                        }
                        this.initSelectedTraceCodeList(item);
                    });
                }
            },
            changeChildCheck(item, child) {
                item.checkedAll = item.composeChildren.filter((it) => {
                    return it.checked;
                }).length === item.composeChildren.length;
                this.initSelectedTraceCodeList(child);
            },

            validate(medicine) {
                if (medicine._unitCount === 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '退药数量不能为0',
                        });
                    };
                } if (medicine._unitCount > medicine.canRefundUnitCount) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: `退药数量不能大于发药数量（${medicine.canRefundUnitCount}${medicine.unit || ''}）`,
                        });
                    };
                }
                return (_, callback) => {
                    callback({
                        validate: true,
                    });
                };
            },

            translateH(length, index) {
                if (length > 1) {
                    return this._HArray[ index ];
                }
                return '';

            },

            /**
             * @desc 确认退费
             * <AUTHOR>
             * @date 2018/07/25 23:04:18
             */
            confirmRefund() {
                this.$refs.refundForm?.validate(async(valid) => {
                    if (valid) {
                        const refundList = [];
                        this.getRefundList(this.westernList, refundList);
                        this.getRefundList(this.externList, refundList);
                        this.getRefundList(this.infusionList, refundList);
                        this.getRefundList(this.chineseList, refundList);
                        this.getRefundList(this.goodsList, refundList);
                        this.getRefundList(this.materialList, refundList);
                        this.getRefundList(this.giftList, refundList);
                        this.getRefundList(this.composeList, refundList);
                        this.getRefundList(this.eyeglasses, refundList);
                        if (refundList.length === 0) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: '没有选择退药项目',
                            });
                            return false;
                        }
                        const operations = this.$refs?.traceCodeOperation ?? [];
                        let finalValidate = false;
                        operations.forEach((operation) => {
                            const validate = operation.validateTraceableCodeCount();
                            if (validate) {
                                finalValidate = true;
                            }
                        });
                        if (finalValidate) return false;
                        const needCheckAllSocialItems = await this.getRefundSocialAllFlag(refundList);
                        if (needCheckAllSocialItems.length) {
                            const h = this.$createElement;
                            this._message = this.$confirm({
                                type: 'warn',
                                title: '未选择所有医保结算项目退药',
                                content: '医保结算项目须整单原路退回，否则可能导致追溯码采集不符合依码支付要求，医保不予拨款。建议选择本单所有医保结算项目，重新发起退药。',
                                contentStyles: {
                                    width: '500px',
                                },
                                showConfirm: false,
                                showCancel: false,
                                footerPrepend: h('abc-flex', {
                                    attrs: {
                                        gap: 0,
                                    },
                                }, [
                                    h('abc-button', {
                                        attrs: {
                                            variant: 'ghost',
                                            theme: 'primary',
                                        },
                                        on: {
                                            click: () => {
                                                this.checkAllSocialItems(needCheckAllSocialItems);
                                                this._message.close();
                                            },
                                        },
                                    }, '选择全部医保项目退药'),
                                    h('abc-button', {
                                        attrs: {
                                            variant: 'ghost',
                                            theme: 'danger',
                                        },
                                        on: {
                                            click: () => {
                                                this.$emit('confirm', refundList);
                                                this._message.close();
                                            },
                                        },
                                    }, '忽略风险，继续退药'),
                                ]),
                            });
                            return false;
                        }
                        this.$emit('confirm', refundList);
                    }
                    console.log('error submit!!');
                    return false;

                });
            },

            /**
             * @desc 退费前校验
             * @param {array} refundList 当前退费项目
             * @return {array} 尚未勾选的医保项目
             */
            async getRefundSocialAllFlag(refundList) {
                try {
                    this.getRefundSocialAllFlagLoading = true;
                    if (!this.isPrecheckRefundAllSocialItem) return [];
                    const refundItems = [];
                    refundList.forEach((form) => {
                        form.dispensingFormItems.forEach((item) => {
                            if (item.composeChildren && item.composeChildren.length) {
                                item.composeChildren.forEach((child) => {
                                    refundItems.push({
                                        dispensingFormItemId: child.id,
                                        doseCount: child.doseCount,
                                        unitCount: child.unitCount,
                                    });
                                });
                            } else {
                                refundItems.push({
                                    dispensingFormItemId: item.id,
                                    doseCount: item.doseCount,
                                    unitCount: item.unitCount,
                                });
                            }
                        });
                    });
                    const preCheckPostData = {
                        dispensingSheetId: this.dispenseId,
                        items: refundItems,
                    };
                    const { data } = await DispensaryAPI.undispensePreCheck(this.dispenseId, preCheckPostData);
                    return data?.items || [];
                } catch (err) {
                    Logger.report({
                        scene: 'getRefundSocialAllFlag',
                        err,
                    });
                    return [];
                } finally {
                    this.getRefundSocialAllFlagLoading = false;
                }
            },

            /**
             * @desc 勾选所有医保项目，并设置退药数量
             * @param {Array<{dispensingFormItemId: string, unitCount: number, doseCount: number}>} needCheckAllSocialItems 尚未勾选的医保项目
             */
            checkAllSocialItems(needCheckAllSocialItems) {
                if (!needCheckAllSocialItems || !needCheckAllSocialItems.length) return;

                // 需要遍历的列表
                const allLists = [
                    this.westernList,
                    this.infusionList,
                    this.chineseList,
                    this.externList,
                    this.goodsList,
                    this.materialList,
                    this.giftList,
                    this.eyeglasses,
                ];

                // 处理普通列表
                allLists.forEach((list) => {
                    if (!list || !list.length) return;

                    list.forEach((form) => {
                        let hasCheckedItem = false;

                        form.dispensingFormItems.forEach((item) => {
                            // 检查是否在需要勾选的医保项目中
                            const matchedSocialItem = needCheckAllSocialItems.find(
                                (socialItem) => socialItem.dispensingFormItemId === item.id,
                            );

                            if (matchedSocialItem) {
                                item.checked = true;
                                // 设置退药数量为匹配项的unitCount
                                item._unitCount = matchedSocialItem.unitCount;
                                item.doseCount = matchedSocialItem.doseCount;
                                this.initSelectedTraceCodeList(item);
                                hasCheckedItem = true;
                            }
                        });

                        // 更新form的全选状态
                        if (hasCheckedItem) {
                            this.changeCheck(form);
                        }
                    });
                });

                // 单独处理套餐列表，因为结构不同
                if (this.composeList && this.composeList.length) {
                    this.composeList.forEach((form) => {
                        form.dispensingFormItems.forEach((item) => {
                            let hasCheckedChild = false;

                            (item.composeChildren || []).forEach((child) => {
                                const matchedSocialItem = needCheckAllSocialItems.find(
                                    (socialItem) => socialItem.dispensingFormItemId === child.id,
                                );

                                if (matchedSocialItem) {
                                    child.checked = true;
                                    // 设置退药数量为匹配项的unitCount
                                    child._unitCount = matchedSocialItem.unitCount;
                                    child.doseCount = matchedSocialItem.doseCount;
                                    this.initSelectedTraceCodeList(child);
                                    hasCheckedChild = true;
                                }
                                // 更新item的全选状态
                                if (hasCheckedChild) {
                                    this.changeChildCheck(item, child);
                                }
                            });
                        });
                    });
                }
            },

            getTraceableCodeList(item) {
                const {
                    selectedTraceCodeList,
                    _traceableCodeListOptions,
                } = item;
                if (this.isCompatibleHistoryData(item) || this.isNoTraceCodeGoods(item)) {
                    const _arr = [];
                    selectedTraceCodeList?.forEach((codeKeyId) => {
                        const res = _traceableCodeListOptions.find((it) => it.keyId === codeKeyId);
                        if (res) {
                            _arr.push(res);
                        }
                    });
                    return _arr;
                }
                return selectedTraceCodeList;

            },

            getRefundList(list, refundList) {
                list.forEach((form) => {
                    const dispensingFormItems = [];
                    form.dispensingFormItems.forEach((item) => {
                        if (item.checked) {

                            dispensingFormItems.push({
                                id: item.id,
                                name: item.name,
                                unit: item.unit,
                                productInfo: item.productInfo,
                                // 中药不更改unitCount
                                unitCount: form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE ? item.unitCount : +item._unitCount,
                                doseCount: item.doseCount,
                                sourceItemType: item.sourceItemType,
                                dispensingFormItemBatches: item._dispensingFormItemBatches,
                                traceableCodeList: this.getTraceableCodeList(item) || [],
                            });
                        }

                        if (item.composeChildren && item.composeChildren.length) {
                            const composeChildren = [];
                            item.composeChildren.forEach((child) => {
                                if (child.checked) {
                                    composeChildren.push({
                                        id: child.id,
                                        name: child.name,
                                        unit: child.unit,
                                        productInfo: child.productInfo,
                                        unitCount: +child._unitCount,
                                        doseCount: child.doseCount,
                                        dispensingFormItemBatches: child._dispensingFormItemBatches,
                                        traceableCodeList: this.getTraceableCodeList(child) || [],
                                    });
                                }
                            });

                            if (composeChildren.length) {
                                const index = dispensingFormItems.findIndex((o) => o.id === item.id);

                                if (index !== -1) {
                                    dispensingFormItems.splice(index, 1, {
                                        id: item.id,
                                        composeChildren,
                                    });
                                } else {
                                    dispensingFormItems.push({
                                        id: item.id,
                                        composeChildren,
                                    });
                                }

                            }
                        }
                    });

                    // 处理中药数据
                    if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                        // 如果是按剂发，doseCount需要再form层展示
                        if (form.formSupportUnDispenseByDose && form.checkedAll) {
                            form.doseCount = form.dispensingFormItems[0].doseCount;
                        } else {
                            form.doseCount = null;
                        }

                        refundList.push({
                            id: form.id,
                            doseCount: form.doseCount,
                            undispenseType: form.sourceFormType === 6 ? 1 : 0,
                            sourceFormType: form.sourceFormType,
                            dispensingFormItems,
                        });

                        return;
                    }

                    refundList.push({
                        id: form.id,
                        undispenseType: form.sourceFormType === 6 ? 1 : 0,
                        sourceFormType: form.sourceFormType,
                        dispensingFormItems,
                    });
                });
            },

            showBatches(item) {
                const {
                    productInfo,
                    dispensingFormItemBatches,
                } = item;
                const { priceType } = productInfo;
                // 固定售价
                if (priceType === PriceType.PRICE) return false;
                // 没有批次信息
                if (!dispensingFormItemBatches || dispensingFormItemBatches.length === 0) return false;
                return true;
            },
            // 选择批次
            onSelectBatch(item) {
                if (!this.showBatches(item)) return;
                if (!item.checked) {
                    item.checked = true;
                }
                const cloneItem = clone(item);
                cloneItem.unitCount = Math.min(cloneItem._unitCount, cloneItem.canRefundUnitCount || 0);
                let remainingUnitCount = cloneItem.unitCount || 0;
                const lastUserCheckedBatchUnitCount = cloneItem._dispensingFormItemBatches?.reduce((acc, cur) => {
                    return acc + cur.unitCount;
                }, 0) || 0;
                // 当前输入比上次选择新增的数量
                let addCount = cloneItem.unitCount - lastUserCheckedBatchUnitCount;
                cloneItem.chargeFormItemBatchInfos = cloneItem.dispensingFormItemBatches.reverse().map((x) => {
                    const {
                        unitCount: batchUnitCount, batchId,
                    } = x;
                    let currentUnitCount = 0;
                    if (lastUserCheckedBatchUnitCount) {
                        // 这个是上次选过，保留上次选择的批次
                        cloneItem._dispensingFormItemBatches.forEach((y) => {
                            // 当前输入比上次少，从上次的基础上减
                            if (addCount <= 0) {
                                if (y.batchId === batchId) {
                                    if (remainingUnitCount > 0) {
                                        currentUnitCount = Math.min(y.unitCount, remainingUnitCount);
                                        remainingUnitCount = Math.max(remainingUnitCount - y.unitCount, 0);
                                    } else {
                                        currentUnitCount = 0;
                                    }
                                }
                            } else {
                                // 当前输入比上次多，多出来的依次向下填
                                if (y.batchId === batchId) {
                                    if (addCount > 0) {
                                        currentUnitCount = Math.min(y.unitCount + addCount, batchUnitCount);
                                        addCount = Math.max(addCount - (currentUnitCount - y.unitCount), 0);
                                        console.log('addCount', addCount);
                                    } else {
                                        currentUnitCount = y.unitCount;
                                    }
                                }
                            }
                        });
                    } else {
                        if (batchUnitCount < remainingUnitCount) {
                            currentUnitCount = batchUnitCount;
                            remainingUnitCount = Math.max(remainingUnitCount - batchUnitCount, 0);
                        } else {
                            currentUnitCount = remainingUnitCount;
                            remainingUnitCount = 0;
                        }
                    }
                    const _unitCount = x._unitCount || x.unitCount;
                    return {
                        ...x.batchInfo,
                        goodsBatchInfoSnap: x.batchInfo,
                        ...x,
                        canRefundUnitCount: _unitCount,
                        _unitCount,
                        unitCount: currentUnitCount,
                    };
                });
                this._batcherDialog = new DialogRefundBatchesSelector({
                    chargeItem: cloneItem,
                    needCostPrice: false,
                    action: '退药',
                    onConfirm: (data) => this.handleBatchesChange(item, data),
                });
                this._batcherDialog.generateDialog();
            },

            handleBatchesChange(item, data) {
                const {
                    dispensingFormItemBatches,
                } = item;
                const {
                    unitCount,
                    chargeFormItemBatchInfos,
                } = data;
                // 更新批次数量
                dispensingFormItemBatches.forEach((x) => {
                    chargeFormItemBatchInfos.forEach((y) => {
                        if (x.batchId === y.batchId) {
                            x._unitCount = x._unitCount || x.unitCount;
                            x.unitCount = y.unitCount;
                        }
                    });
                });
                item._unitCount = unitCount;
                item._dispensingFormItemBatches = clone(dispensingFormItemBatches);
                this.destroyDialog();
            },
            destroyDialog() {
                if (this._batcherDialog) {
                    this._batcherDialog.destroyDialog();
                }
            },
            /**
             * @desc 直接修改数量unitCount还原
             */
            changeCount(item,val) {
                item._unitCount = val;
                item.dispensingFormItemBatches?.forEach((x) => {
                    if (x._unitCount) {
                        x.unitCount = x._unitCount;
                        x._unitCount = undefined;
                    }
                });
                this.initSelectedTraceCodeList(item);
            },

            initSelectedTraceCodeList(item) {
                // 修改的数量是全退需要默认选中所有追溯码并且不可修改，否则清空
                if (item.checked && +item._unitCount === +item.canRefundUnitCount && item._traceableCodeListOptions) {
                    if (this.isCompatibleHistoryData(item) || this.isNoTraceCodeGoods(item)) {
                        item.selectedTraceCodeList = item._traceableCodeListOptions.map((it) => it.keyId);
                        item.disabledTraceCodeList = true;
                    } else {
                        const traceCodeList = clone(item?._traceableCodeListCache ?? []);
                        const dispensedCodeList = [];
                        traceCodeList.forEach((code) => {
                            // 已发药的追溯码
                            if (code.used === 1) {
                                dispensedCodeList.push(code);
                            }
                        });
                        this.$set(item,'dispensedTraceCodeList',dispensedCodeList);
                        item.selectedTraceCodeList = dispensedCodeList;
                    }
                } else if (item.checked && this.isNoTraceCodeGoods(item) && item._traceableCodeListOptions) {
                    item.selectedTraceCodeList = item._traceableCodeListOptions.map((it) => it.keyId);
                    item.disabledTraceCodeList = true;
                } else {
                    item.selectedTraceCodeList = [];
                    item.disabledTraceCodeList = false;
                }
            },

            isCompatibleHistoryData(item) {
                const isExist = item?._traceableCodeListCache?.some((code) => {
                    return code.hisPackageCount > 0 || code.hisPieceCount > 0;
                });
                return !isExist;
            },
            isNoTraceCodeGoods(item) {
                return TraceCode.isNoTraceCodeGoods(item?.productInfo ?? {});
            },
            handleRefundTraceCodeOperationSubmit(item,list) {
                this.$set(item,'selectedTraceCodeList',list);
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.refund-dialog-content {
    .abc-checkbox-wrapper {
        margin-right: 8px;
    }

    .abc-table-list {
        .table-title {
            height: 40px;
            line-height: 40px;
        }
    }

    .refund-dialog-list {
        margin-bottom: 24px;

        .table-title {
            height: 40px;
            line-height: 40px;

            > ul {
                li.title {
                    .abc-table-dosage {
                        display: inline-flex;
                        align-items: center;
                        height: 40px;
                        padding-left: 24px;
                        font-size: 0;

                        li {
                            display: inline-block;
                            font-size: 14px;
                            font-weight: bold;
                            color: $T2;

                            .abc-input-wrapper {
                                margin: 0 6px;
                            }
                        }

                        .table-doseCount {
                            margin: 0 8px;
                            font-weight: normal;

                            .dose-cose-wrapper {
                                .input-des-btn {
                                    border-radius: 4px 0 0 4px;

                                    i {
                                        font-size: 14px;

                                        &::before {
                                            display: block;
                                            width: 14px;
                                            height: 14px;
                                            content: " ";
                                            background: url(../../assets/images/air-pharmacy/<EMAIL>) no-repeat;
                                            background-size: 100%;
                                        }
                                    }
                                }

                                .input-add-btn {
                                    border-radius: 0 var(--abc-border-radius-small) var(--abc-border-radius-small) 0;

                                    i {
                                        font-size: 14px;

                                        &::before {
                                            display: block;
                                            width: 14px;
                                            height: 14px;
                                            content: " ";
                                            background: url(../../assets/images/air-pharmacy/<EMAIL>) no-repeat;
                                            background-size: 100%;
                                        }
                                    }
                                }
                            }

                            .desDisabled {
                                .input-des-btn {
                                    background-color: #e6eaee;
                                }
                            }

                            .addDisabled {
                                .input-add-btn {
                                    background-color: #e6eaee;
                                }
                            }

                            .abc-input-number-wrapper.button-placement-left {
                                .input-des-btn,
                                .input-add-btn {
                                    border: 1px solid $P1;
                                }

                                input {
                                    width: 38px;
                                    text-align: center;
                                    border: 1px solid $P1;
                                    border-right: 0;
                                    border-left: 0;

                                    &:hover {
                                        border: 1px solid #e8e8e8;
                                        outline: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .table-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate3d(-50%, -50%, 0);
    }
}
</style>
