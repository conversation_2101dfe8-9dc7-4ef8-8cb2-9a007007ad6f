<template>
    <abc-modal
        v-if="showDialog"
        v-model="showDialog"
        custom-class="authorized-ec-dialog"
        append-to-body
        content-style="width: 360px"
        :show-footer="false"
    >
        <div class="authorized-ec-dialog__title">
            平台授权弹窗
        </div>
    </abc-modal>
</template>


<script>
    import ECAuthAPI from '@/api/auth';
    export default {
        props: {
            value: Boolean,
            ecTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                loading: false,
                tableData: [],
                showDialog: this.value,
            };
        },
        created() {
            this.handleAuthEC();
        },
        methods: {
            async handleAuthEC() {
                try {
                    const authCode = this.$route?.query?.code;
                    if (!authCode) {
                        throw new Error('Authorization code is missing.');
                    }

                    const res = await ECAuthAPI.authEc({
                        authCode,
                        ecType: 1,
                    });
                    console.log('授权结果', res);
                    this.$emit('refresh-ec');
                } catch (err) {
                    console.error(err);
                }
            },
        },
    };
</script>
<style lang="scss">
.authorized-ec-dialog {
    left: 40%;
    width: 360px;
    height: 300px;

    &__title {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    &__content {
        margin-top: 24px;

        .authorized-ec-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 132px;
            height: 132px;
            cursor: pointer;
            border: 1px solid #e0e5ee;
            border-radius: 6px;

            .name {
                margin-top: 6px;
            }
        }
    }
}
</style>
