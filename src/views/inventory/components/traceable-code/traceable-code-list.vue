<template>
    <abc-list
        ref="listRef"
        :create-key="createKey"
        :data-list="traceableCodeList"
        show-divider
        :scroll-config="{
            paddingSize: 'tiny',
        }"
        :divider-config="{
            variant: 'dashed',
            theme: 'light',
        }"
        style="padding-top: 0;"
        :height="height"
        :hover-item-func="hoverItemFunc"
        :custom-item-class="customItemClass"
        :enable-virtual-list="enableVirtualList"
        :virtual-list-config="virtualListConfig"
    >
        <template
            #default="{
                item, index
            }"
        >
            <abc-popover
                width="260px"
                placement="bottom-end"
                trigger="hover"
                theme="yellow"
                :arrow-offset="280"
                :offset="10"
                :disabled="item.disabledPopover"
                style="width: 100%;"
            >
                <abc-flex
                    slot="reference"
                    justify="space-between"
                    align="center"
                    flex="1"
                    :data-id="item.no"
                    :class="[
                        'trace-code-list-item',
                        'ellipsis',
                        readonly ? '' : 'item-wrapper',
                    ]"
                    style="height: 26px;"
                >
                    <abc-flex :gap="8" class="ellipsis">
                        <abc-text class="ellipsis" :theme="item.theme" :title="formatNo(item)">
                            {{ formatNo(item) }}
                        </abc-text>
                        <abc-text v-if="item.count > 1" theme="gray">
                            x{{ item.count }}
                        </abc-text>
                    </abc-flex>


                    <abc-space :size="4">
                        <abc-text theme="warning-light">
                            {{ item.warningText }}
                        </abc-text>
                        <abc-flex
                            align="center"
                            justify="center"
                            style="width: 20px; height: 20px;"
                        >
                            <abc-icon
                                v-if="item.warningText"
                                class="attention-icon"
                                :size="16"
                                icon="n-alert-fill"
                                color="var(--abc-color-Y2)"
                            ></abc-icon>
                            <abc-delete-icon
                                v-if="canDelete(item)"
                                class="delete-icon"
                                data-cy="abc-delete-icon"
                                theme="dark"
                                @delete="handleDeleteItem(item, index)"
                            ></abc-delete-icon>
                        </abc-flex>
                    </abc-space>
                </abc-flex>

                <abc-flex vertical>
                    <span>{{ formatNo(item) }}</span>
                    <abc-divider
                        margin="small"
                        theme="dark"
                        variant="dashed"
                        size="normal"
                    ></abc-divider>
                    <span>采集追溯码与该商品绑定的产品标识码不同，请确认是否采集错误</span>
                </abc-flex>
            </abc-popover>
        </template>
    </abc-list>
</template>

<script>
    import TraceCode, { TraceableCodeTypeEnum } from '@/service/trace-code/service';

    export default {
        name: 'TraceableCodeList',
        props: {
            traceableCodeList: {
                type: Array,
                default: () => [],
            },
            goods: {
                type: Object,
                required: true,
            },
            height: {
                type: Number,
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            isNoTraceCodeGoods: Boolean,
        },
        data() {
            return {
                highLightCode: null,
                virtualListConfig: {
                    rowHeight: 37,// 36height+1border
                    bufferSize: 50,
                    bufferLoad: false,
                },
            };
        },
        computed: {
            enableVirtualList() {
                return this.traceableCodeList.length > 150;
            },
        },
        beforeDestroy() {
            this._timer = null;
            this.highLightCode = null;
        },
        methods: {
            handleDeleteItem(item, index) {
                this.$emit('deleteItem', item, index);
            },
            formatNo(item) {
                if (item.traceableCodeNoInfo?.type === TraceableCodeTypeEnum.NO_CODE) {
                    return `${item.drugIdentificationCode}`;
                }

                const {
                    start,end,
                } = TraceCode.formatTraceableCode(item);
                return `${start} ${end}`;
            },
            canDelete(item) {
                if (this.readonly) return false;
                if (this.isNoTraceCodeGoods) {
                    return item.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE;
                }
                return true;
            },
            createKey(e) {
                return e.keyId || e.id || e.no;
            },
            hoverItemFunc(item) {
                if (this.readonly) return false;
                return item.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE;
            },
            customItemClass(item) {
                return item.no === this.highLightCode ? 'highlight' : '';
            },
            triggerHighLight(code, duration = 1500) {
                this.$nextTick(() => {
                    this.highLightCode = code;
                    this.scrollTarget();
                    this._timer = setTimeout(() => {
                        this.highLightCode = null;
                    }, duration);
                });
            },
            scrollTarget() {
                if (!this.highLightCode) return;

                if (this.enableVirtualList) {
                    const item = this.traceableCodeList.find((e) => e.no === this.highLightCode);
                    this.$refs.listRef.scrollToElement(this.createKey(item));
                }

                this.$nextTick(() => {
                    this.$el.querySelector(`.trace-code-list-item[data-id="${this.highLightCode}"]`)?.scrollIntoView({
                        behavior: 'smooth',
                    });
                });
            },
        },
    };
</script>

<style lang="scss" scoped>
.item-wrapper {
    .attention-icon {
        display: block;
    }

    .delete-icon {
        display: none;
    }

    &:hover {
        .attention-icon {
            display: none;
        }

        .delete-icon {
            display: flex;
        }
    }
}
</style>
