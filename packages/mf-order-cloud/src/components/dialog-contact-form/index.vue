<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        :title="title"
        append-to-body
        size="small"
    >
        <abc-form
            ref="form"
            v-abc-loading="loading"
            label-position="left"
            :label-width="98"
            item-block
        >
            <abc-form-item label="发货人" required style="margin-bottom: 16px;">
                <abc-input
                    v-model="postData.name"
                    v-abc-focus-selected
                    :max-length="20"
                    trim
                    required
                    :width="220"
                >
                </abc-input>
            </abc-form-item>

            <abc-form-item
                v-abc-focus-selected
                label="发货人电话"
                required
                style="margin-bottom: 16px;"
            >
                <abc-input-mobile
                    v-model="postData.mobile"
                    :country-code="postData.mobileCountryCode"
                    :width="220"
                ></abc-input-mobile>
            </abc-form-item>

            <abc-form-item
                label="发货地址"
                required
                style="margin-bottom: 16px;"
                :validate-event="validateAddress"
            >
                <abc-address-selector v-model="addressInfo" :width="220"></abc-address-selector>
            </abc-form-item>

            <abc-form-item
                label=" "
                required
                :show-red-dot="false"
                style="align-items: flex-start; margin-bottom: 0;"
            >
                <abc-textarea
                    v-model="postData.address.detail"
                    :width="220"
                    :height="64"
                    trim
                    :maxlength="150"
                    placeholder="详细地址"
                >
                </abc-textarea>
            </abc-form-item>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button
                v-if="defaultData && defaultData.id"
                type="danger"
                :loading="deleteLoading"
                style="margin-right: auto;"
                @click="clickDeleteEvent"
            >
                删除
            </abc-button>

            <abc-button
                type="primary"
                :loading="btnLoading"
                @click="submitPrev"
            >
                确定
            </abc-button>
            <abc-button type="blank" @click="visible = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import {
        validateMobile,
        validateAddress,
    } from 'MfBase/validate';
    import { defaultCountryCode } from 'MfBase/country-codes';
    import ECOrderAPI from '../../api/order';

    export default {
        name: 'AddressEditor',
        props: {
            value: Boolean,
            defaultData: Object,
            onRefresh: Function,
        },
        data() {
            return {
                visible: false,
                loading: false,
                btnLoading: false,
                deleteLoading: false,
                postData: {
                    'address': {
                        'city': '',
                        'cityCode': '',
                        'detail': '',
                        'district': '',
                        'districtCode': '',
                        'province': '',
                        'provinceCode': '',
                    },
                    mobile: '',
                    name: '',
                    mobileCountryCode: defaultCountryCode,
                },
                addressInfo: {
                    addressCityId: '',
                    addressCityName: '',
                    addressProvinceId: '',
                    addressProvinceName: '',
                    addressDistrictId: '',
                    addressDistrictName: '',
                },
            };
        },
        computed: {
            title() {
                return this.defaultData?.id ? '编辑发货打印展示地址' : '添加发货打印展示地址';
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        created() {
            if (this.defaultData) {
                Object.assign(this.postData, this.defaultData);
                const {
                    city,
                    cityCode,
                    district,
                    districtCode,
                    province,
                    provinceCode,
                } = this.postData.address;
                Object.assign(this.addressInfo, {
                    addressCityId: cityCode,
                    addressCityName: city,
                    addressProvinceId: provinceCode,
                    addressProvinceName: province,
                    addressDistrictId: districtCode,
                    addressDistrictName: district,
                });
            }
        },
        methods: {
            validateMobile,
            validateAddress,
            transAddress() {
                const {
                    addressCityId,
                    addressCityName,
                    addressProvinceId,
                    addressProvinceName,
                    addressDistrictId,
                    addressDistrictName,
                } = this.addressInfo;
                Object.assign(this.postData.address, {
                    'city': addressCityName,
                    'cityCode': addressCityId,
                    'district': addressDistrictName,
                    'districtCode': addressDistrictId,
                    'province': addressProvinceName,
                    'provinceCode': addressProvinceId,
                });
            },

            submitPrev() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        this.transAddress();
                        this.defaultData?.id ? this.updateSubmit() : this.createSubmit();
                    }
                });
            },

            async createSubmit() {
                try {
                    this.btnLoading = true;
                    const res = await ECOrderAPI.createShipperContact(this.postData);
                    this.successHandler('新增', res);
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            async updateSubmit() {
                try {
                    this.btnLoading = true;
                    const res = await ECOrderAPI.updateShipperContact(this.postData);
                    this.successHandler('修改', res);
                } catch (e) {
                    this.btnLoading = false;
                }
            },

            successHandler(typeStr, current) {
                this.btnLoading = false;
                this.deleteLoading = false;
                this.visible = false;
                this.onRefresh && this.onRefresh(current);
                this.$Toast({
                    message: `${typeStr}成功`,
                    type: 'success',
                });
            },

            clickDeleteEvent() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除地址后将无法恢复，是否确定？',
                    onConfirm: async () => {
                        await this.deleteSubmit();
                    },
                });
            },
            async deleteSubmit() {
                try {
                    this.deleteLoading = true;
                    await ECOrderAPI.deleteShipperContact(this.postData.id);
                    this.successHandler('删除');
                } catch (e) {
                    console.error(e);
                } finally {
                    this.deleteLoading = false;
                }
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
