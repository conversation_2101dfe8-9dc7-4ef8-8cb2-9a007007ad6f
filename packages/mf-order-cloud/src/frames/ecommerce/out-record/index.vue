<template>
    <abc-layout preset="page-table" style="height: calc(100% - 48px);">
        <abc-layout-header>
            <abc-space>
                <abc-input
                    v-model="params.keyword"
                    placeholder="订单号/快递号/商品"
                    :icon="params.keyword ? 'cis-icon-cross_small' : ''"
                    clearable
                    :width="240"
                    @input="handleSearchOrder"
                    @clear="clearSearch"
                >
                    <abc-search-icon slot="prepend"></abc-search-icon>
                </abc-input>

                <abc-date-picker
                    v-model="datePickerValue"
                    :picker-options="pickerOptions"
                    type="daterange"
                    :width="256"
                    :clearable="false"
                    placeholder="选择日期范围"
                    @change="handleChangeDate"
                >
                </abc-date-picker>

                <abc-select
                    v-model="params.type"
                    :width="120"
                    clearable
                    placeholder="动作类型"
                    @change="initOffset"
                >
                    <abc-option :value="OutType.DISTRIBUTE" label="发药出库"></abc-option>
                    <abc-option :value="OutType.RETURN" label="退药入库"></abc-option>
                </abc-select>

                <abc-select
                    v-model="params.ecMallId"
                    :width="160"
                    clearable
                    show-empty
                    placeholder="网店"
                    @change="initOffset"
                >
                    <abc-option
                        v-for="item in mallList"
                        :key="item.ecMallId"
                        :label="item.mallName"
                        :value="item.ecMallId"
                    ></abc-option>
                </abc-select>
                <clinic-select
                    v-if="isChainAdmin"
                    v-model="params.clinicId"
                    :width="160"
                    placeholder="线下门店"
                    clearable
                    :show-all-clinic="false"
                    filter-current-clinic
                    @change="initOffset"
                ></clinic-select>
            </abc-space>
        </abc-layout-header>
        <abc-layout-content>
            <abc-table
                type="pro"
                class="ecommerce-table"
                :loading="loading"
                :show-content-empty="true"
                :tr-click-trigger-checked="false"
                :show-checked="false"
                :need-selected="false"
                :show-hover-tr-bg="false"
                :empty-content="'暂无数据'"
                :render-config="tableRenderConfig"
                :data-list="tableData"
            >
                <abc-flex
                    slot="topHeader"
                    justify="space-between"
                    align="center"
                >
                    <abc-space>
                        <abc-checkbox-button
                            v-model="goodsSkuBindStatus"
                            :statistics-number="summaryData.unBindGoodsCount"
                            type="number"
                            @change="initOffset"
                        >
                            发货时未绑定ABC商品
                        </abc-checkbox-button>
                        <abc-checkbox-button
                            v-model="goodsSkuStockWarnFlag"
                            :statistics-number="summaryData.stockShortageCount"
                            type="number"
                            @change="initOffset"
                        >
                            发货时库存不足
                        </abc-checkbox-button>
                    </abc-space>
                </abc-flex>

                <template #time="{ trData: row }">
                    <abc-table-cell style="height: 72px;">
                        <abc-flex v-if="row.status === OutRecordStatus.SHORTAGE || row.status === OutRecordStatus.UNBIND" vertical>
                            <abc-text theme="warning-light">
                                自动扣库失败
                            </abc-text>
                            <abc-text v-if="row.status === OutRecordStatus.UNBIND">
                                未绑定ABC商品
                            </abc-text>
                            <abc-text v-else>
                                发货时库存不足
                            </abc-text>
                        </abc-flex>
                        <span v-else>{{ formatDate(row.stockTime, 'YYYY-MM-DD HH:mm') }}</span>
                    </abc-table-cell>
                </template>
                <template #type="{ trData: row }">
                    <abc-table-cell style="height: 72px;">
                        <span>{{ row.type === OutType.DISTRIBUTE ? '发药出库' : '退药入库' }}</span>
                    </abc-table-cell>
                </template>
                <template #count="{ trData: row }">
                    <abc-table-cell style="height: 72px;">
                        <template v-if="row.bindGoodsInfo?.goodsId">
                            <span v-if="row.status === OutRecordStatus.SHORTAGE || row.status === OutRecordStatus.UNBIND">-</span>
                            <template v-else>
                                <span
                                    v-abc-title=" row.type === OutType.RETURN ? `+ ${row.bindGoodsInfo?.unitCount}${row.bindGoodsInfo?.unit ?? ''}` : `- ${row.bindGoodsInfo?.unitCount}${row.bindGoodsInfo?.unit ?? ''}`"
                                ></span>
                            </template>
                        </template>
                        <span v-else>-</span>
                    </abc-table-cell>
                </template>
                <template #batchNo="{ trData: row }">
                    <abc-table-cell style="height: 72px;">
                        <template v-if="row.bindGoodsInfo?.goodsId">
                            <abc-flex v-abc-title="formatBatchTitle(row.bindGoodsInfo?.batchInfos)" vertical>
                            </abc-flex>
                        </template>
                        <span v-else>-</span>
                    </abc-table-cell>
                </template>
                <template #order="{ trData: row }">
                    <table-cell-related-orders :order-info="row.ecOrder"></table-cell-related-orders>
                </template>

                <template #goods="{ trData: row }">
                    <abc-table-cell style="height: 72px;">
                        <abc-flex v-if="row.bindGoodsInfo?.goodsId" vertical>
                            <abc-text>{{ row.bindGoodsInfo.goodsName }}</abc-text>
                            <abc-text theme="gray">
                                {{
                                    row.bindGoodsInfo.goodsSpec || ''
                                }}
                                {{
                                    row.bindGoodsInfo.manufacturer || ''
                                }}
                            </abc-text>
                        </abc-flex>
                        <span v-else>-</span>
                    </abc-table-cell>
                </template>

                <template #sku="{ trData }">
                    <table-cell-goods :goods-list="[trData.extGoodsInfo]"></table-cell-goods>
                </template>
                <template #operation="{ trData }">
                    <table-cell-stock-operate
                        :item="trData"
                        :type="trData.type"
                        :record-id="trData.id"
                        :status="trData.status"
                        :bind-goods-info="trData.bindGoodsInfo"
                        style="height: 72px;"
                        @refresh-list="fetchData"
                    ></table-cell-stock-operate>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="paginationParams"
                :count="totalCount"
                :show-total-page="true"
                :page-sizes="[50, 100, 500, 1000]"
                show-size
                @current-change="pageChange"
                @size-change="sizeChange"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import {
        formatDate, prevDate,
    } from '@abc/utils-date';
    import TableConfig from './config';
    import StockRecordAPI from '@/api/stock-record';
    import ECAuthAPI from '@/api/auth';
    import DialogRemarkForm from '@/components/dialog-remark-form';
    import TableCellRelatedOrders from '@/components/table-cell-related-orders.vue';
    import TableCellGoods from '@/components/table-cell-goods.vue';
    import TableCellStockOperate from '@/components/table-cell-stock-operate.vue';
    import ClinicSelect from 'MfBase/clinic-select';
    import {
        OutRecordStatus, OutType, ECTypeEnum, EcShopTypeEnum,
    } from '@/utils/constants';
    import { mapGetters } from 'vuex';

    export default {
        name: 'OutRecordIndex',
        components: {
            ClinicSelect,
            TableCellGoods,
            TableCellStockOperate,
            TableCellRelatedOrders,
        },
        data() {
            const now = new Date();
            const today = formatDate(now);
            // 两天前
            const last7Day = formatDate(prevDate(now, 7));
            const last30Day = formatDate(prevDate(now, 30));
            const last90Day = formatDate(prevDate(now, 90));
            const last365Day = formatDate(prevDate(now, 365));

            return {
                loading: true,
                params: {
                    keyword: '',
                    limit: 100,
                    offset: 0,
                    type: '',
                    ecType: '',
                    ecMallId: '',
                    clinicId: '',
                    beginDate: last30Day,
                    endDate: today,
                    statusList: [],
                },
                goodsSkuStockWarnFlag: 0,
                goodsSkuBindStatus: 0,

                datePickerValue: [last30Day, today],
                pickerStartDate: '', // 获取开始选择时间
                pickerOptions: {
                    onPick: ({
                        minDate, maxDate,
                    }) => {
                        if (minDate) {
                            this.pickerStartDate = minDate.getTime();
                        }
                        if (maxDate) {
                            this.pickerStartDate = '';
                        }
                    },
                    disabledDate: (time) => {
                        const day31 = (365 - 1) * 24 * 3600 * 1000;
                        if (this.pickerStartDate !== '') {
                            let maxTime = this.pickerStartDate + day31;
                            const minTime = this.pickerStartDate - day31;
                            if (maxTime > new Date()) {
                                maxTime = new Date();
                            }
                            return time.getTime() > maxTime ||
                                time.getTime() < minTime ||
                                time.getTime() > Date.now();
                        }
                        return time.getTime() > Date.now();
                    },
                    shortcuts: [
                        {
                            text: '今天',
                            onClick(picker) {
                                picker([
                                    today,
                                    today,
                                    '今天',
                                ]);
                            },
                        },
                        {
                            text: '近7天',
                            onClick(picker) {
                                picker([
                                    last7Day,
                                    today,
                                    '近7天',
                                ]);
                            },
                        },
                        {
                            text: '近30天',
                            onClick(picker) {
                                picker([
                                    last30Day,
                                    today,
                                    '近30天',
                                ]);
                            },
                        },
                        {
                            text: '近90天',
                            onClick(picker) {
                                picker([
                                    last90Day,
                                    today,
                                    '近90天',
                                ]);
                            },
                        },
                        {
                            text: '近1年',
                            onClick(picker) {
                                picker([
                                    last365Day,
                                    today,
                                    '近1年',
                                ]);
                            },
                        },
                    ],
                },
                totalCount: 0,
                mallList: [],
                tableData: [],
                summaryData: {},
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin']),
            OutType() {
                return OutType;
            },
            OutRecordStatus() {
                return OutRecordStatus;
            },
            tableRenderConfig() {
                return TableConfig.getRenderConfig();
            },
            paginationParams() {
                const {
                    limit, offset,
                } = this.params;
                const pageIndex = Math.ceil(offset / limit);
                return {
                    pageIndex,
                    pageSize: limit,
                };
            },
        },
        created() {
            // 暂时只有拼多多
            this.params.ecType = ECTypeEnum.PDD;
            this.getMallList();
            this.initOffset();

            this.$abcEventBus.$on('refresh-ec-goods', () => this.fetchData(), this);
        },
        methods: {
            formatDate,
            formatBatchTitle(batchInfos = []) {
                return batchInfos.map((item) => `${item.batchNo} ×1`).join('\n');
            },
            createParams() {
                return {
                    ...this.params,
                    statusList: [
                        this.goodsSkuBindStatus ? 20 : null,
                        this.goodsSkuStockWarnFlag ? 30 : null,
                    ].filter((item) => item).join(','),
                };
            },
            clearSearch() {
                this.params.keyword = '';
                this.initOffset();
            },
            initOffset() {
                this.params.offset = 0;
                this.fetchData();
            },
            async fetchData() {
                this.loading = true;
                try {
                    const res = await StockRecordAPI.fetchOutRecordList(this.createParams());
                    this.tableData = res.rows || [];
                    this.totalCount = res.total || 0;
                    this.summaryData = res?.summary || {
                        unBindGoodsCount: 0,
                        stockShortageCount: 0,
                    };
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            async getMallList() {
                try {
                    const res = await ECAuthAPI.fetchBindAuthList({
                        offset: 0,
                        limit: 1000,
                    });
                    this.mallList = (res?.rows || []).filter((item) => item.shopType === EcShopTypeEnum.B2C);

                } catch (err) {
                    console.error(err);
                }
            },
            handleChangeDate() {
                this.pickerStartDate = '';
                this.params.beginDate = this.datePickerValue[0];
                this.params.endDate = this.datePickerValue[1];
                this.fetchData();
            },
            handleMounted(data) {
                this.params.limit = data.paginationLimit;
                this.initOffset();
            },
            pageChange(pageIndex) {
                this.params.offset = (pageIndex - 1) * this.params.limit;
                this.fetchData();
            },
            sizeChange(pageSize) {
                this.params.limit = pageSize;
                this.fetchData();
            },
            handleSearchOrder() {
                this.fetchData();
            },
            handleBatchChange(val) {
                if (this.checkedList.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '请勾选订单',
                    });
                    return;
                }
                if (val === 'lock') {
                    this.orderLockSubmit(this.checkedList, 1);
                } else if (val === 'batchAdd') {
                    new DialogRemarkForm({
                        orderList: this.checkedList,
                        onRefresh: this.fetchEcOrderList,
                    }).generateDialogAsync();
                } else if (val === 'batchDelete') {
                    this.batchDeleteHandler();
                } else if (val === 'batchMerge') {
                    this.batchMergeHandler();
                } else if (val === 'batchSplit') {
                    this.batchSplitHandler();
                }
            },
            handleClickTag(val, key) {
                this.params[key] = val;
                this.fetchData();
            },
        },
    };
</script>
