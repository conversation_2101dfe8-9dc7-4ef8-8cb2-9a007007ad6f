<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        content-styles="width:480px;min-height: 320px"
        title="打印快递单"
        class="express-print-dialog-wrapper"
        @close="$emit('close')"
    >
        <abc-layout class="dialog-content clearfix">
            <abc-form ref="abcForm" label-position="left" :label-width="114">
                <div v-for="item in waybillTask" :key="item.ecType">
                    <h5>{{ ECTypeText[item.ecType] || '' }} （共{{ item.orderList.length }}订单）</h5>
                    <abc-form-item
                        label="快递面单模版"
                        required
                        class="express-print-form-item"
                        :custom-label-style="{
                            paddingTop: '10px', alignSelf: 'flex-start'
                        }"
                    >
                        <way-bill-selector
                            v-model="item.templateInfo"
                            :waybill-templates="getTemplateViews(item.ecType)"
                            @router-change="closed = true"
                        ></way-bill-selector>
                    </abc-form-item>
                    <abc-form-item
                        label="快递单打印机"
                        class="express-print-form-item"
                        required
                    >
                        <abc-select
                            v-model="item.printConfig.expressPrinter"
                            :width="318"
                            placeholder="请选择"
                        >
                            <abc-option
                                v-for="(it) in printerList"
                                :key="it.name"
                                :value="it.name"
                                :label="it.name"
                            >
                                <span>{{ it.name }}</span>
                            </abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item label="同时打印发货单" class="express-print-form-item" :custom-label-style="{ alignSelf: 'flex-start' }">
                        <abc-flex vertical>
                            <abc-checkbox v-model="item.printConfig.samePrint" @change="handleChangeSamePrint(item)">
                                开启
                            </abc-checkbox>
                            <abc-checkbox v-if="item.printConfig.samePrint" v-model="item.printConfig.alternatelyPrint" style="margin-top: 8px;">
                                用快递面单打印机，交替打印快递面单、发货单
                            </abc-checkbox>
                        </abc-flex>
                    </abc-form-item>
                    <abc-form-item
                        v-if="item.printConfig.samePrint && !item.printConfig.alternatelyPrint"
                        label="发货单打印机"
                        class="express-print-form-item"
                        required
                    >
                        <abc-space is-compact compact-block>
                            <abc-select v-model="item.printConfig.shipmentTemplate" :width="120" placeholder="请选择">
                                <abc-option label="A4横版" value="A4"></abc-option>
                                <abc-option label="A5横版" value="A5"></abc-option>
                                <abc-option label="热敏(76*130)" value="76"></abc-option>
                            </abc-select>
                            <abc-select
                                v-model="item.printConfig.shipmentPrinter"
                                :width="198"
                                placeholder="请选择"
                            >
                                <abc-option
                                    v-for="(it) in printerList"
                                    :key="it.name"
                                    :value="it.name"
                                    :label="it.name"
                                >
                                    <span>{{ it.name }}</span>
                                </abc-option>
                            </abc-select>
                        </abc-space>
                    </abc-form-item>
                </div>
            </abc-form>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleClickConfirm">
                开始打印
            </abc-button>
            <abc-button @click="handleClickPrintShipment">
                打印并发货
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import ECWayBillAPI from '@/api/way-bill';
    import WayBillSelector from '../way-bill-selector.vue';
    import {
        ECTypeEnum, ECTypeText,
    } from '../../utils/constants';
    import DialogPrintManager from '../dialog-print-manager';
    import ECOrderAPI from '@/api/order';
    import ExpressPrintManage from '../../utils/print-manager/manage.js';

    export default {
        name: 'DialogExpressPrint',
        components: {
            WayBillSelector,
        },
        props: {
            value: Boolean,
            waybillType: Number,
            orderList: Array,
            onFinish: Function,
            shipPrintConfig: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                ECTypeText,
                visible: false,
                closed: false,
                contentLoading: false,
                waybillTask: [],
                printerList: [],
                ecWaybillTemplates: [],
            };
        },
        computed: {
            printTask() {
                const printTask = [];
                this.waybillTask.forEach((item) => {
                    item.orderList.forEach((it) => {
                        printTask.push({
                            ecType: item.ecType,
                            waybillType: this.waybillType,
                            printConfig: item.printConfig,
                            templateInfo: item.templateInfo,
                            orderInfo: it,
                        });
                    });
                });
                return printTask;
            },
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            this.fetchEcWaybillTemplate();
            this.getPrinters();
        },
        methods: {
            initWaybillTask() {
                this.waybillTask = [];
                let _printConfig = localStorage.getItem('last_selected_waybill_print_config');
                let _lastTemplateObj = localStorage.getItem('last_selected_waybill_template_info');
                if (_printConfig) {
                    _printConfig = JSON.parse(_printConfig);
                }
                if (_lastTemplateObj) {
                    _lastTemplateObj = JSON.parse(_lastTemplateObj);
                }
                this.orderList.forEach((item) => {
                    const res = this.waybillTask.find((it) => it.ecType === item.ecType);
                    if (res) {
                        res.orderList.push(item);
                    } else {

                        let lastTemplate = {};
                        if (_lastTemplateObj) {
                            const templateList = this.getTemplateViews(item.ecType);
                            lastTemplate = templateList.find((it) => it.id === _lastTemplateObj[item.ecType]);
                        }

                        this.waybillTask.push({
                            ecType: item.ecType,
                            orderList: [item],
                            templateInfo: lastTemplate || {},
                            printConfig: _printConfig ? _printConfig[item.ecType] : {
                                expressPrinter: '',
                                samePrint: false,
                                alternatelyPrint: false,
                                shipmentPrinter: '',
                                shipmentTemplate: 'A4',
                            },
                        });
                    }
                });
            },
            getTemplateViews(ecType) {
                const res = this.ecWaybillTemplates.find((item) => item.ecType === ecType);
                return res?.templateViews || [];
            },
            getOrderByEcType(ecType) {
                return this.orderList.filter((order) => {
                    return order.ecType === ecType;
                });
            },
            async getPrinters() {
                this.printerList = await ExpressPrintManage.getPrinterList() || [];
            },
            handleChangeSamePrint(item) {
                if (!item.samePrint) {
                    item.printConfig.alternatelyPrint = false;
                    item.printConfig.shipmentPrinter = '';
                }
            },
            async fetchEcWaybillTemplate() {
                const res = await ECWayBillAPI.fetchEcWaybillTemplate();
                this.ecWaybillTemplates = res.rows || [];

                this.initWaybillTask();
            },

            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            handleClickConfirm() {
                this.$refs.abcForm.validate((valid) => {
                    if (valid) {
                        this.openPrintManager();

                    }
                });
            },
            async openPrintManager() {
                const status = await this.checkPrintStatus();
                if (!status) return;
                await new DialogPrintManager({
                    printTask: this.printTask,
                    shipPrintConfig: this.shipPrintConfig,
                    onProcess: this.onPrintProcess,
                    onFinish: () => {
                        this.closed = true;
                        this.onFinish && this.onFinish();
                    },
                }).generateDialogAsync();

                const _printConfig = {};
                const _lastTemplateObj = {};
                this.waybillTask.forEach((it) => {
                    _printConfig[it.ecType] = it.printConfig;
                    _lastTemplateObj[it.ecType] = it.templateInfo.id;
                });
                localStorage.setItem('last_selected_waybill_print_config', JSON.stringify(_printConfig));
                localStorage.setItem('last_selected_waybill_template_info', JSON.stringify(_lastTemplateObj));
            },
            async checkPrintStatus() {
                try {
                    await ExpressPrintManage.checkPrintStatus(this.waybillTask);
                    return true;
                } catch (e) {
                    console.error(e);
                    if (e.__ecType__ === ECTypeEnum.PDD) {
                        this.$alert({
                            type: 'warn',
                            title: '未连接到打印组件',
                            content: [
                                '请检查是否启动拼多多打印组件',
                                '或者没有下载？<a style="color: #005ed9;text-decoration: underline" href="http://meta.pinduoduo.com/api/one/app/v1/lateststable?appId=com.xunmeng.pddprint&platform=windows&subType=main">点击下载</a>',
                            ],
                        });
                    }
                    return false;
                }
            },
            async handleClickPrintShipment() {
                this.$refs.abcForm.validate((valid) => {
                    if (valid) {
                        this.$confirm({
                            type: 'warn',
                            title: '打印并完成发货确认',
                            content: '确认发货完成后，订单SKU绑定的ABC商城库存将自动下账并销售出库。是否确定？',
                            onConfirm: () => {
                                new DialogPrintManager({
                                    printTask: this.printTask,
                                    onProcess: this.onProcess,
                                    onFinish: () => {
                                        this.closed = true;
                                        this.onFinish && this.onFinish();
                                    },
                                }).generateDialogAsync();
                            },
                        });
                    }
                });

            },
            onPrintProcess(res) {
                this.updatePrintStatus(res.orderInfo);
            },
            async onProcess(res) {
                await this.updatePrintStatus(res.orderInfo);
                await this.batchShipOrder(res.orderInfo);
            },
            async batchShipOrder(order) {
                try {
                    const {
                        id,
                    } = order;
                    const res = await ECOrderAPI.batchShipOrder({
                        orderIds: [id],
                    });
                    if (res.errorOrders && res.errorOrders) {
                        const o = res.errorOrders[0];
                        this.$Toast({
                            type: 'warn',
                            title: '订单同时发货失败',
                            message: `${o.orderId}-${o.errorMsg}`,
                        });
                    }
                } catch (e) {
                    console.error(e);
                }
            },
            async updatePrintStatus(order) {
                try {
                    const {
                        id,
                        waybillCode,
                    } = order;
                    await ECOrderAPI.updatePrintStatus({
                        statusList: [
                            {
                                orderId: id,
                                waybillCode,
                                printStatus: 1,
                            },
                        ],
                    });
                } catch (e) {
                    console.error(e);
                }

            },

        },
    };
</script>
<style lang="scss">
    .express-print-dialog-wrapper {
        .dialog-content {
            h5 {
                margin-bottom: 16px;
                font-size: 14px;
                font-weight: bold;
                line-height: 22px; /* 157.143% */
                color: var(--abc-color-T1, #000000);
            }

            .express-print-form-item {
                margin-right: 0;
            }
        }
    }
</style>
