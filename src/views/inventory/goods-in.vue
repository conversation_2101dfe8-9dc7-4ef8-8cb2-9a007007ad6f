<template>
    <abc-layout preset="page-table">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-select
                        key="fetchParamsFromType"
                        v-model="fetchParams.fromType"
                        :width="120"
                        @change="handleFromTypeChange"
                    >
                        <abc-option :label="'采购入库'" :value="GOODS_IN_ORDER_FROM_TYPE.GOODS_IN"></abc-option>
                        <abc-option :label="'初始化入库'" :value="GOODS_IN_ORDER_FROM_TYPE.INIT_GOODS_IN"></abc-option>
                    </abc-select>

                    <abc-select
                        key="fetchParamsStatus"
                        v-model="status"
                        :width="120"
                        custom-class="status-options-select"
                        @change="initOffset"
                        @close="showTotalCount = true"
                        @open="showTotalCount = false"
                    >
                        <abc-option
                            v-if="isChain"
                            :value="99998"
                            class="title-option"
                            disabled
                        >
                            入库单状态
                        </abc-option>
                        <abc-option :statistics-number="todoCount" :value="''" label="全部状态">
                            全部状态
                        </abc-option>

                        <template v-if="isChain">
                            <abc-option :statistics-number="panelData?.inTodoCount ? panelData.inTodoCount : undefined" :value="GOODS_IN_STATUS.CONFIRM" label="待确认">
                                待确认
                            </abc-option>
                            <abc-option :statistics-number="panelData?.reviewTodoCount ? panelData.reviewTodoCount : undefined" :value="GOODS_IN_STATUS.REVIEW" label="待审核">
                                待审核
                            </abc-option>
                            <abc-option :value="GOODS_IN_STATUS.GOODS_IN" label="已入库">
                                已入库
                            </abc-option>
                            <abc-option :value="GOODS_IN_STATUS.REFUSE" label="已驳回">
                                已驳回
                            </abc-option>
                            <abc-option :value="GOODS_IN_STATUS.WITH_DRAW" label="已撤回">
                                已撤回
                            </abc-option>
                        </template>

                        <template v-if="isShowSettlement">
                            <abc-option :value="99999" class="title-option" disabled>
                                结算状态
                            </abc-option>
                            <abc-option :value="SettlementStatus.unSettled" label="未结算">
                                未结算
                            </abc-option>
                            <abc-option :value="SettlementStatus.review" label="待审核">
                                待审核
                            </abc-option>
                            <abc-option :value="SettlementStatus.settled" label="已结算">
                                已结算
                            </abc-option>
                        </template>
                    </abc-select>
                    <template v-if="isChain">
                        <abc-select
                            key="fetchParamsDateField"
                            v-model="fetchParams.dateField"
                            :width="90"
                            @change="initOffset"
                        >
                            <abc-option :value="'createdDate'" label="创建日期"></abc-option>
                            <abc-option :value="'inDate'" label="完成日期"></abc-option>
                        </abc-select>
                    </template>

                    <abc-date-picker
                        v-model="selectDate"
                        :picker-options="pickerOptions"
                        placeholder="选择日期范围"
                        type="daterange"
                        value-format="YYYY-MM-DD"
                        @change="changeDate"
                    >
                    </abc-date-picker>

                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="selectedClinic"
                        :width="100"
                        @change="filterClinicGoodsIn"
                    ></clinic-select>

                    <abc-select
                        key="fetchParamsType"
                        v-model="fetchParams.type"
                        :max-width="200"
                        :width="100"
                        custom-class="supplierWrapper"
                        @change="initOffset"
                    >
                        <abc-option :label="'全部类型'" value=""></abc-option>
                        <abc-option 
                            v-for="option in typeOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                        ></abc-option>
                    </abc-select>

                    <abc-select
                        v-model="selectedSupplierId"
                        :fetch-suggestions="fetchSuggestions"
                        :max-width="240"
                        :width="120"
                        custom-class="supplierWrapper"
                        with-search
                        @change="filterSupplierGoodsIn"
                    >
                        <abc-option :value="''" label="全部供应商"></abc-option>
                        <abc-option
                            v-for="it in supplierOptions"
                            :key="`${it.id}`"
                            :label="it.name"
                            :value="it.id"
                        ></abc-option>
                    </abc-select>

                    <abc-select
                        v-if="pharmacyNo === null && multiPharmacyCanUse"
                        v-model="selectedPharmacyNo"
                        :max-width="200"
                        :width="100"
                        custom-class="supplierWrapper"
                        @change="filterPharmacyGoodsIn"
                    >
                        <abc-option
                            v-for="it in currentPharmacyList"
                            :key="it.no"
                            :label="it.name"
                            :value="it.no"
                        ></abc-option>
                    </abc-select>

                    <goods-auto-complete
                        :auto-focus-first="false"
                        :clear-search-key="false"
                        :enable-barcode-detector="!isDialogShowing"
                        :only-stock="false"
                        :pharmacy-no="pharmacyNo"
                        :placeholder="_searchHint"
                        :search.sync="searchKey"
                        :width="190"
                        :with-stock="false"
                        class="abc-autocomplete-search"
                        enable-local-search
                        is-close-validate
                        @enter="selectGoods"
                        @searchGoods="searchList"
                        @selectGoods="selectGoods"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                        <div slot="append" class="search-icon" @click="clearSearch">
                            <i v-if="searchKey" class="iconfont cis-icon-cross_small"></i>
                        </div>
                    </goods-auto-complete>
                </abc-space>

                <abc-space>
                    <abc-check-access v-if="!hiddenAddOrder">
                        <abc-button
                            v-if="showGoodsInButton"
                            theme="success"
                            icon="s-b-add-line-medium"
                            data-cy="inventory-goods-in-add-btn"
                            @click="addOrder"
                        >
                            采购入库
                        </abc-button>
                    </abc-check-access>
                    <abc-check-access v-if="showReturnApplicationButton">
                        <abc-button
                            variant="ghost"
                            data-cy="inventory-goods-in-return-btn"
                            @click="handleOpenReturnApplicationDialog"
                        >
                            退货申请
                        </abc-button>
                    </abc-check-access>
                    <abc-check-access v-if="showReturnButton">
                        <abc-button
                            variant="ghost"
                            data-cy="inventory-goods-in-return-btn"
                            @click="returnOrder"
                        >
                            退货
                        </abc-button>
                    </abc-check-access>
                    <abc-check-access>
                        <abc-button
                            class="export-btn"
                            icon="n-upload-line"
                            variant="ghost"
                            :loading="exportBtnLoading"
                            data-cy="inventory-goods-in-export-btn"
                            @click="exportExcel"
                        >
                            导出
                        </abc-button>
                    </abc-check-access>
                </abc-space>
            </abc-flex>
        </abc-layout-header>

        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="goods-list-table"
                :class="goodsTableVisible ? 'with-goods-table' : ''"
                :data-list="goodsTableVisible ? goodsTableData : dataList"
                :empty-opt="{ label: '暂无入库记录' }"
                :loading="loading"
                :render-config="renderConfig"
                :custom-tr-key="createKey"
                @handleClickTr="
                    (item) => {
                        return item.status === GOODS_IN_STATUS.DRAFT ? openDraft(item) : showOne(item);
                    }
                "
            >
                <!--			入库单号-->
                <template #orderNo="{ trData: item }">
                    <abc-table-cell class="ellipsis" style="color: var(--abc-color-C2);">
                        <template v-if="item.status === GOODS_IN_STATUS.DRAFT">
                            <span
                                v-abc-title.ellipsis="`最后修改：${ item.lastModifiedDate ? formatCacheTime(item.lastModifiedDate) : '-'}`"
                            ></span>
                        </template>
                        <template v-else>
                            <span v-abc-title="item && item.orderNo"></span>
                            <abc-tooltip-info v-if="item.comment.length" style="margin-left: 4px;">
                                <div>
                                    {{ item.comment[item.comment.length - 1].content }}
                                </div>
                            </abc-tooltip-info>
                        </template>
                    </abc-table-cell>
                </template>

                <!--状态-->
                <template #status="{ trData: item }">
                    <abc-table-cell>
                        <abc-tag-v2 :min-width="56" v-bind="statusConfig(item)">
                            {{ getStatusName(item) }}
                        </abc-tag-v2>
                    </abc-table-cell>
                </template>

                <template #settlementStatus="{ trData: item }">
                    <abc-table-cell>
                        <template v-if="!showSettStatus(item)">
                            -
                        </template>
                        <template v-else>
                            <span v-if="!item.settlementOrder || item.settlementOrder?.status === 6">未结算</span>
                            <span v-if="item.settlementOrder?.status === 0">待审核</span>
                            <span v-if="(item.settlementOrder?.status === 1)">已结算</span>
                        </template>
                    </abc-table-cell>
                </template>

                <!--			门店-->
                <template #clinicName="{ trData: item }">
                    <abc-table-cell>
                        <span v-if="item.status === GOODS_IN_STATUS.DRAFT">{{
                            item && item.toOrganName
                        }}</span>
                        <span v-else :title="item.toOrgan | clinicName">{{ item.toOrgan | clinicName }}</span>
                    </abc-table-cell>
                </template>

                <!--			库房-->
                <template #pharmacy="{ trData: item }">
                    <abc-table-cell>
                        <span :title="item && item.pharmacy && item.pharmacy.name">{{
                            item && item.pharmacy && item.pharmacy.name
                        }}</span>
                    </abc-table-cell>
                </template>

                <!--			类型-->
                <template #type="{ trData: item }">
                    <abc-table-cell>
                        <abc-text v-if="item.type === GOODS_IN_ORDER_TYPE.CORRECT_IN_ORDER_ADD_MORE" theme="black" title="采购入库-修正">
                            采购入库-修正
                        </abc-text>

                        <abc-text v-else-if="item.type === GOODS_IN_ORDER_TYPE.CORRECT_RETURN_OUT" theme="black" title="退货出库-修正">
                            退货出库-修正
                        </abc-text>

                        <abc-text v-else-if="GoodsInInitTypeEnum.includes(item.type)" theme="black" title="初始化入库">
                            初始化入库
                        </abc-text>

                        <abc-text v-else-if="GoodsReturnInitTypeEnum.includes(item.type)" theme="black" title="初始化退货">
                            初始化退货
                        </abc-text>

                        <span v-else :title="item.type === GOODS_IN_ORDER_TYPE.RETURN_OUT ? '退货出库' : '采购入库'">{{
                            item.type === GOODS_IN_ORDER_TYPE.RETURN_OUT ? '退货出库' : '采购入库'
                        }}</span>
                    </abc-table-cell>
                </template>

                <!--			供应商-->
                <template #supplier="{ trData: item }">
                    <abc-table-cell>
                        <span
                            v-if="item.status === GOODS_IN_STATUS.DRAFT"
                            :title="(item.supplier && item.supplier.name) || item.supplier"
                        >{{
                            (item.supplier && item.supplier.name) || item.supplier
                        }}</span>
                        <span v-else :title="item.supplier">{{ item.supplier }}</span>
                    </abc-table-cell>
                </template>

                <template #include="{ trData: item }">
                    <abc-table-cell>
                        <render-props :render="renderInclude" :value="item"></render-props>
                    </abc-table-cell>
                </template>

                <!--			品种-->
                <template #kindCount="{ trData: { kindCount } }">
                    <abc-table-cell><span :title="kindCount">{{ kindCount }}</span></abc-table-cell>
                </template>

                <!--			数量-->
                <template #count="{ trData: { sum } }">
                    <abc-table-cell><span :title="sum ">{{ sum }}</span></abc-table-cell>
                </template>

                <template #amount="{ trData: item }">
                    <abc-table-cell>
                        <inventory-order-fixed-hover-popover :order-type="getOrderType(item.type)" :order-item="item" :order-id="item.id">
                            <abc-text :theme="getAmountTheme(item)" :title="item.amount | formatMoney">
                                {{ item.amount | formatMoney }}
                            </abc-text>
                        </inventory-order-fixed-hover-popover>
                    </abc-table-cell>
                </template>
                <template #amountExcludingTax="{ trData: item }">
                    <abc-table-cell>
                        <inventory-order-fixed-hover-popover :order-type="getOrderType(item.type)" :order-item="item" :order-id="item.id">
                            <abc-text :theme="getAmountTheme(item)" :title="item.amountExcludingTax | formatMoney">
                                {{
                                    item.amountExcludingTax | formatMoney
                                }}
                            </abc-text>
                        </inventory-order-fixed-hover-popover>
                    </abc-table-cell>
                </template>

                <template #createdUser="{ trData: { createdUser } }">
                    <abc-table-cell>
                        <span :title="(createdUser && createdUser.name) || ''">{{
                            (createdUser && createdUser.name) || ''
                        }}</span>
                    </abc-table-cell>
                </template>

                <template #createdDate="{ trData: item }">
                    <abc-table-cell>
                        <span
                            v-if="item.status !== GOODS_IN_STATUS.DRAFT"
                            :title="item.createdDate | parseTime('y-m-d')"
                        >{{
                            item.createdDate | parseTime('y-m-d')
                        }}</span>
                        <span v-else>-</span>
                    </abc-table-cell>
                </template>
                <template #inDate="{ trData: { inDate } }">
                    <abc-table-cell>
                        <span v-if="inDate" :title="inDate | parseTime('y-m-d')">{{ inDate | parseTime('y-m-d') }}</span>
                        <span v-else>-</span>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :count="panelData.count"
                :pagination-params="pageParams"
                :show-total-page="false"
                @current-change="pageTo"
            >
                <ul v-if="panelData.count > 0" slot="tipsContent">
                    <li>
                        共 <span>{{ panelData.count }}</span> 条单据，
                    </li>
                    <li>
                        数量 <span>{{ panelData.stat.count }}</span>，
                    </li>
                    <li>
                        含税金额 <span>{{ panelData.stat.amount | formatMoney(false) }}</span>
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>


        <!--草稿和新建都走这个-->
        <order-form
            v-if="showForm"
            v-model="showForm"
            :draft-id="draftId"
            :goods-id="searchGoods.id"
            :order-id="orderId"
            :pharmacy-no="pharmacyNo"
            :pharmacy-type="pharmacyType"
            @close="closeDialogHandler"
            @refresh="refreshHandler"
        ></order-form>

        <!--提交后的都走这里-->
        <order-detail
            v-if="showDetail"
            v-model="showDetail"
            :goods-id="searchGoods.id"
            :order-id="orderId"
            :pharmacy-type="pharmacyType"
            @close="closeDialogHandler"
            @reInStock="reInStockHandler"
            @refresh="refreshHandler"
            @return="returnStockHandler"
        ></order-detail>

        <order-return
            v-if="showOutForm"
            :draft-id="draftId"
            :goods-id="searchGoods.id"
            :is-resubmit="isResubmit"
            :order-id="orderId"
            :pharmacy-no="pharmacyNo"
            :quick-return-order-id="quickReturnOrderId"
            :visible.sync="showOutForm"
            @close="closeDialogHandler"
            @refresh="refreshHandler"
        ></order-return>

        <!--出库单详情-->
        <order-out-detail
            v-if="showOutDetail"
            :goods-id="searchGoods.id"
            :order-id="orderId"
            :visible.sync="showOutDetail"
            @close="closeDialogHandler"
            @refresh="refreshHandler"
            @resubmit="resubmitHandler"
        ></order-out-detail>


        <goods-in-type-dialog
            v-if="showTypeDialog"
            v-model="showTypeDialog"
            :pharmacy-list="localPharmacyUserList"
            :pharmacy-no="pharmacyNo"
            :pharmacy-type="pharmacyType"
            @close="closeDialogHandler"
            @refresh="refreshHandler"
        ></goods-in-type-dialog>
    </abc-layout>
</template>
<script type="text/ecmascript-6">
// 入库
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { PharmacyTypeEnum } from '@abc/constants';
    import {
        isNull,
        paddingMoney,
        parseTime,
    } from '@/utils';
    import Draft from './mixins/draft';
    import PickerOptions from 'views/common/pickerOptions';
    import GoodsInApi from 'api/goods/stock-in';
    const GoodsAutoComplete = () => import('./common/goods-auto-complete');
    import {
        goodsSpec, isChineseMedicine,
    } from '@/filters';
    import clone from '../../utils/clone';
    import {
        CHECK_IN_SUPPLIER_ID,
        CorrectOrderTypeEnum,
        GOODS_IN_ORDER_TYPE,
        GOODS_IN_ORDER_FROM_TYPE,
        GOODS_IN_STATUS,
        GOODS_OUT_STATUS,
        UNSELECTED_SUPPLIER_INFO,
        GoodsInInitTypeEnum,
        GoodsReturnInitTypeEnum,
    } from 'views/inventory/constant';

    import ClinicSelect from 'views/layout/clinic-select/clinic-select';

    import OrderReturn from 'views/inventory/goods-out/return.vue';
    import OrderOutDetail from 'views/inventory/goods-in/return-detail.vue';

    const OrderDetail = () => import('views/inventory/goods-in/detail.vue');
    const OrderForm = () => import('views/inventory/goods-in/form.vue');

    import RenderProps from '@/components/render-props/index.vue';
    import GoodsInTypeDialog from './goods-in/components/goods-in-type-dialog';

    import {
        filterComment,
    } from 'views/inventory/goods-utils';
    import { debounce } from 'utils/lodash.js';
    import AbcFormDialog from 'views/inventory/goods-in/form-dialog.js';
    import AbcAccess from '@/access/utils.js';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';
    import { TagHelper } from 'utils/tag-helper';
    import AbcCorrectionOrderDialog from 'views/inventory/composite-components/correction-order-dialog';
    import useReviseOrder from 'views/inventory/hooks/useReviseOrder';
    import AbcReturnGoodsApplicationDialog from 'views/inventory/components/return-goods-application-dialog';
    const InventoryOrderFixedHoverPopover = () => import('views/inventory/components/inventory-order-fixed-hover-popover.vue');

    const SettlementStatus = {
        unSettled: -1, // 未结算
        review: -2, // 待审核
        settled: -3, // 已结算
        partial: -4, // 部分结算
    };

    function defaultSelectDate() {
        return {
            begDate: '',
            endDate: '',
        };
    }

    export default {
        name: 'GoodsIn',
        components: {
            OrderForm,
            OrderDetail,
            OrderReturn,
            OrderOutDetail,
            GoodsAutoComplete,
            ClinicSelect,
            GoodsInTypeDialog,
            RenderProps,
            InventoryOrderFixedHoverPopover,
        },

        mixins: [
            PickerOptions,
            Draft,
        ],

        props: {
            pharmacyNo: Number,
            pharmacyType: Number,
        },

        setup(props) {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('采购入库列表');

            const {
                currentSupplierList,
                initSupplierList,
                fetchSuggestions,
            } = useSearchSupplier({
                pharmacyType: props.pharmacyType,
                excludeInitSupplier: true,
            });

            const {
                isReviseTotalPrice,
            } = useReviseOrder();

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                currentSupplierList,
                initSupplierList,
                fetchSuggestions,

                isReviseTotalPrice,
            };
        },

        data() {
            const {
                begDate, endDate,
            } = defaultSelectDate();
            return {
                CorrectOrderTypeEnum,
                GOODS_IN_ORDER_TYPE,
                GOODS_IN_ORDER_FROM_TYPE,
                GOODS_IN_STATUS,
                CHECK_IN_SUPPLIER_ID,
                GoodsInInitTypeEnum,
                GoodsReturnInitTypeEnum,
                SettlementStatus,
                selectDate: '',
                goodsSelected: {},
                loading: true,
                statusText: '全部状态',
                fetchParams: {
                    begDate,
                    endDate,
                    keyword: '',
                    clinicId: '',
                    supplierId: '',
                    offset: 0,
                    limit: 10,
                    withGoodsId: '',
                    fromType: GOODS_IN_ORDER_FROM_TYPE.GOODS_IN, // 0:采购入库 10:初始化入库
                    type: '', // 0:采购入库 10:退货出库
                    status: '', // 单据状态 0：待审核 1：已通过 9：已驳回  不传：全部状态
                    dateField: 'createdDate', // createdDate申请日期  inDate 入库日期
                    settlementStatus: '', // 1 已结算  0 未结算 ''不筛选
                    pharmacyType: this.pharmacyType,
                    pharmacyNo: this.pharmacyNo,// 为 null表示汇总本地库房
                },
                isResubmit: false,
                status: '',
                panelData: {
                    rows: [],
                    count: 0,
                    stat: {
                        count: 0,
                        amount: 0,
                    },
                    inTodoCount: 0,
                    reviewTodoCount: 0,
                },
                detailData: {},

                showForm: false,
                showDetail: false,
                showOutForm: false,
                showOutDetail: false,
                orderId: '',
                draftId: '',
                curMallOrderId: '',
                quickReturnOrderId: '',

                selectedClinic: '', // 选中的门店
                selectedSupplierId: '',
                selectedPharmacyNo: null,// 全部库房值为 null

                isUpdate: -1,

                dataList: [],

                searchKey: '',
                searchGoods: '',
                goodsTableData: [], // 包含搜索药品的库存列表的 入库单
                goodsTableVisible: false,
                selectedGoodsClinicName: '',
                selectedGoodsSupplier: '',
                showTotalCount: true,

                showTypeDialog: false,
                exportBtnLoading: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'subClinics',
                'userInfo',
                'inventoryTodo',
                'isChain',
                'isChainSubStore',
                'isChainAdmin',
                'multiPharmacyCanUse',
                'localPharmacyList',
                'localPharmacyUserList',
                'currentPharmacyModulePermission',
                'goodsConfig',
                'isShowReturnGoodsApplicationButton',
                'isDisableSubClinicStockInReturnOut',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            stockInChainReview() {
                return this.goodsConfig?.chainReview?.stockInChainReview;
            },
            todoCount() {
                const {
                    inTodoCount = 0, reviewTodoCount = 0,
                } = this.panelData || {};
                const count = inTodoCount + reviewTodoCount;
                return count || undefined;
            },
            currentPharmacyList() {
                return [
                    {
                        no: null,
                        name: '全部库房',
                    },
                    ...this.localPharmacyList,
                ];
            },
            isVirtualPharmacy() {
                return this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },

            // 处于虚拟库房并且是总部的情况下，隐藏新增入库入口
            hiddenAddOrder() {
                return (this.isVirtualPharmacy && this.isChainAdmin) || !this.currentPharmacyModulePermission.canAddPurchase;
            },

            showGoodsInButton() {
                // 子店读取，是否禁用退货出库功能开关(采购入库也暂时用这个)，后端针对张仲景门店的子店会返回1
                if (this.isChainSubStore && this.isDisableSubClinicStockInReturnOut) {
                    return false;
                }
                return true;
            },
            // 代煎代配不展示退货入口
            showReturnButton() {
                // 子店读取，是否禁用退货出库功能开关，后端针对张仲景门店的子店会返回1
                if (this.isChainSubStore && this.isDisableSubClinicStockInReturnOut) {
                    return false;
                }
                return !this.hiddenAddOrder && !this.isVirtualPharmacy;
            },
            showReturnApplicationButton() {
                return !this.hiddenAddOrder && !this.isVirtualPharmacy && this.isShowReturnGoodsApplicationButton;
            },
            isPurchased() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.SETTLEMENT_STATUS);
            },
            
            typeOptions() {
                if (this.fetchParams.fromType === GOODS_IN_ORDER_FROM_TYPE.GOODS_IN) {
                    return [
                        {
                            label: '采购入库',
                            value: GOODS_IN_ORDER_TYPE.GOODS_IN,
                        },
                        {
                            label: '退货出库',
                            value: GOODS_IN_ORDER_TYPE.RETURN_OUT,
                        },
                    ];
                }
                return [
                    {
                        label: '初始化入库',
                        value: GOODS_IN_ORDER_TYPE.INIT_GOODS_IN,
                    },
                    {
                        label: '初始化退货',
                        value: GOODS_IN_ORDER_TYPE.INIT_RETURN_OUT,
                    },
                ];
            },

            isShowSettlement() {
                // 空中库房不展示 结算状态
                if (this.isVirtualPharmacy) return false;
                if (!this.isPurchased) return false;

                return true;
            },

            tableConfig() {
                const arr = [
                    {
                        label: '单号',
                        key: 'orderNo',
                        style: {
                            width: '172px',
                            minWidth: '172px',
                            maxWidth: '172px',
                            cursor: 'pointer',
                        },
                    },
                    {
                        label: '状态',
                        key: 'status',
                        width: 40,
                    },
                    {
                        label: '结算状态',
                        key: 'settlementStatus',
                        width: 40,
                    },
                    {
                        label: '门店',
                        key: 'clinicName',
                        width: 60,
                    },
                    {
                        label: '类型',
                        key: 'type',
                        style: {
                            width: '110px',
                            minWidth: '110px',
                        },
                    },
                    {
                        label: '库房',
                        key: 'pharmacy',
                        width: 60,
                    },
                    {
                        label: '供应商',
                        key: 'supplier',
                        width: 60,
                    }, {
                        label: '品种数',
                        key: 'kindCount',
                        width: 30,
                        style: {
                            textAlign: 'right',
                        },
                    }, {
                        label: '数量',
                        key: 'count',
                        width: 30,
                        style: {
                            textAlign: 'right',
                        },
                    }, {
                        label: '含税金额',
                        key: 'amount',
                        width: 40,
                        style: {
                            textAlign: 'right',
                        },
                    }, {
                        label: '不含税金额',
                        key: 'amountExcludingTax',
                        width: 40,
                        style: {
                            textAlign: 'right',
                        },
                    }, {
                        label: '制单人',
                        key: 'createdUser',
                        width: 60,
                        style: {
                            paddingLeft: '60px',
                        },
                    }, {
                        label: '创建日期',
                        key: 'createdDate',
                        width: 40,
                        style: {
                            textAlign: 'left',
                        },
                    }, {
                        label: '完成日期',
                        key: 'inDate',
                        width: 40,
                        style: {
                            textAlign: 'left',
                        },
                    },
                ];

                return arr.filter((config) => {
                    if (config.key === 'clinicName') {
                        return this.isChainAdmin;
                    }
                    if (config.key === 'pharmacy') {
                        return this.isChainAdmin || (this.multiPharmacyCanUse && isNull(this.pharmacyNo));
                    }
                    if (config.key === 'createdDate') {
                        return this.isChain;
                    }
                    if (config.key === 'settlementStatus') {
                        return this.isShowSettlement;
                    }
                    return true;
                });
            },

            renderConfig() {
                return this.goodsTableVisible ? {
                    list: this.goodsTableConfig,
                } : {
                    list: this.tableConfig,
                };
            },

            /**
             * @desc 返回如果有其他的dialog/蒙层出现 就关闭下层条码监听
             * <AUTHOR>
             * @date 2019/05/13 15:47:15
             * @params
             * @return
             */
            isDialogShowing() {
                return this.showForm || this.showDetail || this.showOutForm || this.showOutDetail || this.showTypeDialog;
            },
            pageParams() {
                const {
                    limit: pageSize, offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },

            currentSubClinicsArray() {
                return this.subClinics || [];
            },

            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },

            userId() {
                return this.userInfo && this.userInfo.id;
            },

            goodsTableConfig() {
                let arr = [
                    {
                        label: '单号',
                        key: 'orderNo',
                        style: {
                            width: '70px',
                            color: '#0A8CEA',
                        },
                    },
                    {
                        label: '状态',
                        key: 'status',
                        style: {
                            width: '40px',
                        },
                    },
                    {
                        label: '结算状态',
                        key: 'settlementStatus',
                        style: {
                            width: '40px',
                        },
                    },
                    {
                        label: '门店',
                        key: 'clinicName',
                        style: {
                            width: '80px',
                        },
                    },
                    {
                        label: '类型',
                        key: 'type',
                        style: {
                            width: '110px',
                            minWidth: '110px',
                        },
                    },
                    {
                        label: '供应商',
                        key: 'supplier',
                        style: {
                            width: '70px',
                        },
                    },
                    {
                        label: '制单人',
                        key: 'createdUser',
                        style: {
                            width: '40px',
                        },
                    },
                    {
                        label: '完成日期',
                        key: 'inDate',
                        style: {
                            width: '40px',
                        },
                    },
                    {
                        label: '',
                        key: 'include',
                        headerRender: () => {
                            const goodsName = this.searchGoods.medicineCadn || this.searchGoods.name;
                            const goodsSpecs = goodsSpec(this.searchGoods);
                            return (
                                <label class="ellipsis">
                                    <span>包含</span>
                                    <abc-text theme="warning-light" title={`${goodsName} ${goodsSpecs}`} style="margin-left: 8px;">{goodsName} {goodsSpecs}</abc-text>
                                </label>
                            );
                        },
                        style: {
                            width: '150px',
                            flex: 2,
                        },
                    },
                ];
                if (!this.isShowSettlement) {
                    arr = arr.filter((item) => {
                        return item.key !== 'settlementStatus';
                    });
                }
                return arr;
            },
            supplierOptions() {
                if (this.fetchParams.fromType === GOODS_IN_ORDER_FROM_TYPE.INIT_GOODS_IN) {
                    return [
                        {
                            id: UNSELECTED_SUPPLIER_INFO.clinic.id,  
                            name: UNSELECTED_SUPPLIER_INFO.clinic.name,
                        },
                    ].concat(this.currentSupplierList);
                }
                return this.currentSupplierList;
            },
        },

        watch: {
            searchKey() {
                if (!this.searchKey) {
                    this.goodsTableVisible = false;
                    this.fetchParams.withGoodsId = '';
                    this.fetchParams.offset = 0;
                    this.fetchParams.keyword = '';
                    this.fetchData();
                }
            },
            pharmacyType() {
                this.fetchParams.pharmacyType = this.pharmacyType;
                this._debounceFetchData();
            },
            pharmacyNo: {
                handler(no) {
                    if (this.isChainAdmin) {
                        this.fetchParams.pharmacyNo = '';
                        this.selectedPharmacyNo = '';
                    } else {
                        this.fetchParams.pharmacyNo = no;
                        this.selectedPharmacyNo = no;
                    }
                    // this.fetchData();
                },
                immediate: true,
            },
        },
        created() {
            const {
                searchHint,
            } = this.viewDistributeConfig.Inventory;
            this._searchHint = `${searchHint}/单号`;
            this._debounceFetchData = debounce(() => {
                this.fetchData();
            }, 200);

            if (this.stockInChainReview) {
                this.fetchParams.dateField = 'inDate';
            }
        },
        methods: {
            ...mapActions([
                'fetchGoodsCloudDraft',
            ]),
            isNull,
            parseTime,
            paddingMoney,
            handleMounted(data) {
                this.fetchParams.limit = data.paginationLimit;
                this.fetchData(true);
            },
            getAmountTheme(item) {
                return this.isReviseTotalPrice(item) ? 'warning-light' : 'black';
            },
            renderInclude(h, item) {
                let useCount = '', useUnitCostPrice = '', useUnit = '', batchNo = '';
                const {
                    batchNo: no,
                    packageCostPrice,
                    pieceCostPrice,
                    packageUnit,
                    pieceUnit,
                    totalPackageCount,
                    totalPieceCount,
                } = item.searchByGoodsView || {};
                batchNo = no || '-';

                if (totalPackageCount) {
                    useCount = totalPackageCount;
                    useUnitCostPrice = this.paddingMoney(packageCostPrice);
                    useUnit = packageUnit;
                } else {
                    useCount = totalPieceCount;
                    useUnitCostPrice = this.paddingMoney(pieceCostPrice);
                    useUnit = pieceUnit;
                }

                // 带上单位
                useUnitCostPrice = useUnitCostPrice ? `${useUnitCostPrice}/${useUnit || ''}` : '-';
                useCount = useCount ? `${useCount}${useUnit || ''}` : '-';
                return (
                    <div class="include-goods">
                        <span class="count ellipsis" style="max-width:90px;" title={useCount}>
                            数量：<abc-text theme="warning-light">{useCount}</abc-text>
                        </span>

                        <span class="price ellipsis" style="margin-left:12px;max-width:120px;" title={useUnitCostPrice}>
                                {
                                    batchNo === '多批号' ? '均价：' : '进价：'
                                }
                                <abc-text theme="warning-light">{useUnitCostPrice}</abc-text>
                        </span>

                        <span class="batch-no ellipsis" title={batchNo} style="margin-left:12px;max-width:154px;">
                              生产批号：<abc-text theme="warning-light">{batchNo}</abc-text>
                        </span>
                    </div>
                );
            },
            statusConfig(item) {
                let canReview = false;
                if ((item.status === GOODS_IN_STATUS.REVIEW && this.isChainAdmin) ||
                    (item.status === GOODS_IN_STATUS.CONFIRM && !this.isChainAdmin)) {
                    canReview = true;
                }
                const configMap = {
                    [GOODS_IN_STATUS.REVIEW]: canReview ? TagHelper.TODO_TAG : TagHelper.ING_TAG,
                    [GOODS_IN_STATUS.CONFIRM]: canReview ? TagHelper.TODO_TAG : TagHelper.ING_TAG,
                    [GOODS_IN_STATUS.DRAFT]: TagHelper.DRAFT_TAG,
                    [GOODS_IN_STATUS.GOODS_IN]: TagHelper.COMPLETE_TAG,
                    [GOODS_IN_STATUS.REFUSE]: TagHelper.REFUSE_TAG,
                    [GOODS_IN_STATUS.WITH_DRAW]: TagHelper.CANCEL_TAG,
                };
                return configMap[item.status] || TagHelper.CANCEL_TAG;
            },
            getStatusName(item) {
                let str = '';
                if (item.status === GOODS_IN_STATUS.DRAFT) {
                    str = '草稿';
                } else {
                    str = item.statusName || '-';
                }
                return str;
            },
            // 接口定义的结算状态 settlementStatus 1: 未结算 4: 待审核 8:已结算
            initOffset() {
                if (this.status === SettlementStatus.unSettled) { // 未结算0
                    this.fetchParams.status = '';
                    this.fetchParams.settlementStatus = 0;
                } else if (this.status === SettlementStatus.review) { // 结算中2
                    this.fetchParams.status = '';
                    this.fetchParams.settlementStatus = 2;
                } else if (this.status === SettlementStatus.settled) { // 已结算4
                    this.fetchParams.status = '';
                    this.fetchParams.settlementStatus = 4;
                } else if (this.status === SettlementStatus.partial) { // 部分结算1
                    this.fetchParams.status = '';
                    this.fetchParams.settlementStatus = 1;
                } else {
                    this.fetchParams.status = this.status;
                    this.fetchParams.settlementStatus = '';
                }
                this.fetchParams.offset = 0;
                this.fetchData();
            },
            // 盘点入库和已撤回 均不显示结算状态
            showSettStatus(item) {
                return item.supplierId !== this.CHECK_IN_SUPPLIER_ID &&
                    item.status === GOODS_IN_STATUS.GOODS_IN;
            },
            calCostPrice(it) {
                if (isChineseMedicine(it.goods)) {
                    return it.packageCostPrice;
                }
                if (it.goods.dismounting && it.pieceCount > 0) {
                    return paddingMoney(it.packageCostPrice / it.goods.pieceNum);
                }
                return it.packageCostPrice;

            },
            currentUnit(item) {
                const goods = item && item.goods || {};
                const { pieceCount } = item || {};
                if (goods.type === 1 && goods.subType === 2) {
                    return goods.pieceUnit;
                }
                if (goods.dismounting && pieceCount > 0) {
                    return goods.pieceUnit;
                }
                return goods.packageUnit;


            },
            changeDate(picker) {
                if (picker.length === 2) {
                    this.fetchParams.begDate = picker[0];
                    this.fetchParams.endDate = picker[1];
                } else {
                    this.fetchParams.begDate = '';
                    this.fetchParams.endDate = '';
                }
                this.fetchParams.offset = 0;
                this.fetchData();
            },

            async fetchData(needDraft = false) {
                this.loading = true;
                if (needDraft) {
                    await this.fetchGoodsCloudDraft('goods-in');
                }
                const { data } = await GoodsInApi.list(this.fetchParams);

                if (data) {
                    this.panelData = {
                        ...data,
                        rows: data.rows.map((item) => {
                            item.comment = filterComment(item);
                            return item;
                        }),
                    };
                    this.mergeDraftList();
                }

                this.loading = false;
            },

            /**
             * @desc 合并 草稿 + 后端传来的列表数据
             * <AUTHOR>
             * @date 2018/11/28 12:13:26
             */
            mergeDraftList() {
                // 合并云草稿和还存在本地的草稿
                let tempDraftGoodsIn = [];

                // 第一页才更新草稿数据
                if (this.fetchParams.offset === 0) {
                    // 草稿中需要根据库房类型过滤草稿
                    tempDraftGoodsIn = this.draftGoodsIn.filter((draft) => {
                        // 约定了一定有，下面两个兼容处理
                        let pharmacyTypeInDraft = (draft.pharmacy?.type);
                        let pharmacyNoInDraft = (draft.pharmacy?.no);
                        if (isNull(pharmacyTypeInDraft)) {
                            pharmacyTypeInDraft = draft.pharmacyType;
                            pharmacyNoInDraft = draft.pharmacyNo;
                        }
                        if (isNull(pharmacyTypeInDraft)) {
                            // 老的草稿信息存储了order信息，如果都没有设置为默认的本地库房，库房号和库房类型都是 0
                            pharmacyTypeInDraft = draft?.order?.pharmacyType ?? PharmacyTypeEnum.LOCAL_PHARMACY;
                            pharmacyNoInDraft = draft?.order?.pharmacyNo ?? PharmacyTypeEnum.LOCAL_PHARMACY;
                        }
                        if (!this.pharmacyType) {// 是本地库房类型

                            // 先判断单据类型
                            if (this.fetchParams.type !== '') {
                                const type = draft.type || 0;
                                if (type !== this.fetchParams.type) {
                                    return false;
                                }
                            }
                            // 多库房模式下，还要根据库房号过滤
                            if (this.multiPharmacyCanUse && !isNull(this.selectedPharmacyNo)) {
                                return pharmacyNoInDraft === this.selectedPharmacyNo && !pharmacyTypeInDraft;
                            }
                            return !pharmacyTypeInDraft ;
                        }
                        // 代煎代配库房类型
                        return pharmacyTypeInDraft === this.pharmacyType;
                    }).map((draft) => {
                        return {
                            ...draft,
                            status: GOODS_IN_STATUS.DRAFT,
                            createdDate: draft.created || draft.createdDate,
                            lastModifiedDate: draft.lastModified || draft.lastModifiedDate,
                            draftId: draft.draftId || draft.id,
                            toOrganName: draft.toOrgan?.name || draft.toOrganName,
                            isCloud: !draft.draftId,// 是否云草稿
                        };
                    });
                }

                if (this.goodsTableVisible) {
                    this.filterGoodsIn(tempDraftGoodsIn);
                    this.goodsTableData = this.goodsTableData.concat(this.panelData.rows);
                } else {
                    this.dataList = tempDraftGoodsIn.concat(this.panelData.rows);
                }
            },

            /**
             * @desc 直接新增
             * <AUTHOR>
             * @date 2018/11/07 14:48:54
             * @params
             * @return
             */
            addOrder() {
                this.orderId = '';
                this.draftId = '';
                this.quickReturnOrderId = '';
                this.showTypeDialog = true;
                try {
                    this.$abcPlatform.service.report.reportEventSLS('inventory_purchase_add', '【新增采购入库】按钮点击次数');
                } catch (e) {
                    console.error(e);
                }
            },
            returnOrder() {
                this.orderId = '';
                this.draftId = '';
                this.quickReturnOrderId = '';
                this.isResubmit = false;
                this.showOutForm = true;
            },
            handleOpenReturnApplicationDialog() {
                new AbcReturnGoodsApplicationDialog({
                    visible: true,
                    title: '退货申请',
                    pharmacyNo: this.pharmacyNo,
                }).generateDialogAsync({
                    parent: this,
                });
            },

            /**
             * @desc 打开草稿
             * <AUTHOR>
             * @date 2018/11/28 10:38:45
             */
            openDraft(item) {
                this.orderId = '';
                this.quickReturnOrderId = '';
                this.draftId = item.draftId || item.id;
                this.showForm = true;
            },
            createKey(item) {
                return item.keyId || `${item.draftId}-${item.id}`;
            },
            getOrderType(type) {
                return (type === GOODS_IN_ORDER_TYPE.CORRECT_RETURN_OUT || type === GOODS_IN_ORDER_TYPE.RETURN_OUT) ? CorrectOrderTypeEnum.GoodsReturn : CorrectOrderTypeEnum.GoodsIn;
            },
            showOne({
                status, id, type,
            }) {
                this.draftId = '';
                this.isResubmit = false;
                this.quickReturnOrderId = '';
                this.orderId = id;
                if (type === GOODS_IN_ORDER_TYPE.RETURN_OUT) {
                    // 待审核、待确定并且是总部
                    if ((status === GOODS_OUT_STATUS.CONFIRM || status === GOODS_OUT_STATUS.REVIEW) && this.isChainAdmin) {
                        this.showOutForm = true;
                    } else {
                        this.showOutDetail = true;
                    }
                } else if ([GOODS_IN_ORDER_TYPE.CORRECT_RETURN_OUT, GOODS_IN_ORDER_TYPE.CORRECT_IN_ORDER_ADD_MORE].includes(type)) {
                    new AbcCorrectionOrderDialog({
                        visible: true,
                        orderType: this.getOrderType(type),
                        orderId: this.orderId,
                    }).generateDialogAsync({
                        parent: this,
                    });
                } else {
                    this.showDetail = true;
                }
            },

            resubmitHandler({
                orderId, outType,
            }) {
                this.orderId = orderId;
                this.type = outType;
                this.showOutForm = true;
                this.isResubmit = true;
                this.$nextTick(() => {
                    this.showDetail = false;
                });
            },

            /**
             * @desc 只关闭弹窗
             * <AUTHOR>
             * @date 2018/11/24 15:00:59
             */
            async closeDialogHandler() {
                this.showForm = false;
                this.showDetail = false;
                this.showTypeDialog = false;
                if (this.fetchParams.offset === 0) {
                    await this.fetchGoodsCloudDraft('goods-in');
                }
                this.mergeDraftList();
            },

            /**
             * @desc 关闭弹窗 且 刷新列表
             * <AUTHOR>
             * @date 2018/11/24 15:01:13
             */
            async refreshHandler(showToast = false, type = '', isClose = true) {
                if (type === 'add') {
                    this.fetchParams.offset = 0;
                }
                if (isClose) this.closeDialogHandler();

                this.fetchData();
                this.initSupplierList();
                if (showToast) {
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                }
                this.showTypeDialog = false;
                this.$store.dispatch('fetchInventoryTodo', this.pharmacyNo);
            },
            /**
             * @desc 修改并重新发起
             * <AUTHOR>
             * @date 2022/6/16 11:33
             */
            reInStockHandler(props) {
                new AbcFormDialog({
                    orderId: props.orderId,
                    goodsId: props.goodsId,
                    pharmacyNo: this.pharmacyNo,
                    pharmacyType: this.pharmacyType,
                    mallOrderId: this.mallOrderId,
                    refresh: this.refreshHandler,
                    close: this.closeDialogHandler,
                    isReInStock: true,// 特殊标记为重新入库，数据还是之前的orderId获取但是是新建入库单，不修改原单据。
                }).generateDialogAsync({
                    parent: this,
                });
            },
            // 新建退货出库单直接退货当前单据
            returnStockHandler(props) {
                this.draftId = '';
                this.orderId = '';
                this.isResubmit = false;
                this.quickReturnOrderId = props.orderId;
                this.$nextTick(() => {
                    this.showOutForm = true;
                });
            },
            async exportExcel() {
                this.exportBtnLoading = true;
                try {
                    await GoodsInApi.exportList(this.fetchParams);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.exportBtnLoading = false;
                }
            },

            async pageTo(page) {
                this.fetchParams.offset = (page - 1) * this.fetchParams.limit;
                if (this.fetchParams.offset === 0) {
                    await this.fetchGoodsCloudDraft('goods-in');
                }
                await this.fetchData();
            },

            /**
             * @desc 总部筛选门店入库列表
             * <AUTHOR>
             * @date 2018/11/06 19:35:02
             * @params
             * @return
             */
            async filterClinicGoodsIn() {
                this.selectedGoodsClinicName = '';
                if (this.selectedClinic) {
                    const clinic = this.currentSubClinicsArray.find((item) => {
                        return item.id === this.selectedClinic;
                    });
                    this.selectedGoodsClinicName = clinic.name;
                }
                this.fetchParams.clinicId = this.selectedClinic;
                this.fetchParams.offset = 0;
                await this.fetchData();
            },

            /**
             * @desc 处理入库来源类型变化
             * @date 2025/06/30
             */
            handleFromTypeChange(fromType) {
                // 重置类型选择
                this.fetchParams.type = '';
                // 确保选择适当的供应商
                if (fromType === GOODS_IN_ORDER_FROM_TYPE.GOODS_IN && this.fetchParams.supplierId === UNSELECTED_SUPPLIER_INFO.clinic.id) {
                    this.selectedSupplierId = '';
                    this.fetchParams.supplierId = '';
                }
                this.initOffset();
            },

            /**
             * @desc 筛选供应商入库单列表
             * <AUTHOR>
             * @date 2018/11/06 20:24:42
             * @params
             * @return
             */
            async filterSupplierGoodsIn() {
                this.fetchParams.supplierId = this.selectedSupplierId;
                this.fetchParams.offset = 0;
                await this.fetchData();
            },

            async filterPharmacyGoodsIn() {
                this.fetchParams.pharmacyNo = this.selectedPharmacyNo;
                this.fetchParams.offset = 0;
                await this.fetchData();
            },
            selectGoods(goods) {
                this.searchGoods = goods;
                this.searchKey = goods.medicineCadn || goods.name;
                this.fetchParams.withGoodsId = goods.id;
                this.fetchParams.offset = 0;
                this.goodsTableVisible = true;
                this.fetchParams.keyword = '';
                this.fetchData();
            },
            searchList(keyword) {
                this.searchGoods = {};
                this.fetchParams.keyword = keyword;
                this.fetchParams.offset = 0;
                this.goodsTableVisible = false;
                this.fetchData();
            },

            /**
             * @desc 筛选包含 指定药品名称的入库单
             * <AUTHOR>
             * @date 2019/04/23 16:06:39
             */
            filterGoodsIn(draftGoodsInList) {
                this.goodsTableData = [];
                this.filterGoodsFromDraft(draftGoodsInList);
            },

            /**
             * @desc  筛选包含 指定药品名称的 草稿入库单
             * 1. 需要综合 门店（总部存在），日期， 关键字综合搜索
             * 2. 搜索到的药品需要全部展示  一个单据存在多个相同的药品 保存到 withGoods
             * <AUTHOR>
             * @date 2019/04/23 16:20:19
             */
            filterGoodsFromDraft(draftGoodsInList) {
                if (this.fetchParams.status !== '') {
                    this.goodsTableData = [];
                    return;
                }
                if (this.fetchParams.dateField !== 'createdDate') {
                    this.goodsTableData = [];
                    return;
                }
                // 筛选出满足 日期 供应商 和 门店的入库单
                const drafts = clone(draftGoodsInList);
                const goodsTable = drafts.filter((item) => {
                    if (this.checkTime(item.lastModifiedDate) &&
                        (!this.fetchParams.supplierId || (item.supplierId === this.fetchParams.supplierId)) &&
                        (!this.selectedGoodsClinicName || (item.toOrganName === this.selectedGoodsClinicName))
                    ) {
                        let list = [];// 本地和云草稿 list存放不同
                        if (item.list?.length) {
                            list = item.list;
                        } else {
                            list = item.order?.list || [];
                        }

                        const withGoods = list.filter((_item) => {
                            const goodsId = _item.goods?.id || _item.goodsId;
                            if (goodsId === this.searchGoods.id) {
                                return true;
                            }
                        });
                        item.list = clone(withGoods);
                        if (item.list.length) {
                            return item;
                        }
                    }
                });
                this.goodsTableData = this.goodsTableData.concat(goodsTable);
            },

            // 清空 搜索的药品名称
            clearSearch() {
                this.searchKey = '';
                this.fetchParams.withGoodsId = '';
                this.searchGoods = {};
            },

            checkTime(lastTime) {
                const begTime = new Date(this.fetchParams.begDate).toLocaleDateString();
                const endTime = new Date(this.fetchParams.endDate).toLocaleDateString();
                const currentTime = new Date(lastTime).toLocaleDateString();
                if (new Date(currentTime) <= new Date(endTime) && new Date(currentTime) >= new Date(begTime)) {
                    return true;
                }
                return false;
            },
        },

    };
</script>
