<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        size="medium"
        class="select-trace-dialog-wrapper"
        title="确认需要回收单号的包裏"
        @close="$emit('close')"
    >
        <label v-for="item in waybillInfoList" :key="item.waybillCode">
            <abc-card
                padding-size="small"
                class="delivery-item"
                :class="{
                    'is-checked': item.checked
                }"
            >
                <abc-flex align="center">
                    <abc-checkbox v-model="item.checked"></abc-checkbox>
                    <abc-flex style="margin-left: 14px;" vertical>
                        <abc-text bold>
                            {{ item.wpName }} {{ item.waybillCode }}
                        </abc-text>
                        <abc-text v-if="item.receiverInfo" theme="gray" size="small">
                            {{ item.receiverInfo.receiverNameMask }} {{ item.receiverInfo.receiverPhoneMask }}
                        </abc-text>
                        <abc-text v-if="item.receiverInfo" theme="gray" size="small">
                            {{ item.receiverInfo.receiverAddressMask }}
                        </abc-text>
                    </abc-flex>
                </abc-flex>
            </abc-card>
        </label>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleClickConfirm">
                确认回收
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import ECOrderAPI from '../../api/order';

    export default {
        name: 'DialogTraceSelect',
        components: {},
        props: {
            value: Boolean,
            orderId: {
                type: String,
                required: true,
            },
            onConfirm: {
                type: Function,
                required: true,
            },
        },
        data() {
            return {
                visible: false,
                closed: false,
                contentLoading: false,
                waybillInfoList: [],
            };
        },
        computed: {},
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            this.fetchOrderWaybill();
        },
        methods: {
            async fetchOrderWaybill() {
                try {
                    this.contentLoading = true;
                    const res = await ECOrderAPI.fetchOrderWaybill({
                        orderIds: [ this.orderId ],
                    });
                    this.waybillInfoList = res?.rows[0]?.waybillInfoList || [];
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            destroyElement() {
                this.$destroy();
                this.$el?.parentNode?.removeChild(this.$el);
            },
            handleClickConfirm() {
                const list = this.waybillInfoList.filter((it) => it.checked);
                this.onConfirm && this.onConfirm(list, () => {
                    this.closed = true;
                });
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.select-trace-dialog-wrapper {
    .delivery-item {
        cursor: pointer;

        & + .delivery-item {
            margin-top: 12px;
        }

        &.is-checked {
            background: var(--abc-color-B4);
            border-color: var(--abc-color-P7);
        }
    }
}
</style>
