import { ANONYMOUS_ID } from 'views/common/enum';
import ChargeAPI from 'api/charge';
import GoodsAPI from 'api/goods';
import PhysicalExaminationAPI from 'api/physical-examination/pe-order.js';
import cloneDeep from 'lodash.clonedeep';
import i18n from '@/i18n/index.js';
import { formatMoney } from 'src/filters/index';

import {
    GOODS_JSON_TYPE, GOODS_JSON_SUB_TYPE, ORDER_FORM_TYPE, PE_PAY_STATUS_OPTIONS, OPERATE_WAY,
} from './constant';
import { ReportGetWayEnum } from 'views/physical-examination/constants';
import { formatDate } from '@abc/utils-date';
import GoodsV3API from 'api/goods/index-v3';
import CrmAPI from 'api/crm';
import { PEBusinessTypeEnum } from 'views/physical-examination/constants';
import { createGUID } from '@/utils';
import RegistrationAPI from 'api/registrations/index';

export class IndividualPresenter {
    constructor(view) {
        this.view = view;
    }

    async loadTableData(params) {
        try {
            this.view.showLoading();
            const {
                rows, total,
            } = await PhysicalExaminationAPI.fetchOrderList(params);

            const list = (rows || []).map((o) => {
                return {
                    ...o,
                    patientName: o.patient?.name ?? '',
                    patientAge: o.patient?.age?.year,
                    patientSex: o.patient?.sex,
                    marital: o.patient?.marital ?? '',
                    chargeStatusDisplayName:
                        o.businessType === PEBusinessTypeEnum.PUBLIC_HEALTH ?
                            '无需结算' :
                            PE_PAY_STATUS_OPTIONS.find((item) => item.value === o.chargeStatus)?.label || '',
                };
            });

            this.view.receiveTableData({
                list,
                pagination: {
                    count: total || 0,
                },
            });
        } catch (e) {
            console.error(e);
        } finally {
            this.view.hideLoading();
        }
    }

    static async fetchPatientAddressByPatientId(id) {
        if (!id || id === ANONYMOUS_ID) return [];

        const { data } = await ChargeAPI.fetchPatientAddress(id);
        return data.deliveryInfos || [];
    }

    static createDraftOrder(businessType) {
        const today = formatDate(new Date(), 'YYYY-MM-DD');

        return {
            address: null,
            businessTime: today,
            businessType,
            forms: [],
            displayName: '',
            patient: {
                id: '', // 患者id
                name: '', // 患者姓名
                sex: '男', // 患者性别
                age: {
                    month: null,
                    year: null,
                    day: null,
                }, // 患者年龄
                mobile: '', // 患者手机号
                idCard: '', // 患者身份证
                idCardType: '', // 患者身份证类型
            },
            reportGetWay: ReportGetWayEnum.TAKE_SELF,
        };
    }

    static async getRecommendGoods() {
        const promises = [
            GoodsV3API.searchGoods({
                keyword: '',
                offset: 0,
                limit: 5,
                jsonType: [
                    {
                        type: GOODS_JSON_TYPE.PHYSICAL_EXAMINATION_COMPOSE,
                        subType: [GOODS_JSON_SUB_TYPE.NORMAL],
                    },
                ],
            }),
            GoodsV3API.searchGoods({
                keyword: '',
                offset: 0,
                limit: 5,
                jsonType: [
                    {
                        type: GOODS_JSON_TYPE.PHYSICAL_EXAMINATION_COMPOSE,
                        subType: [GOODS_JSON_SUB_TYPE.PUBLIC_HEALTH],
                    },
                ],
            }),
        ];

        const [normalGoods, publicHealthGoods] = await Promise.all(promises);

        return {
            normalRecommendGoods: normalGoods.data.list || [],
            publicHealthRecommendGoods: publicHealthGoods.data.list || [],
        };
    }

    static async getGoodsList(params) {
        const { data } = await GoodsV3API.searchGoods(params);
        return data.list || [];
    }

    async getEmployeeList() {
        const { data } = await CrmAPI.fetchEmployeeList();
        return data.rows || [];
    }

    async getEmployeeWithDepartmentList() {
        const list = await RegistrationAPI.fetchAllDoctorsRegsFee({
            allEmployee: 1,
            allDepartment: 1,
        });
        return list;
    }

    static async loadGoodsDetailById(id) {
        const { data } = await GoodsAPI.fetchGoods(id);

        return data;
    }

    static prepareDraftGoodsData(data, ignoreExpectedUnitPrice = false) {
        const dataClone = cloneDeep(data);

        const ADDITION_FORM = dataClone.forms.find((o) => o.type === ORDER_FORM_TYPE.ADDITIONS);
        const COMPOSE_FORM = dataClone.forms.find((o) => o.type === ORDER_FORM_TYPE.PE_COMPOSE);

        if (ADDITION_FORM) {
            ADDITION_FORM.items = ADDITION_FORM.items.map(
                ({
                    goodsId,
                    salesEmployeeId,
                    status,
                    expectedUnitPrice,
                    scheduleTime,
                    remark,
                    keyId,
                    salesDepartmentId,
                }) => {
                    const obj = {
                        goodsId,
                        salesEmployeeId,
                        salesDepartmentId,
                        status,
                        scheduleTime,
                        remark,
                        keyId,
                    };

                    if (typeof expectedUnitPrice !== 'undefined' && !ignoreExpectedUnitPrice) {
                        obj.expectedUnitPrice = Number(expectedUnitPrice);
                    }

                    return obj;
                },
            );
        }

        if (COMPOSE_FORM) {
            COMPOSE_FORM.items[0] = {
                ...COMPOSE_FORM.items[0],
                children: COMPOSE_FORM.items[0].children.map(
                    ({
                        status, expectedUnitPrice, goodsId, scheduleTime, remark, keyId, goodsPrimaryId,
                    }) => {
                        const obj = {
                            status,
                            goodsId,
                            scheduleTime,
                            remark,
                            keyId,
                            goodsPrimaryId,
                        };

                        if (typeof expectedUnitPrice !== 'undefined' && !ignoreExpectedUnitPrice) {
                            obj.expectedUnitPrice = Number(expectedUnitPrice);
                        }

                        return obj;
                    },
                ),
            };
        }

        return dataClone;
    }

    static prepareDetailGoodsData(data, ignoreExpectedUnitPrice = false) {
        const dataClone = cloneDeep(data);

        const {
            address, businessTime, businessType, forms, reportGetWay,
        } = dataClone;

        const ADDITION_FORM = forms.find((o) => o.type === ORDER_FORM_TYPE.ADDITIONS);
        const COMPOSE_FORM = forms.find((o) => o.type === ORDER_FORM_TYPE.PE_COMPOSE);

        if (ADDITION_FORM) {
            ADDITION_FORM.items = ADDITION_FORM.items.map(
                ({
                    goodsId,
                    salesEmployeeId,
                    status,
                    expectedUnitPrice,
                    id,
                    scheduleTime,
                    remark,
                    keyId,
                    salesDepartmentId,
                }) => {
                    const obj = {
                        goodsId,
                        salesEmployeeId,
                        salesDepartmentId,
                        status,
                        scheduleTime,
                        remark,
                        keyId,
                    };

                    if (typeof expectedUnitPrice !== 'undefined' && !ignoreExpectedUnitPrice) {
                        obj.expectedUnitPrice = Number(expectedUnitPrice);
                    }

                    if (id) {
                        obj.id = id;
                    }

                    return obj;
                },
            );
        }

        if (COMPOSE_FORM) {
            COMPOSE_FORM.items[0] = {
                ...COMPOSE_FORM.items[0],
                children: COMPOSE_FORM.items[0].children.map(
                    ({
                        status, expectedUnitPrice, id, goodsId, scheduleTime, remark, keyId, goodsPrimaryId,
                    }) => {
                        const obj = {
                            status,
                            goodsId,
                            id,
                            scheduleTime,
                            remark,
                            keyId,
                            goodsPrimaryId,
                        };

                        if (typeof expectedUnitPrice !== 'undefined' && !ignoreExpectedUnitPrice) {
                            obj.expectedUnitPrice = Number(expectedUnitPrice);
                        }

                        return obj;
                    },
                ),
            };
        }

        return {
            address,
            businessTime,
            businessType,
            forms,
            reportGetWay,
        };
    }

    static prepareGroupDetailGoodsData(data) {
        const dataClone = cloneDeep(data);

        const {
            address, businessTime, businessType, forms, reportGetWay,
        } = dataClone;

        const ADDITION_FORM = forms.find((o) => o.type === ORDER_FORM_TYPE.ADDITIONS);
        const COMPOSE_FORM = forms.find((o) => o.type === ORDER_FORM_TYPE.GROUP_PE_COMPOSE);

        if (ADDITION_FORM) {
            ADDITION_FORM.items = ADDITION_FORM.items.map(
                ({
                    goodsId, salesEmployeeId, status, expectedUnitPrice, id, keyId, salesDepartmentId,
                }) => {
                    const obj = {
                        goodsId,
                        salesEmployeeId,
                        salesDepartmentId,
                        status,
                        keyId,
                    };

                    if (typeof expectedUnitPrice !== 'undefined') {
                        obj.expectedUnitPrice = Number(expectedUnitPrice);
                    }

                    if (id) {
                        obj.id = id;
                    }

                    return obj;
                },
            );
        }

        if (COMPOSE_FORM) {
            COMPOSE_FORM.items = COMPOSE_FORM.items.map(
                ({
                    status, expectedUnitPrice, id, goodsId, keyId, goodsPrimaryId,
                }) => {
                    const obj = {
                        status,
                        goodsId,
                        id,
                        keyId,
                        goodsPrimaryId,
                    };

                    if (typeof expectedUnitPrice !== 'undefined') {
                        obj.expectedUnitPrice = Number(expectedUnitPrice);
                    }

                    return obj;
                },
            );
        }

        return {
            address,
            businessTime,
            businessType,
            forms,
            reportGetWay,
        };
    }

    static async createOrder(data) {
        const postData = IndividualPresenter.prepareDraftGoodsData(data);

        return PhysicalExaminationAPI.createOrder(postData);
    }

    static async updateOrder(id, data) {
        const postData = IndividualPresenter.prepareDetailGoodsData(data);

        return PhysicalExaminationAPI.updateOrder(id, postData);
    }

    static async updateGroupOrder(id, data) {
        const postData = IndividualPresenter.prepareGroupDetailGoodsData(data);

        return PhysicalExaminationAPI.updateOrder(id, postData);
    }

    static async deleteOrder(id) {
        return PhysicalExaminationAPI.deleteOrder(id);
    }

    static async createGroupDetailRsp(id) {
        const { data } = await PhysicalExaminationAPI.fetchOrderDetail(id);

        const ADDITION_FORM = data.forms.find((o) => o.type === ORDER_FORM_TYPE.ADDITIONS);
        const COMPOSE_FORM = data.forms.find((o) => o.type === ORDER_FORM_TYPE.GROUP_PE_COMPOSE);

        if (ADDITION_FORM) {
            ADDITION_FORM.items = ADDITION_FORM.items.map((item) => {
                return {
                    ...item,
                    displayName: item.goodsName,
                    salesEmployeeId: item.salesEmployee?.id ?? '',
                    salesDepartmentId: item.salesDepartment?.id ?? '',
                    keyId: item.keyId || createGUID(),
                };
            });
        }

        if (COMPOSE_FORM) {
            COMPOSE_FORM.salesEmployeeId = COMPOSE_FORM.salesEmployee?.id ?? '';
            COMPOSE_FORM.salesDepartmentId = COMPOSE_FORM.salesDepartment?.id ?? '';

            COMPOSE_FORM.items = COMPOSE_FORM.items.map((item) => {
                return {
                    ...item,
                    displayName: item.goodsName,
                    keyId: item.keyId || createGUID(),
                };
            });
        }

        const {
            groupOrder, additionalInfo,
        } = data;

        return {
            detail: data,
            totalPrice: data.totalFee,
            additionDisplayInfo: ADDITION_FORM ?
                [
                    {
                        label: '增项项目',
                        value: `${additionalInfo.total}项`,
                    },
                    {
                        label: '订单编号',
                        value: additionalInfo.orderNo,
                    },
                    {
                        label: '结算状态',
                        value:
                              data.businessType === PEBusinessTypeEnum.PUBLIC_HEALTH ?
                                  '无需结算' :
                                  PE_PAY_STATUS_OPTIONS.find((item) => item.value === additionalInfo.chargeStatus)
                                      ?.label || '',
                    },
                    {
                        label: '应收',
                        value: `${i18n.t('currencySymbol')}${formatMoney(additionalInfo.receivableFee)}`,
                    },
                    {
                        label: '实收',
                        value: `${i18n.t('currencySymbol')}${formatMoney(additionalInfo.receivedFee)}`,
                    },
                ] :
                [],
            composeDisplayInfo: COMPOSE_FORM ?
                [
                    {
                        label: '订单名称',
                        value: groupOrder.orderName,
                    },
                    {
                        label: '订单编号',
                        value: groupOrder.orderNo,
                    },
                    {
                        label: '下单时间',
                        value: formatDate(groupOrder.created, 'YYYY-MM-DD HH:mm'),
                    },
                    {
                        label: '销售人',
                        value: groupOrder.salesEmployeeName ?? '',
                    },
                    {
                        label: '结算状态',
                        value:
                              data.businessType === PEBusinessTypeEnum.PUBLIC_HEALTH ?
                                  '无需结算' :
                                  PE_PAY_STATUS_OPTIONS.find((item) => item.value === groupOrder.payStatus)?.label ||
                                    '',
                    },
                ] :
                [],
        };
    }

    static async createDetailRsp(id) {
        const { data } = await PhysicalExaminationAPI.fetchOrderDetail(id);

        const ADDITION_FORM = data.forms.find((o) => o.type === ORDER_FORM_TYPE.ADDITIONS);
        const COMPOSE_FORM = data.forms.find((o) => o.type === ORDER_FORM_TYPE.PE_COMPOSE);

        if (ADDITION_FORM) {
            ADDITION_FORM.items = ADDITION_FORM.items.map((item) => {
                return {
                    ...item,
                    displayName: item.goodsName,
                    salesEmployeeId: item.salesEmployee?.id ?? '',
                    salesDepartmentId: item.salesDepartment?.id ?? '',
                    keyId: item.keyId || createGUID(),
                };
            });
        }

        if (COMPOSE_FORM) {
            COMPOSE_FORM.salesEmployeeId = COMPOSE_FORM.salesEmployee?.id ?? '';
            COMPOSE_FORM.salesDepartmentId = COMPOSE_FORM.salesDepartment?.id ?? '';

            COMPOSE_FORM.items[0].children = COMPOSE_FORM.items[0].children.map((item) => {
                return {
                    ...item,
                    displayName: item.goodsName,
                    keyId: item.keyId || createGUID(),
                };
            });
        }

        return {
            detail: data,
            totalPrice: data.totalFee,
            additionDisplayInfo: ADDITION_FORM ?
                [
                    {
                        label: '增项项目',
                        value: `${ADDITION_FORM.items.length}项`,
                    },
                    {
                        label: '订单编号',
                        value: data.orderNo,
                    },
                    {
                        label: '结算状态',
                        value:
                              data.businessType === PEBusinessTypeEnum.PUBLIC_HEALTH ?
                                  '无需结算' :
                                  PE_PAY_STATUS_OPTIONS.find((item) => item.value === data.chargeStatus)?.label || '',
                    },
                    {
                        label: '应收',
                        value: `${i18n.t('currencySymbol')}${formatMoney(ADDITION_FORM.receivableFee)}`,
                    },
                    {
                        label: '实收',
                        value: `${i18n.t('currencySymbol')}${formatMoney(ADDITION_FORM.receivedFee)}`,
                    },
                ] :
                [],
            composeDisplayInfo: COMPOSE_FORM ?
                [
                    {
                        label: '订单名称',
                        value: data.name,
                    },
                    {
                        label: '订单编号',
                        value: data.orderNo,
                    },
                    {
                        label: '下单时间',
                        value: formatDate(data.orderCreated, 'YYYY-MM-DD HH:mm'),
                    },
                    {
                        label: '销售人',
                        value: COMPOSE_FORM.salesEmployee?.name ?? '',
                    },
                    {
                        label: '结算状态',
                        value:
                              data.businessType === PEBusinessTypeEnum.PUBLIC_HEALTH ?
                                  '无需结算' :
                                  PE_PAY_STATUS_OPTIONS.find((item) => item.value === data.chargeStatus)?.label || '',
                    },
                ] :
                [],
        };
    }

    static async clac(data) {
        const {
            mode, totalPrice, detail, isModifyTotalPrice, id,
        } = data;
        let payload;

        if (mode === OPERATE_WAY.ADD) {
            payload = IndividualPresenter.prepareDraftGoodsData(detail, isModifyTotalPrice);
        } else {
            payload = IndividualPresenter.prepareDetailGoodsData(detail, isModifyTotalPrice);
            payload.id = id;
        }

        if (isModifyTotalPrice) {
            payload.expectedTotalPrice = Number(totalPrice);
        }

        return PhysicalExaminationAPI.calculatePrice(payload);
    }

    async export(params) {
        try {
            this.view.exportBtnLoading = true;
            await PhysicalExaminationAPI.exportReportList(params);
        } catch (error) {
            this.$Toast({
                message: error.message,
                type: 'error',
            });
        } finally {
            this.view.exportBtnLoading = false;
        }
    }
}
