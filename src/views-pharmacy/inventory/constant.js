/**
 * 调价 item 的 status，与 PriceAdjustmentStatus 不同
 * @type {Readonly<{DRAFT: number, WITH_DRAW: number, REVIEW: number, GOODS_IN: number, REFUSE: number}>}
 */
export const PriceAdjustmentItemStatus = Object.freeze({
    DRAFT: 0, // 草稿
    REVIEW: 1, // 待审核
    GOODS_IN: 2, // 待生效
    FINISH: 3, // 已生效
    REFUSE: 4, // 已驳回
    WITH_DRAW: 5, // 已撤回
});

export const PriceAdjustmentItemTagTheme = Object.freeze({
    [PriceAdjustmentItemStatus.DRAFT]: '', // 草稿
    [PriceAdjustmentItemStatus.REVIEW]: 'primary', // 待审核
    [PriceAdjustmentItemStatus.GOODS_IN]: 'primary', // 待生效
    [PriceAdjustmentItemStatus.REFUSE]: 'danger', // 已驳回
    [PriceAdjustmentItemStatus.WITH_DRAW]: 'danger', // 已撤回
    [PriceAdjustmentItemStatus.FINISH]: 'success', // 已生效
});

export const PriceAdjustmentStatus = Object.freeze({
    DRAFT: 0, // 草稿
    REVIEW: 1, // 待审核
    GOODS_IN: 2, // 待生效
    FINISH: 3, // 已生效
    REFUSE: 4, // 已驳回
    WITH_DRAW: 5, // 已撤回
    CANCEL: 6, // 已作废
});

export const PriceAdjustmentStatusName = Object.freeze({
    [PriceAdjustmentStatus.DRAFT]: '草稿',
    [PriceAdjustmentStatus.REVIEW]: '待审核',
    [PriceAdjustmentStatus.GOODS_IN]: '待生效',
    [PriceAdjustmentStatus.FINISH]: '已生效',
    [PriceAdjustmentStatus.REFUSE]: '已驳回',
    [PriceAdjustmentStatus.WITH_DRAW]: '已撤回',
    [PriceAdjustmentStatus.CANCEL]: '已作废',
});

export const PriceAdjustmentItemTagStyle = Object.freeze({
    [PriceAdjustmentItemStatus.DRAFT]: {
        type: 'await',
        text: '草稿',
    },
    [PriceAdjustmentItemStatus.REVIEW]: {
        type: 'doing',
        text: '待审核',
    },
    [PriceAdjustmentItemStatus.GOODS_IN]: {
        type: 'doing',
        text: '待生效',
    },
    [PriceAdjustmentItemStatus.FINISH]: {
        type: 'finish',
        text: '已生效',
    },
    [PriceAdjustmentItemStatus.REFUSE]: {
        type: 'reject',
        text: '已驳回',
    },
    [PriceAdjustmentItemStatus.WITH_DRAW]: {
        type: 'reject',
        text: '已撤回',
    },
    // [PriceAdjustmentItemStatus.CANCEL]: {
    //     type: 'reject',
    //     text: '已作废',
    // },
});

// 批量调价类型
export const OpType = Object.freeze({
    // 手动设置售价
    MANUAL: 0,
    // 按原进价进行比例调价
    SELL_PRICE: 1,
    // 按最近进价进行比例调价
    COST_PRICE: 2,
    // 按社保限价进行调价
    SOCIAL_PRICE: 3,
    // 按平均成本进行比例调价
    PROFIT: 4,
    // 按进价加成
    MARK_UP: 10,
});

// 采购单状态
export const PurchaseOrderStatus = Object.freeze({
    DRAFT: -1, // 草稿
    REVIEW: 0, // 待审核
    REVIEWED: 1, // 审核完成
    ACTION: 2, // 待处理
    REFUSE: 9, // 已驳回
    TERMINATE: 10, // 已终止
    WITH_DRAW: 31, // 已撤销
    PAYMENT: 40, // 待付款
    MALL_WAITING_PURCHASE: 44, // 待采购
    DISTRIBUTION: 50, // 待发货
    TAKE_DELIVERY: 60, // 待收货
    ACCEPTANCE_CHECK: 62, // 待验收
    GOODS_ING: 64, // 待入库
    GOODS_IN: 66, // 已入库
    FINISHED: 70, // 已完成
    CLOSED: 80, // 已关闭
});

// 采购单状态名称
export const PurchaseOrderStatusName = Object.freeze({
    [PurchaseOrderStatus.DRAFT]: '草稿',
    [PurchaseOrderStatus.REVIEW]: '待审核',
    [PurchaseOrderStatus.REVIEWED]: '审核完成',
    [PurchaseOrderStatus.ACTION]: '待处理',
    [PurchaseOrderStatus.PAYMENT]: '待付款',
    [PurchaseOrderStatus.MALL_WAITING_PURCHASE]: '待采购',
    [PurchaseOrderStatus.DISTRIBUTION]: '待发货',
    [PurchaseOrderStatus.TAKE_DELIVERY]: '待收货',
    [PurchaseOrderStatus.ACCEPTANCE_CHECK]: '待验收',
    [PurchaseOrderStatus.GOODS_ING]: '待入库',
    [PurchaseOrderStatus.GOODS_IN]: '已入库',
    [PurchaseOrderStatus.REFUSE]: '已驳回',
    [PurchaseOrderStatus.TERMINATE]: '已终止',
    [PurchaseOrderStatus.WITH_DRAW]: '已撤销',
    [PurchaseOrderStatus.FINISHED]: '已完成',
    [PurchaseOrderStatus.CLOSED]: '已关闭',
});

export const PurchaseOrderStatusTagTheme = Object.freeze({
    [PurchaseOrderStatus.DRAFT]: 'warning',
    [PurchaseOrderStatus.REVIEW]: 'primary',
    [PurchaseOrderStatus.REVIEWED]: 'success',
    [PurchaseOrderStatus.ACTION]: 'primary',
    [PurchaseOrderStatus.PAYMENT]: 'primary',
    [PurchaseOrderStatus.MALL_WAITING_PURCHASE]: 'primary',
    [PurchaseOrderStatus.DISTRIBUTION]: 'primary',
    [PurchaseOrderStatus.TAKE_DELIVERY]: 'primary',
    [PurchaseOrderStatus.ACCEPTANCE_CHECK]: 'primary',
    [PurchaseOrderStatus.GOODS_ING]: 'primary',
    [PurchaseOrderStatus.GOODS_IN]: 'success',
    [PurchaseOrderStatus.REFUSE]: 'danger',
    [PurchaseOrderStatus.TERMINATE]: 'danger',
    [PurchaseOrderStatus.WITH_DRAW]: 'default',
    [PurchaseOrderStatus.FINISHED]: 'success',
    [PurchaseOrderStatus.CLOSED]: 'default',
});

// 单据类型-采购单、要货单
export const PurchaseOrderType = Object.freeze({
    // 0采购单-线下采购
    PURCHASE: 0,
    // 10ABC商城采购
    MALL: 10,
    // 20要货单
    REQUIRE: 20,
});

// 单据类型-采购单、要货单
export const PurchaseOrderTypeName = Object.freeze({
    // 0采购单-线下采购
    [PurchaseOrderType.PURCHASE]: '采购订单',
    // 10ABC商城采购
    [PurchaseOrderType.MALL]: '商城订单',
    // 20要货单
    [PurchaseOrderType.REQUIRE]: '要货单',
});


// 收货单状态
export const PurchaseReceiveOrderStatus = Object.freeze({
    DRAFT: 0, // 草稿
    REVIEW: 1, // 待审核
    TAKE_DELIVERY: 10, // 待收货
    RECEIVED: 20, // 已收货
    REJECT: 9, // 已驳回
    WITH_DRAW: 31, // 已撤销
});
// 收货单状态名称
export const PurchaseReceiveOrderStatusName = Object.freeze({
    [PurchaseReceiveOrderStatus.DRAFT]: '草稿',
    [PurchaseReceiveOrderStatus.REVIEW]: '待审核',
    [PurchaseReceiveOrderStatus.TAKE_DELIVERY]: '待收货',
    [PurchaseReceiveOrderStatus.RECEIVED]: '已收货',
    [PurchaseReceiveOrderStatus.REJECT]: '已驳回',
    [PurchaseReceiveOrderStatus.WITH_DRAW]: '已撤销',
});

export const PurchaseReceiveOrderStatusTagTheme = Object.freeze({
    [PurchaseReceiveOrderStatus.DRAFT]: 'warning',
    [PurchaseReceiveOrderStatus.REVIEW]: 'primary',
    [PurchaseReceiveOrderStatus.TAKE_DELIVERY]: 'primary',
    [PurchaseReceiveOrderStatus.RECEIVED]: 'success',
    [PurchaseReceiveOrderStatus.REJECT]: 'danger',
    [PurchaseReceiveOrderStatus.WITH_DRAW]: 'default',
});

export const PurchaseDeliveryOrderStatus = Object.freeze({
    DRAFT: 0, // 草稿
    CONFIRM: 10, // 待入库
    GOODS_IN: 20, // 已入库
    RETURN: 30, //已退货
    REVERT: 40, //已撤回
});
// 配货单状态名称
export const PurchaseDeliveryOrderStatusName = Object.freeze({
    [PurchaseDeliveryOrderStatus.DRAFT]: '草稿',
    [PurchaseDeliveryOrderStatus.CONFIRM]: '待收货',
    [PurchaseDeliveryOrderStatus.GOODS_IN]: '已收货',
    [PurchaseDeliveryOrderStatus.RETURN]: '已退货',
    [PurchaseDeliveryOrderStatus.REVERT]: '已撤回',
});

export const PurchaseDeliveryOrderStatusTagName = Object.freeze({
    [PurchaseDeliveryOrderStatus.DRAFT]: 'warning',
    [PurchaseDeliveryOrderStatus.CONFIRM]: 'primary',
    [PurchaseDeliveryOrderStatus.GOODS_IN]: 'success',
    [PurchaseDeliveryOrderStatus.RETURN]: 'danger',
    [PurchaseDeliveryOrderStatus.REVERT]: 'default',
});

export const PurchaseSupplierCompanyTypeStatus = Object.freeze({
    BUSINESS: 1, // 商业企业-药品批发企业
    DRUGS: 10, //药品生产企业
    INSTRUMENT: 20, //医疗器械生产企业
    INSTRUMENT_WHOLESALE: 30, // 医疗器械批发企业
    COSMETICS_MAKE: 40, // 化妆品生产企业
    OTHER: 1000, // 其他商业企业
});

export const PurchaseSupplierCompanyTypeName = Object.freeze({
    [PurchaseSupplierCompanyTypeStatus.BUSINESS]: '药品批发企业',
    [PurchaseSupplierCompanyTypeStatus.DRUGS]: '药品生产企业',
    [PurchaseSupplierCompanyTypeStatus.INSTRUMENT]: '医疗器械生产企业',
    [PurchaseSupplierCompanyTypeStatus.INSTRUMENT_WHOLESALE]: '医疗器械批发企业',
    [PurchaseSupplierCompanyTypeStatus.COSMETICS_MAKE]: '化妆品生产企业',
    [PurchaseSupplierCompanyTypeStatus.OTHER]: '其他商业企业',
});

export const PurchaseSupplierCompanyTypeLabel = [
    {
        label: PurchaseSupplierCompanyTypeName[PurchaseSupplierCompanyTypeStatus.BUSINESS],
        value: PurchaseSupplierCompanyTypeStatus.BUSINESS,
    },
    {
        label: PurchaseSupplierCompanyTypeName[PurchaseSupplierCompanyTypeStatus.DRUGS],
        value: PurchaseSupplierCompanyTypeStatus.DRUGS,
    },
    {
        label: PurchaseSupplierCompanyTypeName[PurchaseSupplierCompanyTypeStatus.INSTRUMENT_WHOLESALE],
        value: PurchaseSupplierCompanyTypeStatus.INSTRUMENT_WHOLESALE,
    },
    {
        label: PurchaseSupplierCompanyTypeName[PurchaseSupplierCompanyTypeStatus.INSTRUMENT],
        value: PurchaseSupplierCompanyTypeStatus.INSTRUMENT,
    },
    {
        label: PurchaseSupplierCompanyTypeName[PurchaseSupplierCompanyTypeStatus.COSMETICS_MAKE],
        value: PurchaseSupplierCompanyTypeStatus.COSMETICS_MAKE,
    },
    {
        label: PurchaseSupplierCompanyTypeName[PurchaseSupplierCompanyTypeStatus.OTHER],
        value: PurchaseSupplierCompanyTypeStatus.OTHER,
    },
];

export const EntrustDeliveryModeName = Object.freeze({
    1: '总部集采/委托配送',
    0: '门店自采',
});

// 集采委托配送方式
export const EntrustDeliveryType = Object.freeze({
    SELF_DELIVERY: 2, // 自配
    ENTRUST_DELIVERY: 1, // 委配
});

// 来源单据类型-采购单/要货单
export const ReceiptOrginOrderType = Object.freeze({
    // 总部集采(自配)
    CONCENTRATE: 0,
    // 总部集采(委配)
    CONCENTRATE_SEPARATE: 1,
    // 门店自采
    SEPARATE: 10,
    // 总部配货
    DELIVERY: 20,
    // 门店调拨
    TRANS: 30,
    // 门店要货
    CLAIM: 40,
    // 来自初始化入库单的退货单
    INIT_GOODS_IN: 50,
});

// 购进关联单据类型
export const RelatedOrderType = Object.freeze({
    // 要货单
    CLAIM: 0,
    // 采购单
    PURCHASE: 1,
    // 配货单
    DELIVERY: 2,
    // 收货单
    RECEIPT: 3,
    // 验收单
    INSPECT: 4,
    // 入库单
    GOODS_IN: 5,
    // 退货单
    RETURN: 6,
});

export const SourceTypeToOrderType = Object.freeze({
    // 总部集采(自配)
    [ReceiptOrginOrderType.CONCENTRATE]: RelatedOrderType.PURCHASE,
    // 总部集采(委配)
    [ReceiptOrginOrderType.CONCENTRATE_SEPARATE]: RelatedOrderType.PURCHASE,
    // 门店自采
    [ReceiptOrginOrderType.SEPARATE]: RelatedOrderType.PURCHASE,
    // 总部配货
    [ReceiptOrginOrderType.DELIVERY]: RelatedOrderType.DELIVERY,
    // 门店调拨
    [ReceiptOrginOrderType.TRANS]: RelatedOrderType.PURCHASE,
    // 门店要货
    [ReceiptOrginOrderType.CLAIM]: RelatedOrderType.CLAIM,
});

// 来源单据类型-采购单/要货单
export const ReceiptOrginOrderTypeName = Object.freeze({
    // 总部集采(自配)
    [ReceiptOrginOrderType.CONCENTRATE]: '总部集采(自配)',
    // 总部集采(委配)
    [ReceiptOrginOrderType.CONCENTRATE_SEPARATE]: '总部集采(委配)',
    // 门店自采
    [ReceiptOrginOrderType.SEPARATE]: '门店自采',
    // 总部配货
    [ReceiptOrginOrderType.DELIVERY]: '总部配货',
    // 门店调拨
    [ReceiptOrginOrderType.TRANS]: '门店调拨',
    // 门店要货
    [ReceiptOrginOrderType.CLAIM]: '门店要货',
});


// 验收单状态
export const PurchaseInspectOrderStatus = Object.freeze({
    // 草稿
    DRAFT: 0,
    // 待验收
    WAIT_INSPECT: 10,
    // 验收合格
    INSPECT_PASS: 20,
    // 不合格
    NO_PASS: 30,
    // 部分合格
    PART_PASS: 40,
});

// 验收单状态名称
export const PurchaseInspectOrderStatusName = Object.freeze({
    [PurchaseInspectOrderStatus.DRAFT]: '草稿',
    [PurchaseInspectOrderStatus.WAIT_INSPECT]: '待验收',
    [PurchaseInspectOrderStatus.INSPECT_PASS]: '验收合格',
    [PurchaseInspectOrderStatus.NO_PASS]: '不合格',
    [PurchaseInspectOrderStatus.PART_PASS]: '部分合格',
});

export const PurchaseInspectOrderStatusTagTheme = Object.freeze({
    [PurchaseInspectOrderStatus.DRAFT]: 'warning',
    [PurchaseInspectOrderStatus.WAIT_INSPECT]: 'primary',
    [PurchaseInspectOrderStatus.INSPECT_PASS]: 'success',
    [PurchaseInspectOrderStatus.NO_PASS]: 'danger',
    [PurchaseInspectOrderStatus.PART_PASS]: 'danger',
});


// 入库单状态
export const PurchaseGoodsInOrderStatus = Object.freeze({
    // 前端草稿状态
    DRAFT: 10000,
    // 待审核
    REVIEW: 0,
    // 待确认-待入库
    CONFIRM: 1,
    // 已入库
    GOODS_IN: 2,
    // 已驳回
    REFUSE: 9,
    // 已撤回
    WITH_DRAW: 31,

    // 商品没有入过库
    NEVER_GOODS_IN: 40,
    // 不能入库
    DISABLED_GOODS_IN: 41,
});

export const PurchaseGoodsInOrderStatusTagTheme = Object.freeze({
    [PurchaseGoodsInOrderStatus.DRAFT]: 'warning',
    [PurchaseGoodsInOrderStatus.REVIEW]: 'primary',
    [PurchaseGoodsInOrderStatus.CONFIRM]: 'primary',
    [PurchaseGoodsInOrderStatus.GOODS_IN]: 'success',
    [PurchaseGoodsInOrderStatus.REFUSE]: 'danger',
    [PurchaseGoodsInOrderStatus.WITH_DRAW]: 'default',
    [PurchaseGoodsInOrderStatus.NEVER_GOODS_IN]: 'default',
    [PurchaseGoodsInOrderStatus.DISABLED_GOODS_IN]: 'danger',
});

// 入库单状态名称
export const PurchaseGoodsInOrderStatusName = Object.freeze({
    [PurchaseGoodsInOrderStatus.REVIEW]: '待审核',
    [PurchaseGoodsInOrderStatus.CONFIRM]: '待入库',
    [PurchaseGoodsInOrderStatus.GOODS_IN]: '已入库',
    [PurchaseGoodsInOrderStatus.REFUSE]: '已驳回',
    [PurchaseGoodsInOrderStatus.WITH_DRAW]: '已撤回',
    [PurchaseGoodsInOrderStatus.NEVER_GOODS_IN]: '商品没有入过库',
    [PurchaseGoodsInOrderStatus.DISABLED_GOODS_IN]: '不能入库',
});


// 退货，页面类型 1选择入库单，2选择退货商品，3确认退货
export const ReturnPageType = Object.freeze({
    // 1选择入库单
    SELECT_GOODS_IN: 1,
    // 2选择退货商品
    SELECT_RETURN_GOODS: 2,
    // 3确认退货
    CONFIRM_RETURN_GOODS: 3,
});

export const ReturnPageTypeName = Object.freeze({
    // 1选择入库单
    [ReturnPageType.SELECT_GOODS_IN]: '选择入库单',
    // 2选择退货商品
    [ReturnPageType.SELECT_RETURN_GOODS]: '选择退货商品',
    // 3确认退货
    [ReturnPageType.CONFIRM_RETURN_GOODS]: '确认退货',
});

// 退货来源
export const ReturnSourceType = Object.freeze({
    // 召回
    RECALL: 10,
    // 不合格品
    UNQUALIFIED: 20,
});

export const ReturnGoodsStatus = Object.freeze({
    // 退货待审核
    REVIEW: 0,
    // 退货成功
    FINISHED: 2,
    // 审核驳回
    REFUSE: 9,
});

export const ConserveStatus = Object.freeze({
    // 待养护
    REVIEW: 0,
    // 已养护
    FINISHED: 1,
});

export const SuspiciousStatus = Object.freeze({
    // 待审核
    REVIEW: 10,
    // 已审核
    FINISHED: 20,
});

export const DestroyApplyStatus = Object.freeze({
    // 销毁申请-待审核
    REVIEW: 10,
    // 已驳回
    REFUSE: 20,
    // 待销毁
    WAIT_DONE: 30,
    // 已销毁
    FINISHED: 40,
});

export const DestroyApplyStatusName = Object.freeze({
    [DestroyApplyStatus.REVIEW]: '待审核',
    [DestroyApplyStatus.WAIT_DONE]: '待销毁',
    [DestroyApplyStatus.FINISHED]: '已销毁',
    [DestroyApplyStatus.REFUSE]: '已驳回',
});

export const ReturnGoodsStatusName = Object.freeze({
    [ReturnGoodsStatus.REVIEW]: '待审核',
    [ReturnGoodsStatus.FINISHED]: '已完成',
    [ReturnGoodsStatus.REFUSE]: '已驳回',
});

export const ConserveStatusName = Object.freeze({
    [ConserveStatus.REVIEW]: '待养护',
    [ConserveStatus.FINISHED]: '已养护',
});

export const SuspiciousStatusName = Object.freeze({
    [SuspiciousStatus.REVIEW]: '待审核',
    [SuspiciousStatus.FINISHED]: '已审核',
});

export const ReturnGoodsStatusTagTheme = Object.freeze({
    [ReturnGoodsStatus.REVIEW]: 'primary',
    [ReturnGoodsStatus.FINISHED]: 'success',
    [ReturnGoodsStatus.REFUSE]: 'danger',
});

// 批量修改字段key
export const BatchModifyEnum = Object.freeze({
    // 0x01 改二级分类 0x02 改利润率 0x04 改养护类型 0x08改oct 0x10改存储条件 0x20改经营范围 0x40改基药 0x80改自定义二级类型
    CLASSIFICATION: 0x01,
    PROFIT: 0x02,
    CONSERVE: 0x04,
    OCT: 0x08,
    STORAGE: 0x10,
    BUSINESS_SCOPE: 0x20,
    BASE_MEDICINE: 0x40,
    CUSTOM_TYPE: 0x80,
    GOODS_TAG: 0x100,
    TYPE_ID: 0x200,
});

// 库存列表视图模式
export const InventoryViewMode = Object.freeze({
    // 普通
    NORMAL: 'normal',
    // 批量修改
    BATCH: 'batchUpdateArchives',
    // 批量打印药签
    BATCH_PRINT_PRICE_TAG: 'batchPrintPriceTag',
});

// 总部集采委配审批开关
export const BIT_FLAG_PURCHASE_APPLY_CHAIN_ENTRUST_DELIVERY = 0x02;
// 门店自采审批开关
export const BIT_FLAG_PURCHASE_APPLY_CLINIC_SELF = 0x04;
// 为1表示不支持进价加成
export const CHAIN_NOT_SUPPORT_PRICE_MAKE_UP_MODE = 0x08;
// 采购结算审批开关
export const BIT_FLAG_SETTLEMENT_ORDER_AUDITING = 0x10;
// 入库单批号必填
export const BIT_FLAG_STOCK_IN_BATCH_NO_REQUIRED = 0x20;
// 入库单进价不能为0
export const BIT_FLAG_STOCK_IN_COST_PRICE_NOT_ZERO = 0x40;
// 售价能为0
export const BIT_FLAG_SELL_PRICE_NOT_ZERO = 0x80;

export const PHARMACY_GOODS_IN_ORDER_TYPE = Object.freeze({
    GOODS_IN: 0, // 采购入库
    INIT_GOODS_IN: 20, // 初始化入库
    RETURN_OUT: 10, // 采购退货
    INIT_RETURN_OUT: 30, // 初始化退货
});
