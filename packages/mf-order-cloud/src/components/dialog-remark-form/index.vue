<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        size="small"
        title="批量添加备注"
        @close="$emit('close')"
    >
        <abc-layout class="dialog-content clearfix">
            <abc-form
                ref="abcForm"
                label-width="72"
                item-block
                label-position="left"
            >
                <abc-form-item label="已选订单">
                    <div style="font-size: 14px;">
                        {{ orderList.length }}
                    </div>
                </abc-form-item>

                <abc-form-item label="添加备注">
                    <abc-edit-div v-model="curRemark" :width="334" :maxlength="100"></abc-edit-div>
                </abc-form-item>
                <div v-if="hasRemarkList.length" class="warn-tips" style="color: var(--abc-color-Y2);">
                    有{{ hasRemarkList.length }}个订单已有备注，新备注将替换已有备注
                </div>
            </abc-form>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="btnLoading" @click="handleClickConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import ECOrderAPI from '@/api/order.js';

    export default {
        name: 'DialogRemarkForm',
        components: {
        },
        props: {
            orderList: {
                type: Array,
            },
            onRefresh: Function,
        },
        data() {
            return {
                visible: false,
                closed: false,
                btnLoading: false,
                curRemark: '',
            };
        },
        computed: {
            hasRemarkList() {
                return this.orderList.filter((it) => it.remark);
            },
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },

        methods: {
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            handleClickConfirm() {
                this.submitHandler();
            },
            async submitHandler() {
                try {
                    this.btnLoading = true;
                    await ECOrderAPI.updateOrderRemark({
                        orderIds: this.orderList.map((it) => it.id),
                        note: this.curRemark,
                    });
                    this.$Toast({
                        type: 'success',
                        message: '添加备注成功',
                    });
                    this.onRefresh && this.onRefresh();
                    this.closed = true;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
        },
    };
</script>

