<template>
    <abc-dialog
        title="家庭医生签约"
        class="crm-module__package-family-doctor__dialog-sign-in"
        append-to-body
        :value="true"
        size="medium"
        content-styles="min-height: 508px;overflow-x:hidden;"
        :shadow="visibleSigning"
        @input="val => $emit('input' , val)"
    >
        <div v-abc-loading="loading" class="section">
            <crm-box-item
                v-if="formData"
                style="width: 416px; margin-bottom: 16px;"
                :patient="formData"
                :is-can-see-patient-mobile="true"
            ></crm-box-item>
            <abc-divider variant="dashed"></abc-divider>
            <abc-form
                v-if="!!formData"
                ref="formData"
                label-position="left"
                :label-width="80"
                item-block
            >
                <abc-form-item label="家庭住址" class="label-flex-start">
                    <abc-address-selector
                        v-model="formData.address"
                        :width="336"
                    ></abc-address-selector>
                    <abc-input
                        v-model.trim="formData.address.addressDetail"
                        placeholder="详细地址"
                        :width="336"
                        type="text"
                    ></abc-input>
                </abc-form-item>
                <abc-form-item label="服务包">
                    <abc-select
                        v-model="formData.servicePackId"
                        :width="336"
                        placeholder="请选择服务包"
                        :disabled="isFixedServicePack"
                        @change="onChangeServicePack"
                    >
                        <abc-option
                            v-for="item in servicePackageOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
                <abc-form-item v-if="visibleBuyTheNumber" label="服务期限">
                    <abc-select
                        v-model="formData.buyTheNumber"
                        :width="336"
                        :disabled="isFixedBuyTheNumber"
                    >
                        <abc-option
                            v-for="item in buyTheNumberOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
                <abc-form-item label="签约医生">
                    <div class="item-member">
                        <abc-select
                            v-model="formData.doctorId"
                            :width="120"
                            with-search
                            :fetch-suggestions="(key) => doctorKey = key"
                            @change="onChangeDoctor"
                        >
                            <abc-option
                                v-for="item in doctorList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></abc-option>
                        </abc-select>
                        <item-auto-mobile
                            v-model="formData.doctorMobile"
                            :auto-item="currentDoctor"
                        ></item-auto-mobile>
                    </div>
                </abc-form-item>
                <abc-form-item label="团队成员" class="label-flex-start">
                    <div
                        v-for="(member, index) in formData.doctorTeam"
                        :key="index"
                        class="item-member"
                        :class="{ 'margintop': index !== 0 }"
                    >
                        <abc-select
                            v-model="member.doctorId"
                            :width="120"
                            with-search
                            :fetch-suggestions="(key) => member.doctorKey = key"
                            @change="onChangeTeamMember(member, index)"
                        >
                            <abc-option
                                v-for="item in handleFilterDoctor(member.employeeOptions, member.doctorKey)"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></abc-option>
                        </abc-select>
                        <item-auto-mobile
                            v-model="member.doctorMobile"
                            :auto-item="member.autoItem"
                        ></item-auto-mobile>
                        <div class="close-btn">
                            <abc-delete-icon variant="fill" size="small" @delete="onClickDeleteMember(index)"></abc-delete-icon>
                        </div>
                    </div>
                    <div v-if="visibleAddBtn" class="btn-add-member">
                        <abc-button type="text" @click="onClickAddMember">
                            添加
                        </abc-button>
                    </div>
                </abc-form-item>
                <abc-form-item label="费用合计">
                    <span class="money">{{ totalPriceWording }}</span>
                </abc-form-item>
                <abc-form-item label="权益内容" class="item-content-text label-flex-start">
                    <span v-for="(text, index) in contentTextList.slice(0, 2)" :key="index">
                        {{ `${index + 1}.${text}` }}
                    </span>
                    <span v-if="contentTextList.length > 2">3.......</span>
                </abc-form-item>
            </abc-form>
        </div>
        <abc-flex slot="footer" align="center" class="dialog-footer">
            <abc-button
                type="blank"
                :disabled="isDisabledPreviewProtocolBtn"
                @click="onClickPreviewProtocol"
            >
                协议预览
            </abc-button>
            <div class="stance"></div>
            <abc-button
                v-if="isEditing"
                :disabled="disabledEditBtn"
                :loading="loadingSubmit"
                @click="onClickEdit"
            >
                保存
            </abc-button>
            <abc-button
                v-else
                :disabled="disabledSignBtn"
                :loading="loadingSubmit"
                @click="onClickSignIn"
            >
                发起签约
            </abc-button>
            <abc-button variant="ghost" @click="$emit('input' , false)">
                取消
            </abc-button>
        </abc-flex>

        <view-sign
            slot="shadow"
            v-model="visibleSigning"
            :sign-result="signResult"
            @success="$emit('success')"
        ></view-sign>
    </abc-dialog>
</template>

<script>
    import CrmAPI from 'api/crm';
    import { mapGetters } from 'vuex';
    import {
        isNotNull, formatAge,
    } from 'utils/index';
    import { formatMoney } from 'src/filters/index';
    import { packStateConst } from './constants';
    import { isEqual } from 'utils/lodash';
    import { getServiceTypeWording } from './utils';
    import ItemAutoMobile from './item-auto-mobile.vue';
    import ViewSign from './view-sign.vue';
    import DialogAgreementComponent from './dialog-agreement.js';
    import CrmBoxItem from 'views/crm/common/package-member/crm-box-item.vue';

    export default {
        name: 'DialogSignIn',
        components: {
            CrmBoxItem,
            ItemAutoMobile,
            ViewSign,
        },
        props: {
            // 患者id，当没有患者信息时，传入id可获取患者信息
            patientId: {
                type: String,
                default: '',
            },
            // 是否编辑
            isEditSign: {
                type: Boolean,
                default: false,
            },
            // 是否续签
            isRenewalSign: {
                type: Boolean,
                default: false,
            },
            // 是否重签
            isAgainSign: {
                type: Boolean,
                default: false,
            },
            // 默认选中服务包id
            defaultServicePackId: {
                type: String,
                default: '',
            },
            // 家庭医生服务信息
            familyDoctorInfo: {
                type: Object,
                default: null,
            },
            // 服务包列表
            servicePackageList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                loading: false,
                patientBasicInfo: null,
                employeeList: [],//成员列表


                formData: null,
                doctorKey: '',
                visibleSigning: false,
                signResult: null,
                loadingSubmit: false,
                visibleBuyTheNumber: true,
            };
        },
        computed: {
            ...mapGetters([ 'userInfo', 'currentClinic', 'fullEmployeeList' ]),
            // 是否编辑状态
            isEditing() {
                return this.isEditSign && !!this.familyDoctorInfo;
            },
            // 家庭医生id - 修改时出现
            familyDoctorId() {
                const { id } = this.familyDoctorInfo || {};
                return id || '';
            },
            // 是否固定服务包，不可切换
            isFixedServicePack() {
                return this.isRenewalSign || this.isEditing;
            },
            // 是否固定服务期限，不可切换
            isFixedBuyTheNumber() {
                return this.isEditing;
            },
            // 可选服务包列表
            canCheckedservicePackage() {
                let options = [];
                if (this.isEditing) {
                    const { familyDoctorConfig } = this.familyDoctorInfo || {};
                    familyDoctorConfig && options.push(familyDoctorConfig);
                } else {
                    options = this.servicePackageList.filter((item) => item.serviceState === packStateConst.ENABLED);
                }
                return options;
            },
            // 可选服务包
            servicePackageOptions() {
                return this.canCheckedservicePackage.map((item) => ({
                    value: item.id,
                    label: `${item.servicePackName}（${formatMoney(item.unitPrice)}/${getServiceTypeWording(item.serviceType, '年')}）`,
                }));
            },
            // 当前选中服务包
            checkedServicePackage() {
                const { servicePackId } = this.formData || {};
                const target = this.canCheckedservicePackage.find((item) => item.id === servicePackId);
                return target || null;
            },
            // 当前选中服务包类型描述
            checkedServicePackageTypeWording() {
                const { serviceType } = this.checkedServicePackage || {};
                return getServiceTypeWording(serviceType, '年');
            },
            // 可选期限，1-12
            buyTheNumberOptions() {
                const options = [];
                for (let index = 1; index <= 12; index++) {
                    options.push({
                        value: index,
                        label: `${index}${this.checkedServicePackageTypeWording}`,
                    });
                }
                return options;
            },
            // 医生列表
            doctorList() {
                const list = this.employeeList.filter((item) => {
                    return item.roles && (item.roles.split(/[,，]/).includes('医生') || item.roles.split(/[,，]/).includes('门诊医生') || item.roles.split(/[,，]/).includes('住院医生'));
                });
                return this.handleFilterDoctor(list, this.doctorKey);
            },
            // 当前选择医生
            currentDoctor() {
                const { doctorId } = this.formData || {};
                return this.doctorList.find((item) => item.id === doctorId);
            },
            // 是否可新增成员
            visibleAddBtn() {
                const max = 3;
                const { doctorTeam = [] } = this.formData || {};
                return doctorTeam.length < max;
            },
            // 费用合计
            totalPriceWording() {
                let totalPriceWording = 0;
                if (this.formData && this.checkedServicePackage) {
                    const { buyTheNumber } = this.formData;
                    const { unitPrice } = this.checkedServicePackage;
                    totalPriceWording = buyTheNumber * unitPrice;
                }
                const { receivableFee } = this.familyDoctorInfo || {};

                return `${this.$t('currencySymbol')} ${formatMoney(this.isEditing ? receivableFee : totalPriceWording)}`;
            },
            // 权益内容
            contentTextList() {
                const { servicePackDetails } = this.checkedServicePackage || {};
                return servicePackDetails || [];
            },
            // 是否可以点击预览协议按钮
            isDisabledPreviewProtocolBtn() {
                const { servicePackId } = this.formData || {};
                return !servicePackId;
            },
            // 是否禁用签约按钮
            disabledSignBtn() {
                const {
                    doctorId,//签约医师id
                    servicePackId,//服务包id
                } = this.formData || {};
                return !doctorId || !servicePackId;
            },
            // 是否禁用编辑按钮
            disabledEditBtn() {
                return this.disabledSignBtn || isEqual(this.formData, this.createFormData());
            },
        },
        watch: {
            visibleSigning(newValue) {
                !newValue && this.$emit('input', false);
            },
        },
        mounted () {
            this.init();
        },
        methods: {
            /**
             * 初始化工作
             * 1、获取患者基本信息
             * 2、获取诊所下成员列表
             * 3、获取家庭医生服务包
             * 4、创建formData，做表单数据绑定
             * <AUTHOR>
             * @date 2021-05-20
             */
            async init() {
                this.loading = true;

                // 获取患者基本信息
                if (this.patientId) {
                    const patientInfo = await this.fetchPatientInfo();
                    this.patientBasicInfo = patientInfo || [];
                }

                // 获取诊所成员列表
                const employeeList = await this.fetchEmployeeList();
                this.employeeList = employeeList || [];

                // 创建formData
                this.formData = this.createFormData();

                this.loading = false;
            },
            /**
             * 拉取患者详情
             * <AUTHOR>
             * @date 2021-05-20
             * @returns {Object|undefined}
             */
            async fetchPatientInfo() {
                try {
                    const params = {
                        wx: 1,
                        chronicArchives: 1,
                    };
                    const { data } = await CrmAPI.fetchPatientOverview(this.patientId, params);
                    return data;
                } catch (error) {
                    console.log('fetchPatientInfo error', error);
                }
            },
            /**
             * 拉取门店下成员列表
             * <AUTHOR>
             * @date 2020-10-16
             * @returns {Object|undefined}
             */
            fetchEmployeeList() {
                const employeeList = this.fullEmployeeList;
                return employeeList.map((item) => ({
                    id: item.employeeId,
                    name: item.employeeName,
                    mobile: item.employeeMobile,
                    roles: item.roles,
                    namePy: item.employeeNamePy,
                    namePyFirst: item.employeeNamePyFirst,
                }));
            },
            /**
             * 创建表单数据
             * <AUTHOR>
             * @date 2021-05-20
             * @returns {Object}
             */
            createFormData() {
                const formData = {
                    name: '',//姓名
                    sex: '',//性别
                    age: '',//年龄
                    mobile: '',//手机号
                    // 地址
                    address: {
                        addressCityId: '',
                        addressCityName: '',
                        addressProvinceId: '',
                        addressProvinceName: '',
                        addressDistrictId: '',
                        addressDistrictName: '',
                        addressDetail: '',//详细地址
                    },
                    servicePackId: '',//服务包id
                    buyTheNumber: 1,//期限，默认 1

                    doctorId: '',//签约医生
                    doctorName: '',//签约医生姓名
                    doctorMobile: '',//签约医生联系方式
                    doctorTeam: [],//其他成员
                };

                if (this.patientBasicInfo) {
                    const {
                        name,//姓名
                        sex,//性别
                        age,//年龄
                        mobile,//手机号
                        address,
                    } = this.patientBasicInfo;
                    formData.name = isNotNull(name) ? name : '';
                    formData.sex = isNotNull(sex) ? sex : '';
                    formData.age = formatAge(age, {
                        monthYear: 150, dayYear: 1, defaultValue: '',
                    });
                    formData.mobile = isNotNull(mobile) ? mobile : '';
                    if (address) {
                        Object.keys(formData.address).forEach((key) => {
                            formData.address[key] = isNotNull(address[key]) ? address[key] : '';
                        });
                    }
                }

                if (this.isEditing) {
                    // 编辑状态
                    const {
                        addressCityId,
                        addressCityName,
                        addressProvinceId,
                        addressProvinceName,
                        addressDistrictId,
                        addressDistrictName,
                        addressDetail,
                        servicePackId,//服务包id
                        buyTheNumber,//期限
                        doctorId,//签约医生
                        doctorName,//签约医生姓名
                        doctorMobile,//签约医生联系方式
                        doctorTeam,//其他成员
                    } = this.familyDoctorInfo;
                    formData.address.addressCityId = addressCityId;
                    formData.address.addressCityName = addressCityName;
                    formData.address.addressProvinceId = addressProvinceId;
                    formData.address.addressProvinceName = addressProvinceName;
                    formData.address.addressDistrictId = addressDistrictId;
                    formData.address.addressDistrictName = addressDistrictName;
                    formData.address.addressDetail = addressDetail;

                    formData.servicePackId = servicePackId;//服务包id
                    formData.buyTheNumber = buyTheNumber;//期限
                    formData.doctorId = doctorId;//签约医生
                    formData.doctorName = doctorName;//签约医生姓名
                    formData.doctorMobile = doctorMobile || '';//签约医生联系方式
                    //其他成员
                    formData.doctorTeam = (doctorTeam || []).map((item) => {
                        const target = this.employeeList.find((one) => one.id === item.doctorId);
                        const {
                            name = '', mobile = '',
                        } = target || {};
                        const itemInfo = {
                            doctorId: item.doctorId,//医生id
                            doctorName: item.doctorName,//医生姓名
                            doctorMobile: item.doctorMobile,//医生手机号
                            doctorKey: '',
                            employeeOptions: this.getEmployeeOptions(item.doctorId),
                            autoItem: {
                                name, mobile,
                            },
                        };
                        return itemInfo;
                    });
                } else {
                    // 新增状态
                    const defaultServicePack = this.servicePackageOptions.find((item) => item.value === this.defaultServicePackId);
                    if (defaultServicePack) {
                        formData.servicePackId = defaultServicePack.value;
                    }

                    const defaultDoctor = this.doctorList.find((item) => item.id === this.userInfo.id);
                    if (defaultDoctor) {
                        // 如果自己是医生，默认自己
                        formData.doctorId = defaultDoctor.id;
                        formData.doctorName = defaultDoctor.name;
                        formData.doctorMobile = ''; // 默认必填，聚焦时提示
                    }
                }

                return formData;
            },
            /**
             * 关键词过滤
             * <AUTHOR>
             * @date 2021-06-08
             * @param {Array} doctorList 医生列表
             * @param {String} key 关键词
             * @returns {Array}
             */
            handleFilterDoctor(doctorList, key) {
                const _key = key.trim().toLocaleLowerCase();
                if (!_key) {
                    return doctorList;
                }
                return doctorList.filter((item) => (
                    (item.name && item.name.indexOf(_key) > -1) ||
                    (item.mobile && item.mobile.indexOf(_key) > -1) ||
                    (item.namePy && item.namePy.indexOf(_key) > -1) ||
                    (item.namePyFirst && item.namePyFirst.toLocaleLowerCase().indexOf(_key) > -1)
                ));
            },
            /**
             * 当添加成员
             * <AUTHOR>
             * @date 2021-05-20
             */
            onClickAddMember() {
                this.formData.doctorTeam.push({
                    doctorId: '',
                    doctorName: '',
                    doctorMobile: '',
                    doctorKey: '',
                    employeeOptions: this.getEmployeeOptions(),
                    autoItem: null,
                });
            },
            /**
             * 当删除指定成员
             * <AUTHOR>
             * @date 2021-05-20
             * @param {Number} index 索引
             */
            onClickDeleteMember(index) {
                this.formData.doctorTeam.splice(index, 1);
                this.updateTeamEmployeeOptions();
            },
            /**
             * 当切换医师
             * <AUTHOR>
             * @date 2021-05-21
             */
            onChangeDoctor() {
                const {
                    doctorId, doctorTeam,
                } = this.formData;
                const target = this.employeeList.find((item) => item.id === doctorId);
                this.formData.doctorName = target ? target.name : '';
                this.formData.doctorTeam = doctorTeam.filter((item) => item.doctorId !== doctorId);
                this.formData.doctorMobile = '';
                this.updateTeamEmployeeOptions();
            },
            /**
             * 当改变成员
             * <AUTHOR>
             * @date 2021-05-20
             * @param {Object} member 成员
             */
            onChangeTeamMember(member) {
                const target = this.employeeList.find((item) => item.id === member.doctorId);
                if (target) {
                    member.doctorName = target.name;
                    member.doctorMobile = '';
                    member.autoItem = {
                        name: target.name,
                        mobile: target.mobile,
                    };
                }
                this.updateTeamEmployeeOptions();
            },
            /**
             * 更新成员里面的成员可选项
             * <AUTHOR>
             * @date 2021-05-21
             */
            updateTeamEmployeeOptions() {
                const { doctorTeam = [] } = this.formData || {};
                doctorTeam.forEach((item) => {
                    item.employeeOptions = this.getEmployeeOptions(item.doctorId);
                });
            },
            /**
             * 获取成员可选选项，排除已选择（非自己）
             * <AUTHOR>
             * @date 2021-05-21
             * @param {String} excludeDoctorId 患者id
             * @returns {Array}
             */
            getEmployeeOptions(excludeDoctorId) {
                const { doctorTeam = [] } = this.formData || {};
                const doctorIds = doctorTeam
                    .filter((item) => item.doctorId && item.doctorId !== excludeDoctorId)
                    .map((item) => item.doctorId);
                const { doctorId } = this.formData || {};
                doctorId && doctorIds.push(doctorId);
                const employeeOptions = this.employeeList.filter((item) => !doctorIds.includes(item.id));
                return employeeOptions;
            },
            /**
             * 创建家庭医生信息
             * <AUTHOR>
             * @date 2021-06-07
             * @returns {Object}
             */
            createFamilyDoctorInfo() {
                const familyDoctorInfo = {
                    patientName: '',//患者姓名
                    patientMobile: '',//联系电话
                    patientSignature: '',//签字
                    signTime: '',//签字时间

                    clinicName: '',//诊所名称
                    doctorName: '',//医生姓名
                    doctorMobile: '',//医生电话
                    doctorTeam: [],//团队成员

                    buyTheNumber: '',
                    unitPrice: '',
                    receivableFee: '',
                };
                if (this.formData) {
                    const {
                        name,//姓名
                        mobile,//手机号
                        address: {
                            addressCityName,
                            addressProvinceName,
                            addressDistrictName,
                            addressDetail,//详细地址
                        },
                        buyTheNumber,//期限
                        doctorName,//签约医生姓名
                        doctorMobile,//签约医生联系方式
                        doctorTeam,//其他成员
                    } = this.formData || {};
                    familyDoctorInfo.patientName = name;
                    familyDoctorInfo.patientMobile = mobile;
                    familyDoctorInfo.clinicName = this.currentClinic.clinicName;
                    familyDoctorInfo.addressProvinceName = addressProvinceName;
                    familyDoctorInfo.addressCityName = addressCityName;
                    familyDoctorInfo.addressDistrictName = addressDistrictName;
                    familyDoctorInfo.addressDetail = addressDetail;
                    familyDoctorInfo.doctorName = doctorName;
                    familyDoctorInfo.doctorMobile = doctorMobile;
                    familyDoctorInfo.doctorTeam = doctorTeam.map((item) => ({
                        doctorName: item.doctorName,//医生姓名
                        doctorMobile: item.doctorMobile,//医生姓名
                    }));

                    const { unitPrice } = this.checkedServicePackage;
                    familyDoctorInfo.buyTheNumber = buyTheNumber;
                    familyDoctorInfo.unitPrice = unitPrice;
                    familyDoctorInfo.receivableFee = buyTheNumber * unitPrice;
                }
                if (this.isEditing) {
                    const {
                        patientSignature,//签字
                        signTime,//签字时间
                    } = this.familyDoctorInfo;
                    familyDoctorInfo.patientSignature = patientSignature || '';
                    familyDoctorInfo.signTime = signTime || '';
                }
                return familyDoctorInfo;
            },
            /**
             * 当点击预览协议
             * <AUTHOR>
             * @date 2021-05-20
             */
            onClickPreviewProtocol() {
                const { servicePackId } = this.formData || {};
                const servicePackInfo = this.canCheckedservicePackage.find((item) => item.id === servicePackId);
                servicePackInfo.clinicName = this.currentClinic && this.currentClinic.clinicName;
                if (servicePackInfo) {
                    const familyDoctorInfo = this.createFamilyDoctorInfo();
                    DialogAgreementComponent.getInstance({
                        props: {
                            servicePackInfo,
                            familyDoctorInfo,
                        },
                    });
                }
            },
            /**
             * 当切换服务包时 - 刷新视图
             * <AUTHOR>
             * @date 2021-06-03
             */
            onChangeServicePack() {
                this.visibleBuyTheNumber = false;
                this.$nextTick(() => {
                    this.visibleBuyTheNumber = true;
                });
            },
            /**
             * 当点击发起签约
             * <AUTHOR>
             * @date 2021-05-20
             */
            async onClickSignIn() {
                this.loadingSubmit = true;
                const signResult = await this.handleSubmitFamilyDoctorSignTheContract();
                if (signResult) {
                    this.$emit('success');
                    this.visibleSigning = true;
                    this.signResult = signResult;
                }
                this.loadingSubmit = false;
            },
            /**
             * 当点击保存编辑
             * <AUTHOR>
             * @date 2021-05-20
             */
            async onClickEdit() {
                this.loadingSubmit = true;
                const signResult = await this.updateFamilyDoctorInfoById();
                if (signResult) {
                    this.$emit('success');
                    this.$emit('input', false);
                }
                this.loadingSubmit = false;
            },
            /**
             * 创建家庭医生签约提交数据
             * <AUTHOR>
             * @date 2021-05-25
             * @returns {Object}
             */
            createPostData() {
                const postData = {
                    patientId: '',//签约患者id
                    addressCityId: '',
                    addressCityName: '',
                    addressDistrictId: '',
                    addressDistrictName: '',
                    addressProvinceId: '',
                    addressProvinceName: '',
                    addressGeo: '',
                    addressDetail: '',

                    patientSignature: '',//患者签名 - 传空
                    servicePackId: '',//服务包id
                    agreement: '',//患者签订协议 - 传空
                    buyTheNumber: 0, // 购买次数
                    doctorId: '',//医生id
                    doctorName: '',//医生姓名
                    doctorMobile: '',//医生联系方式
                    doctorTeam: [],
                };

                if (this.formData) {
                    const {
                        address: {
                            addressCityId,
                            addressCityName,
                            addressProvinceId,
                            addressProvinceName,
                            addressDistrictId,
                            addressDistrictName,
                            addressDetail,//详细地址
                        },
                        servicePackId,//服务包id
                        buyTheNumber,//期限
                        doctorId,//签约医生
                        doctorMobile,//签约医生联系方式
                        doctorTeam,//其他成员
                    } = this.formData;
                    postData.patientId = this.patientId;

                    postData.addressCityId = addressCityId;
                    postData.addressCityName = addressCityName;
                    postData.addressDistrictId = addressDistrictId;
                    postData.addressDistrictName = addressDistrictName;
                    postData.addressProvinceId = addressProvinceId;
                    postData.addressProvinceName = addressProvinceName;
                    postData.addressDetail = addressDetail;

                    postData.servicePackId = servicePackId;//服务包id
                    postData.buyTheNumber = buyTheNumber; // 购买次数

                    const targetDoctor = this.doctorList.find((item) => item.id === doctorId);
                    if (targetDoctor) {
                        postData.doctorId = doctorId;//医生id
                        postData.doctorName = targetDoctor.name;//医生姓名
                        postData.doctorMobile = doctorMobile;//签约医生联系方式
                        postData.doctorTeam = doctorTeam
                            .filter((item) => item.doctorId)
                            .map((item) => ({
                                doctorId: item.doctorId,//医生id
                                doctorName: item.doctorName,//医生姓名
                                doctorMobile: item.doctorMobile,//医生手机号
                            }));
                    }
                }
                return postData;
            },
            /**
             * 处理提交家庭医生签约交易
             * <AUTHOR>
             * @date 2021-05-25
             * @returns {Promise<Object|Undefined>}
             */
            async handleSubmitFamilyDoctorSignTheContract() {
                try {
                    const postData = this.createPostData();
                    const { data } = await CrmAPI.handleSubmitFamilyDoctorSignTheContract(postData);
                    return data;
                } catch (error) {
                    console.log('handleSubmitFamilyDoctorSignTheContract error', error);
                }
            },
            /**
             * 处理更新家庭医生签约交易
             * <AUTHOR>
             * @date 2021-05-25
             * @returns {Promise<Object|Undefined>}
             */
            async updateFamilyDoctorInfoById() {
                try {
                    const postData = this.createPostData();
                    const { data } = await CrmAPI.updateFamilyDoctorInfoById(this.familyDoctorId, postData);
                    return data;
                } catch (error) {
                    console.log('updateFamilyDoctorInfoById error', error);
                }
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .crm-module__package-family-doctor__dialog-sign-in {
        .abc-dialog-header {
            height: 40px;
            padding: 0 24px;
            line-height: 40px;
        }

        .abc-dialog-body {
            width: 454px;
            height: 556px;
            overflow: auto;
        }

        .section {
            .patient-info {
                .sex {
                    margin-right: 8px;
                    margin-left: 24px;
                }

                .mobile {
                    margin-left: 8px;
                }
            }

            .abc-form-item.label-flex-start {
                align-items: flex-start;

                .abc-form-item-label {
                    line-height: 32px;
                }
            }

            .address-selector {
                margin-bottom: 6px;
            }

            .item-member {
                position: relative;

                @include flex(row, flex-start, center);

                .abc-select-wrapper {
                    margin-right: 8px;
                }

                &.margintop {
                    margin-top: 8px;
                }

                & + .btn-add-member {
                    margin-top: 4px;
                }

                .close-btn {
                    @include flex(row, center, center);

                    position: absolute;
                    right: -18px;
                    display: none;
                    width: 22px;
                    height: 32px;

                    .iconfont {
                        color: #aab4bf;
                        cursor: pointer;

                        &:hover {
                            color: $T3;
                        }
                    }
                }

                &:hover .close-btn {
                    display: flex;
                }
            }

            .btn-add-member {
                height: 32px;

                @include flex(row, flex-start, center);
            }

            .money {
                font-weight: 500;
                color: #e5892d;
            }

            .item-content-text {
                margin-bottom: 0;

                .abc-form-item-content {
                    padding-top: 6px;
                    line-height: 20px;
                    color: $T2;

                    @include flex(column, flex-start, stretch);
                }
            }
        }

        .dialog-footer {
            @include flex(row, flex-start, center);

            .stance {
                flex: 1;
            }
        }
    }
</style>
