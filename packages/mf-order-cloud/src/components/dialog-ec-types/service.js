import { getTargetHost } from 'MfBase/build-env';
export default class ThirdPartyAuthService {
    constructor(options) {
        this.shortId = options.shortId;
    }

    async handleThirdAuthorize(item) {
        let authWindow = null;
        const onMessage = (message) => {
            if (message.origin.indexOf('global-') !== -1 && message.data === 'ec_auth_success') {
                this.$abcEventBus.$emit('ec-auth-success');
                this.visible = false;
                window.removeEventListener('message', onMessage);
                authWindow.close();
            }
        };

        window.addEventListener('message', onMessage);

        const thirdParties = [
            // 拼多多
            {
                mainDomain: 'https://mms.pinduoduo.com',
                authUrl: 'https://fuwu.pinduoduo.com/service-market/auth?response_type=code',
            },
        ];

        for (const thirdParty of thirdParties) {
            const redirectUri = encodeURIComponent(`https://${getTargetHost()}/ec-oauth-callback`);
            const state = encodeURIComponent(`${this.shortId}@${location.hostname}`);
            const url = `${thirdParty.authUrl}&client_id=${item.clientId}&redirect_uri=${redirectUri}&state=${state}`;

            if (window.remote?.session) {
                try {
                    await this.removeCookies(thirdParty.mainDomain);
                    await this.removeCookies(thirdParty.authUrl);
                } catch (error) {
                    console.error(error);
                }
            }

            const {
                width,
                height,
            } = this.calculateWindowSize(0.8);

            authWindow = window.open(url, '_blank', `width=${width},height=${height}`);
        }
    }

    async removeCookies(url) {
        const cookies = await window.remote.session.defaultSession.cookies.get({ url });
        for (const cookie of cookies) {
            await window.remote.session.defaultSession.cookies.remove(url, cookie.name);
        }
    }

    calculateWindowSize(factor) {
        const currentWidth = window.innerWidth;
        const currentHeight = window.innerHeight;

        return {
            width: Math.round(currentWidth * factor),
            height: Math.round(currentHeight * factor),
        };
    }
}
